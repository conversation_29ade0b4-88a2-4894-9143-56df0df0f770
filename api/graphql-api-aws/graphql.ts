/* eslint-disable prefer-arrow/prefer-arrow-functions */

import { ApolloServer } from 'apollo-server-lambda';
import bodyParser from 'body-parser';
import express, { json } from 'express';
import { createComplexityLimitRule } from 'graphql-validation-complexity';

import { typeDefs } from './src/graphql';

if (process.env.LOCAL_DEBUG === 'CINURU') {
	// eslint-disable-next-line @typescript-eslint/no-var-requires
	require('dotenv').config({ path: '.env.development' });
}

if (process.env.LOCAL_DEBUG === 'CINEPLEX') {
	// eslint-disable-next-line @typescript-eslint/no-var-requires
	require('dotenv').config({ path: '.env.development.cineplex' });
}

//@ts-ignore
import { mutationResolvers, queryResolvers, scalarResolvers } from './src/graphql/';
import { getFixedI18n } from './src/utils/i18nHelpers';
//@ts-ignore
import { ApolloServerPluginLandingPageGraphQLPlayground } from 'apollo-server-core';
import { APPLE_DEVELOPER_MERCHANT_DOMAIN_ASSOCIATION, PAYPAL_WEBHOOK_ROUTE } from './consts';
import { storeCpAccountUpdates } from './src/graphql/BonusProgramMembership/storeCpAccountUpdates';
import { createShopifyAPI } from './src/integrations/shopify/shopifyWebhookEndpoint';
import { analyticsAPI } from './src/notifications/analyticsAPI';
import { engageAPI } from './src/notifications/engageAPI';
import { createSubscriptionTokenAPI } from './src/subscriptionTokenApi/validateSubscriptionTokenAPI';
import { Context } from './src/typescript-types';
import { applyAuthMiddleware, authenticate, corsConfig } from './src/utils/auth';
import { newsletterEndpoints } from './src/utils/newsletterEndpoints';
import { paypalWebhookAPI } from './src/utils/paypalWebhookAPI';
import { activateRateLimiter } from './src/utils/rateLimiter';

// this brakes line numbers in tests
if (!process.env.JEST_WORKER_ID) {
	// eslint-disable-next-line @typescript-eslint/no-var-requires
	require('source-map-support').install();
}

export const resolvers = {
	...scalarResolvers,
	Query: queryResolvers,
	Mutation: mutationResolvers,
	ContentSection: {
		__resolveType: (obj: { type: string }) => {
			return obj.type;
		},
	},
};

const context = async ({ express }): Promise<Context> => {
	const req = express.req;
	const res = express.res;
	// Please dont remove this comment as we sometimes want to log the incoming queries
	// console.log(req.body.query);
	// console.log(req.body.variables);
	const viewer = authenticate(req);
	const appLink = req.header('appLink');
	const clientVersion = req.header('version');
	const clientSession = req.header('session');
	const requestId = req.header('requestId');

	const i18n = getFixedI18n(viewer.language);
	return {
		viewer,
		httpReq: req,
		httpRes: res,
		i18n,
		language: i18n.language,
		brand: req.header('brand'),
		appLink,
		clientVersion,
		clientSession,
		requestId,
	};
};

const apollo = new ApolloServer({
	typeDefs,
	resolvers,
	introspection: true,
	context,
	plugins:
		process.env.NODE_ENV === 'production' ? [] : [ApolloServerPluginLandingPageGraphQLPlayground()],
	validationRules: [
		createComplexityLimitRule(5000, {
			// TODO: Lower this number, once we know what exactly are realistic query costs
			scalarCost: 0,
			objectCost: 1,
			listFactor: 5,
			onCost: (cost) => {
				console.log('query cost:', cost);
			},
			formatErrorMessage: (cost) => `query with cost ${cost} exceeds complexity limit`,
		}),
	],
});

export const graphqlHandler = apollo.createHandler({
	expressAppFromMiddleware(middleware) {
		const app = express();
		createShopifyAPI(app);
		app.use(json({ limit: '20mb' }));
		activateRateLimiter(app);
		newsletterEndpoints(app);
		createSubscriptionTokenAPI(app);

		app.post('*/cpaccountupdates', bodyParser.json(), storeCpAccountUpdates);
		app.post('*/engage', bodyParser.json(), engageAPI);
		app.post('*/analytics', bodyParser.json(), analyticsAPI);
		app.post('*/appleauth', bodyParser.urlencoded({ extended: false }), (req, res) => {
			const {
				state: hackedState,
				code: authorizationCode,
				id_token: identityToken,
				user,
			} = req.body;
			const [origin, state] = hackedState.split('?');
			const { name: fullName, email } = user
				? JSON.parse(user)
				: { email: undefined, name: undefined };
			const result = encodeURIComponent(
				JSON.stringify({ identityToken, authorizationCode, email, fullName, state })
			);
			if (req.method === 'POST') {
				res.writeHead(302, { Location: `${origin}?result=${result}` });
				res.end();
			} else {
				res.status(404).send('expected POST request with form data');
			}
		});

		app.post(`*/${PAYPAL_WEBHOOK_ROUTE}`, bodyParser.json(), paypalWebhookAPI);

		app.get('*/.well-known/apple-developer-merchantid-domain-association.txt', (req, res) => {
			res.status(200).send(APPLE_DEVELOPER_MERCHANT_DOMAIN_ASSOCIATION);
		});
		app.set('trust proxy', 1);

		app.get('*/204', (req, res) => {
			console.log('204');
			res.status(204).send();
		});
		applyAuthMiddleware(app);

		app.use(middleware);
		return app;
	},
	expressGetMiddlewareOptions: {
		cors: corsConfig,
	},
});
