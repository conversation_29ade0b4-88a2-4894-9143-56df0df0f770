service: graphql-api
provider:
  name: aws
  runtime: nodejs18.x
  timeout: 30
  environment:
    STAGE: ${sls:stage}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
      Resource: '*'
params:
  default:
    vpc:
      securityGroupIds: []
      subnetIds: []
    notificationschedule: cron(0 12 * * ? *)
    notificationscheduleEnabled: false
    syncSchedule: cron(0 12 * * ? *)
    syncScheduleEnabled: false
    sharcmailschedule: cron(0 12 * * ? *)
    sharcmailscheduleEnabled: false
    monitoringschedule: cron(0 12 * * ? *)
    monitoringscheduleEnabled: false
    subscribeToCpUpdatesSchedule: cron(0 12 * * ? *)
    subscribeToCpUpdatesScheduleEnabled: false
    cineplexBgTasksAndNotifications: cron(0 12 * * ? *)
    cineplexBgTasksAndNotificationsEnabled: false
    cleanupdatabaseschedule: cron(0 12 * * ? *)
    cleanupdatabasescheduleEnabled: false
    confirmTicketsAndBookBonusPoints: cron(0 12 * * ? *)
    confirmTicketsAndBookBonusPointsEnabled: false
    createAnalyticsEvents: cron(0 12 * * ? *)
    createAnalyticsEventsEnabled: false
    engageCampaignSender: cron(0 12 * * ? *)
    engageCampaignSenderEnabled: false
    computeCineplexAchievements: cron(0 12 * * ? *)
    computeCineplexAchievementsEnabled: false
    sendRecommenderEmails: cron(0 12 * * ? *)
    sendRecommenderEmailsEnabled: false
    dailyMaintanance: cron(0 12 * * ? *)
    dailyMaintananceEnabled: false
    subscribeToPaypalEvents: cron(0 12 * * ? *)
    subscribeToPaypalEventsEnabled: false
    refreshRedisCache: cron(0 12 * * ? *)
    refreshRedisCacheEnabled: false
    validateCompesoVoucherCodes: cron(0 12 * * ? *)
    validateCompesoVoucherCodesEnabled: false
    cinfinityRegularTasks: cron(0 12 * * ? *)
    cinfinityRegularTasksEnabled: false
    cancelPaypalSubscriptions: cron(0 12 * * ? *)
    cancelPaypalSubscriptionsEnabled: false
    processShopify: cron(0 12 * * ? *)
    processShopifyEnabled: false
    checkScannedStatus: cron(0 12 * * ? *)
    checkScannedStatusEnabled: false
  cinuruStagingDev:
  cinuruStagingProd:
    vpc:
      securityGroupIds:
        - sg-a77a77cd
      subnetIds:
        - subnet-83bd3ace
  cinuruRelease:
    vpc:
      securityGroupIds:
        - sg-a77a77cd
      subnetIds:
        - subnet-83bd3ace
    notificationschedule: cron(0/15 * * * ? *)
    notificationscheduleEnabled: true
    syncSchedule: cron(3/10 * * * ? *)
    syncScheduleEnabled: true
    sharcmailschedule: cron(0 08 ? * TUE *)
    sharcmailscheduleEnabled: true
    monitoringschedule: cron(7/15 08-20 * * ? *)
    monitoringscheduleEnabled: true
    cleanupdatabaseschedule: cron(0 04 * * ? *)
    cleanupdatabasescheduleEnabled: true
    confirmTicketsAndBookBonusPoints: cron(0/15 06-23 * * ? *)
    confirmTicketsAndBookBonusPointsEnabled: true
    createAnalyticsEvents: cron(30 04 * * ? *)
    createAnalyticsEventsEnabled: true
  cineplexStagingDev:
    vpc:
      securityGroupIds:
        - sg-a77a77cd
      subnetIds:
        - subnet-83bd3ace
    subscribeToCpUpdatesSchedule: cron(0 06 * * ? *)
    subscribeToCpUpdatesScheduleEnabled: true
    engageCampaignSender: cron(* 07-18 * * ? *)
    engageCampaignSenderEnabled: true
    computeCineplexAchievements: cron(50 08-14 * * ? *)
    computeCineplexAchievementsEnabled: true
  cineplexRelease:
    provisionedConcurrency: 5
    vpc:
      securityGroupIds:
        - sg-a77a77cd
      subnetIds:
        - subnet-83bd3ace
    subscribeToCpUpdatesSchedule: cron(0 06 * * ? *)
    subscribeToCpUpdatesScheduleEnabled: true
    cineplexBgTasksAndNotifications: cron(0/20 08-16 * * ? *)
    cineplexBgTasksAndNotificationsEnabled: true
    computeCineplexAchievements: cron(50 08-20 * * ? *)
    computeCineplexAchievementsEnabled: true
    engageCampaignSender: cron(* 08-18 * * ? *)
    engageCampaignSenderEnabled: true
    createAnalyticsEvents: cron(30 04 * * ? *)
    createAnalyticsEventsEnabled: true
    dailyMaintanance: cron(30 00 * * ? *)
    dailyMaintananceEnabled: true
    refreshRedisCache: cron(2/10 * * * ? *)
    refreshRedisCacheEnabled: true
  cinfinityStaging:
    cinfinityRegularTasks: cron(*/2 * * * ? *)
    cinfinityRegularTasksEnabled: true
    cancelPaypalSubscriptions: cron(0 12 * * ? *)
    cancelPaypalSubscriptionsEnabled: true
  cinfinityRelease:
    vpc:
      securityGroupIds:
        - sg-a77a77cd
      subnetIds:
        - subnet-83bd3ace
    cinfinityRegularTasks: cron(*/2 * * * ? *)
    cinfinityRegularTasksEnabled: true
    cancelPaypalSubscriptions: cron(0 12 * * ? *)
    cancelPaypalSubscriptionsEnabled: true
    processShopify: cron(*/1 * * * ? *)
    processShopifyEnabled: true
    checkScannedStatus: cron(*/1 * * * ? *)
    checkScannedStatusEnabled: true
functions:
  graphql:
    handler: graphql.graphqlHandler
    vpc: ${param:vpc}
    events:
      - http:
          path: /
          method: post
      - http:
          path: /
          method: options
      - http:
          path: /
          method: get
      - http:
          path: /204
          method: get
      - http:
          path: /204
          method: head
      - http:
          path: /appleauth
          method: post
      - http:
          path: /cpaccountupdates
          method: post
      - http:
          path: /engage
          method: post
      - http:
          path: /analytics
          method: post
      - http:
          path: /paypal-webhook
          method: post
      - http:
          path: /subscription-ticket-voucher/validate
          method: post
      - http:
          path: /subscription-ticket-voucher/redeem
          method: post
      - http:
          path: /subscription-ticket-voucher/cancel
          method: post
      - http:
          path: /shopify-webhook
          method: post
      - http:
          path: /.well-known/apple-developer-merchantid-domain-association.txt
          method: get
      - http:
          path: /{proxy+}
          method: get

    #provisionedConcurrency: ${param:provisionedConcurrency}
  notifications:
    handler: src/notifications/index.notificationHandler
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 20000
    events:
      - schedule:
          rate: ${param:notificationschedule}
          enabled: ${param:notificationscheduleEnabled}
    timeout: 600
  syncAllPOS:
    handler: src/pos/pos-facade/syncAllPOS.syncAllPOSHandler
    vpc: ${param:vpc}
    timeout: 600
    events:
      - schedule:
          rate: ${param:syncSchedule}
          enabled: ${param:syncScheduleEnabled}
  missingSharcData:
    handler: src/backendLambdas/missingSharcData.run
    vpc: ${param:vpc}
    timeout: 600
    events:
      - schedule:
          rate: ${param:sharcmailschedule}
          enabled: ${param:sharcmailscheduleEnabled}
  cleanupDatabase:
    handler: src/backendLambdas/cleanupDatabase.run
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 600000
    timeout: 600
    events:
      - schedule:
          rate: ${param:cleanupdatabaseschedule}
          enabled: ${param:cleanupdatabasescheduleEnabled}
  monitoring:
    handler: src/monitoring/handler.checkMonitoringScripts
    vpc: ${param:vpc}
    timeout: 600
    environment:
      QUERY_TIMEOUT: 60000
    events:
      - schedule:
          rate: ${param:monitoringschedule}
          enabled: ${param:monitoringscheduleEnabled}
  subscribeToCpUpdates:
    handler: src/backendLambdas/subscribeToCineplexUpdates.run
    vpc: ${param:vpc}
    events:
      - schedule:
          rate: ${param:subscribeToCpUpdatesSchedule}
          enabled: ${param:subscribeToCpUpdatesScheduleEnabled}
  refreshRedisCache:
    handler: src/backendLambdas/refreshRedisCache.run
    vpc: ${param:vpc}
    events:
      - schedule:
          rate: ${param:refreshRedisCache}
          enabled: ${param:refreshRedisCacheEnabled}
  sendNotificationsManual:
    handler: src/backendLambdas/sendNotificationsManual.run
    vpc: ${param:vpc}
    timeout: 900
  cineplexBgTasksAndNotifications:
    handler: src/backendLambdas/cineplexBgTasksAndNotifications.run
    vpc: ${param:vpc}
    timeout: 900
    environment:
      QUERY_TIMEOUT: 180000
    events:
      - schedule:
          rate: ${param:cineplexBgTasksAndNotifications}
          enabled: ${param:cineplexBgTasksAndNotificationsEnabled}
  engageCampaignSender:
    handler: src/notifications/engageCampaignSender.sendEngageCampaigns
    vpc: ${param:vpc}
    timeout: 170
    environment:
      QUERY_TIMEOUT: 20000
    events:
      - schedule:
          rate: ${param:engageCampaignSender}
          enabled: ${param:engageCampaignSenderEnabled}
  computeCineplexAchievements:
    handler: src/backendLambdas/computeCineplexAchievements.run
    vpc: ${param:vpc}
    timeout: 600
    environment:
      QUERY_TIMEOUT: 180000
    events:
      - schedule:
          rate: ${param:computeCineplexAchievements}
          enabled: ${param:computeCineplexAchievementsEnabled}
  confirmTicketsAndBookBonusPoints:
    handler: src/backendLambdas/confirmTicketsAndBookBonusPoints/handler.run
    vpc: ${param:vpc}
    timeout: 600
    environment:
      QUERY_TIMEOUT: 60000
    events:
      - schedule:
          rate: ${param:confirmTicketsAndBookBonusPoints}
          enabled: ${param:confirmTicketsAndBookBonusPointsEnabled}
  sendRecommenderEmails:
    handler: src/backendLambdas/sendRecommenderEmails.run
    vpc: ${param:vpc}
    timeout: 600
    environment:
      QUERY_TIMEOUT: 60000
    events:
      - schedule:
          rate: ${param:sendRecommenderEmails}
          enabled: ${param:sendRecommenderEmailsEnabled}
  handleBouncesAndComplaints:
    handler: src/backendLambdas/handleBouncesAndComplaints.run
    vpc: ${param:vpc}
  testInvoices:
    handler: src/backendLambdas/testInvoices.run
    vpc: ${param:vpc}
    timeout: 15
  testLambda:
    handler: src/backendLambdas/testlambda.run
    vpc: ${param:vpc}
    timeout: 15
  createAnalyticsEvents:
    handler: src/backendLambdas/createAnalyticsEvents.run
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 900000
    timeout: 900
    events:
      - schedule:
          rate: ${param:createAnalyticsEvents}
          enabled: ${param:createAnalyticsEventsEnabled}
  dailyMaintanance:
    handler: src/backendLambdas/dailyMaintanance.run
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 900000
    timeout: 900
    events:
      - schedule:
          rate: ${param:dailyMaintanance}
          enabled: ${param:dailyMaintananceEnabled}
  subscribeToPaypalEvents:
    handler: src/backendLambdas/subscribeToPaypalEvents.run
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 900000
    timeout: 900
    events:
      - schedule:
          rate: ${param:subscribeToPaypalEvents}
          enabled: ${param:subscribeToPaypalEventsEnabled}
  translateSubscriptionTiersPaypal:
    handler: src/backendLambdas/translateSubscriptionTiersPaypal.run
    vpc: ${param:vpc}
    timeout: 900
  syncCineplexUser:
    handler: src/backendLambdas/syncCineplexUser.run
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 10000
    timeout: 120
  validateCompesoVoucherCodes:
    handler: src/backendLambdas/validateCompesoVoucherCodes.run
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 900000
    timeout: 900
    events:
      - schedule:
          rate: ${param:validateCompesoVoucherCodes}
          enabled: ${param:validateCompesoVoucherCodesEnabled}
  cinfinityRegularTasks:
    handler: src/backendLambdas/cinfinityRegularTasks.run
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 10000
    timeout: 120
    events:
      - schedule:
          rate: ${param:cinfinityRegularTasks}
          enabled: ${param:cinfinityRegularTasksEnabled}
  revalidateCompesoBookings:
    handler: src/backendLambdas/revalidateCompesoBookings.run
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 10000
    timeout: 120
  cancelPaypalSubscriptions:
    handler: src/backendLambdas/cancelPaypalSubscriptions.run
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 10000
    timeout: 120
    events:
      - schedule:
          rate: ${param:cancelPaypalSubscriptions}
          enabled: ${param:cancelPaypalSubscriptionsEnabled}
  resendPaypalWebhookEvents:
    handler: src/backendLambdas/resendPaypalWebhookEvents.run
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 10000
    timeout: 120
  processShopify:
    handler: src/integrations/shopify/processShopifyOrders.processShopifyOrders
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 10000
    timeout: 50
    events:
      - schedule:
          rate: ${param:processShopify}
          enabled: ${param:processShopifyEnabled}
  checkScannedStatus:
    handler: src/backendLambdas/checkScannedStatus.checkScannedStatus
    vpc: ${param:vpc}
    environment:
      QUERY_TIMEOUT: 10000
    timeout: 60
    events:
      - schedule:
          rate: ${param:checkScannedStatus}
          enabled: ${param:checkScannedStatusEnabled}

plugins:
  - serverless-webpack
  - serverless-dotenv-plugin
  - serverless-offline
  - serverless-prune-plugin

custom:
  prune:
    automatic: true
    number: 3
  serverless-offline:
    noPrependStageInUrl: true
    port: 3000
  webpack:
    webpackConfig: 'webpack.config.js' # Name of webpack configuration file
    includeModules:
      forceInclude:
        - encoding
    packager: 'yarn' # Packager that will be used to package your external modules

