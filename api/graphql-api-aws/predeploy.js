const { execSync } = require('child_process');
const { existsSync, writeFileSync } = require('fs');

// verify dotenv version
require('dotenv').config({ path: `./.env.${process.env.NODE_ENV}` });

const EXPECTED_VERSION = 49;
if (Number(process.env.ENV_FILE_VERSION) !== EXPECTED_VERSION) {
	throw new Error(
		`Env file version does not match, expected ${EXPECTED_VERSION}, got ${process.env.ENV_FILE_VERSION}`
	);
}
console.log('dotenv version verified');

// verify firebaseServiceAccountKeys
if (
	!existsSync('./firebaseServiceAccountKeys/cinuruServiceAccountKey.json') ||
	!existsSync('./firebaseServiceAccountKeys/cinfinityServiceAccountKey.json') ||
	!existsSync('./firebaseServiceAccountKeys/cineplexServiceAccountKey.json')
) {
	throw new Error(
		`Missing service Account keys. Please get them from /credentials folder and put them into ./firebaseServiceAccountKeys/`
	);
}
console.log('firebaseServiceAccountKeys verified');

// verify googleWalletApiServiceAccountKeys
if (!existsSync('./googleWalletApiServiceAccountKeys/cineplexServiceAccountKey.json')) {
	throw new Error(
		`Missing service Account keys. Please get them from /credentials folder and put them into ./googleWalletApiServiceAccountKeys/`
	);
}
console.log('googleWalletApiServiceAccountKeys verified');

// verify if all dependencies of linked dependencies are installed
// and adds them as bundledDependencies because of https://github.com/serverless-heaven/serverless-webpack/pull/541
const packageJson = require('./package.json');
const { dependencies } = packageJson;
packageJson.bundledDependencies = packageJson.bundledDependencies || [];
for (const name in dependencies) {
	if (dependencies[name].substring(0, 5) === 'link:') {
		const path = dependencies[name].substring(5);
		const linkedDependencies = require(`${path}/package.json`).dependencies || {};
		for (const depName in linkedDependencies) {
			const dep = `${[depName]}@${linkedDependencies[depName]}`;
			const index = packageJson.bundledDependencies.findIndex((n) => n.split('@')[0] === depName);
			if (index >= 0) {
				console.log(`updating ${dep} from ${name}`);
				packageJson.bundledDependencies[index] = dep;
			} else {
				if (dependencies[depName]) {
					if (
						linkedDependencies[depName] !== dependencies[depName] &&
						!dependencies[depName].includes('link:')
					) {
						throw new Error(
							`missmatched version for ${depName}, ${name} expected ${linkedDependencies[depName]} but ${dependencies[depName]} was declared.`
						);
					}
				} else {
					console.log(`adding ${dep} from ${name}`);
					packageJson.bundledDependencies.push(dep);
				}
			}
		}
	}
}
writeFileSync('./package.json', JSON.stringify(packageJson, null, 2));
if (packageJson.bundledDependencies.length) {
	execSync(`yarn add ${packageJson.bundledDependencies.join(' ')}`, { stdio: 'inherit' });
} else {
	execSync('yarn', { stdio: 'inherit' });
}
console.log('dependencies verified');
