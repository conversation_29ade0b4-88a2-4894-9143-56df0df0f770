directive @cost(value: Int) on FIELD_DEFINITION

directive @costFactor(value: Int) on FIELD_DEFINITION

type acceptAppTermsReturnType {
  user: User
}

enum AccessRestriction {
  PRODUCTION
  STAGING
  DEVELOPMENT
}

type Achievement {
  id: ID!
  name: String @deprecated(reason: "[5.1.16] use title instead")
  title: String
  image: String
  imageShare: String
  description: String
  receivedAt: DateTime!
}

type AddInvitedByReturnType {
  success: Boolean
}

type AgeFilter {
  active: Boolean!
  minAge: Int!
  maxAge: Int
}

input AgeFilterInput {
  active: Boolean!
  minAge: Int!
  maxAge: Int
}

type App {
  id: ID!
  os: String
  fcmToken: String
  userId: ID
  version: String
  device: String
}

type AppFeedbackSection implements ContentSection {
  id: ID!
}

type AppRelease {
  versionCode: String!
  releaseDate: Date!
  deprecationDetails: String
  supported: Boolean!
}

type AppTermsStatus {
  latestTermsAgreed: Boolean
  alertTitle: String
  alertDescription: String
  alertButtonLabel: String
}

type AssociatedCinema {
  id: ID!
  name: String!
}

type AudioFingerprintingConfig {
  snippet_numberOfPeakBorderingTimeBins: Int
  snippet_numberOfPeakBorderingFrequencies: Int
  snippet_numberOfVectorNeighbors: Int
  original_numberOfPeakBorderingTimeBins: Int
  original_numberOfPeakBorderingFrequencies: Int
  original_numberOfVectorNeighbors: Int
  halfWidthOfPeakDetectionWindow: Int
  samplesPerBin: Int
  sampleRate: Int
  timeDistanceBetweenBins: Int
  bottomFrequencyLimiter: Int
  topFrequencyLimiter: Int
}

type Auditorium {
  id: ID!
  name: String
  cinema: Cinema!
  seats: [Seat!]!
  seatCategories: [SeatCategory!]!
}

type AverageCinemaVisitsFilter {
  active: Boolean!
  minVisits: Int!
  maxVisits: Int
  timePeriod: TimePeriod
}

input AverageCinemaVisitsFilterInput {
  active: Boolean!
  minVisits: Int!
  maxVisits: Int
  timePeriod: TimePeriod
}

type AverageGroupSizesFilter {
  active: Boolean!
  sizes: [GroupSize!]!
}

input AverageGroupSizesFilterInput {
  active: Boolean!
  sizes: [GroupSize!]!
}

type AveragePerDateStatistic {
  date: Date!
  count: Float!
  newCount: Float
}

type Beacon {
  id: ID!
  cinema: Cinema
  instanceId: Int!
  lastUpdated: DateTime!
  lastScan: DateTime
  comments: String
  lastBatteryChange: DateTime
}

enum BlockedReason {
  NO_SHOW
  PAYMENT_FAILED
  OTHER
}

type BonusCurrency {
  id: ID!
  name: String
}

type BonusPointHistoryConnection {
  bonusPointHistoryItems: [BonusPointHistoryItem!]!
}

type BonusPointHistoryItem {
  id: ID!
  amount: Int
  title: String
  description: String
  datetime: DateTime
}

type BonusPointsFilter {
  active: Boolean!
  minBonusPoints: Int!
  maxBonusPoints: Int
}

input BonusPointsFilterInput {
  active: Boolean!
  minBonusPoints: Int!
  maxBonusPoints: Int
}

type BonusProgram {
  id: ID!
  name: String
  coinName: String
  logo: String
  backgroundColor: String
  accentColor: String
  participatingCinemas: [Cinema!]!
  voucherCatalog: [VoucherClass!]! @deprecated(reason: "[5.0.6] use BonusProgramMembership -> voucher Catalog instead (voucher catalog might be user specific)")
  userMembership(userId: ID): BonusProgramMembership
  termsLink: String
  termsVersion: Int!
  statistics(from: Date!, to: Date!): BonusProgramStatistics
  legalEntity: String!
  bonusType: BonusType
  infoLinks: [CinemaInfoLink!]
}

type BonusProgramMembership {
  id: ID!
  user: User
  bonusProgram: BonusProgram
  voucherCatalog: VoucherCatalog! @deprecated(reason: "[5.2.1] use voucherClasses instead")
  voucherClasses: [VoucherClass!]!
  coinBalance: Int
  statusPointBalance: Int
  statusLevel: StatusLevel
  achievements: [Achievement!]!
  bookingHistory: [BonusPointHistoryItem!]!
  termsStatus: BonusProgramTermsStatus
  ownedVouchers(validOnly: Boolean): [VoucherInstance!]!
  qrCode: String
  collectionDisclaimer: String
  active: Boolean
  joinedAt: DateTime
  inviteCode: String
  pkpass: String
  provisionalBonusPointBookings: [ProvisionalBonusPointBooking!]!
  statusLevelDowngradeAt: DateTime
  avoidStatusLevelDowngradeNumVisits: Int
}

enum BonusProgramMemberStatus {
  BONUSPROGRAM_MEMBERS_ONLY
  NON_BONUSPROGRAM_MEMBERS_ONLY
}

type BonusProgramMemberStatusFilter {
  active: Boolean!
  status: BonusProgramMemberStatus
}

input BonusProgramMemberStatusFilterInput {
  active: Boolean!
  status: BonusProgramMemberStatus
}

type BonusProgramStatistics {
  numBpBookingsInInterval: Int
  numBpBookingsTotal: Int
  numUsersInInterval: Int
  numUsersTotal: Int
  numNewUsersInInterval: Int
  numNewUsersTotal: Int
  scannedVouchersInInterval: Int
  scannedVouchersTotal: Int
  sumBonusPointsInInterval: Int
  sumBonusPointsTotal: Int
  sumBonusPointsSupportInInterval: Int
  sumBonusPointsSupportTotal: Int
  numUsersMultipleBookingsTotal: Int
  redeemedVouchersInInterval: [RedeemedVoucher!]!
}

type BonusProgramTermsStatus {
  latestTermsAgreed: Boolean
  alertTitle: String
  alertDescription: String
  alertButtonLabel: String
}

enum BonusType {
  COINS
  STAMPS
}

type bookCinemaTicketPointsReturnType {
  status: String
}

type bookCouponReturnType {
  user: User
  couponType: String
  couponMessage: String
  bonusProgram: BonusProgram
}

type BookingProcess {
  id: ID!
  timeout: DateTime!
  selectedSeats: [SelectedSeat!]!
  occupiedSeats: [OccupiedSeat!]!
  occupiedSeatingAreas: [OccupiedSeatingArea!]!
  priceInCents: Int!
  isPaid: Boolean
  paypalOrderId: String
  paypalApproveLink: String
  subscriptionTicketWarningText: String
  isFixedSeating: Boolean
}

type BookingProcessReturnType {
  bookingProcess: BookingProcess!
  statusMessage: String
}

enum Brand {
  CINURU
  CINFINITY
  CINEPLEX
  CINEWEB
  LUMOS
  ELSELICHTSPIELE
  DREHWERK
}

type ButtonSection implements ContentSection {
  id: ID!
  buttonLabel: String!
  onPressType: ButtonSectionOnPressType!
  routeName: String
  routeArgs: Json
  margin: String
  hideChevron: Boolean
}

enum ButtonSectionOnPressType {
  NAVIGATION
  IN_APP_BROWSER
  OTHER_APP
}

type buyAndRedeemVoucherReturnType {
  success: Boolean!
}

type buyVoucherReturnType {
  voucherInstance: VoucherInstance
}

type Campaign {
  id: ID!
  type: CampaignType
  trigger: CampaignTrigger
  name: String!
  cinemas: [Cinema!]!
  channels: [Channel!]!
  messageTitle: String
  message: String
  messageImage: String
  link: String
  creator: User!
  status: CampaignStatus!
  userGroupFilter: UserGroupFilter
  userGroupFilterOnReferencedMovie: UserGroupFilterOnReferencedMovie
  sendingTime: Time
  sendingDate: Date
  sendExactlyMinutesAfterTrigger: Int
  sendOnDayXAfterTrigger: Int
  language: Language
  emailContent: Json
  startDate: String
  endDate: String
  startTime: String
  endTime: String
  timeUnit: CampaignTimeUnit
  timeUnitFactor: Int
  numberOfIntervals: Int
  intervalEndType: CampaignIntervalEndType
  eventType: CampaignEventType
  emailSubject: String
  targetGroupIds: [ID!]
  triggerThreshold: String
}

enum CampaignEventType {
  BEFORE
  AFTER
}

enum CampaignIntervalEndType {
  NEVER
  ON
  AFTER
}

type CampaignsByCinemasReturnType {
  campaigns: [Campaign!]!
}

enum CampaignStatus {
  EDITING
  ACTIVE
  SENT
  FAILED
  ARCHIVED
}

enum CampaignTimeUnit {
  MINUTE
  HOUR
  DAY
  WEEK
  MONTH
  YEAR
}

enum CampaignTrigger {
  SINGLE_EVENT
  REGULAR
  BIRTHDAY
  VISIT
  FILM_START
  BOUGHT_TICKET
  BOUGHT_CONCESSIONS
  NEWSLETTER_SUBSCRIBED
  CREATED_ACCOUNT
  APP_INSTALL
  JOIN_BONUSPROGRAM
  POINTS_REACHED
  STATUSLEVEL_REACHED
  NO_VISIT
}

enum CampaignType {
  FIXED_DATE
  MOVIE_CINEMA_START
  MOVIE_CINEMA_LEAVE
  MOVIE_ANNOUNCED
  MOVIE_NEW_TRAILER
  USER_BIRTHDAY
  USER_CINEMA_LAST_VISIT
  USER_POINTS_REACHED
  USER_POINTS_LOST
  USER_MOVIE_SEEN
  USER_MOVIE_MARKED
  USER_MOVIE_RATED
  USER_VOUCHER_EXPIRES
  VOUCHER_START
  VOUCHER_LEAVE
}

type CancelBookingProcessResponse {
  success: Boolean!
}

type CancelOrderReturnType {
  canceledOrder: Order
  currentUser: User
}

type CancelTicketReturnType {
  canceledTicket: Ticket
  currentUser: User
}

type CardSection implements ContentSection {
  id: ID!
  title: String
  text: String
  collapsible: Boolean
  collapsed: Boolean
  margin: String
  cardSectionImageUrl: String
}

type CastOrCrewMember {
  id: ID!
  fullName: String
  image: String
}

type CastOrCrewMemberAffinityFilter {
  active: Boolean!
  castOrCrewMembers: [CastOrCrewMember!]!
}

input CastOrCrewMemberAffinityFilterInput {
  active: Boolean!
  castOrCrewMembers: [ID!]!
}

type CentralPlannerReservation implements ContentSection {
  id: ID!
  title: String
  url: String!
  margin: String
  collapsed: Boolean
  infoText: String
  infoTextAfter: String
}

enum Channel {
  EMAIL
  PUSH
  APP
  IN_APP
}

enum CheckVoucherCodeErrorCode {
  VOUCHER_CODE_INVALID
  VOUCHER_CODE_ALREADY_REDEEMED
  VOUCHER_CODE_TOO_OLD
}

type CheckVoucherCodeReturnType {
  valid: Boolean!
  errorCode: CheckVoucherCodeErrorCode
}

type CheckVoucherCodesReturnType {
  valid: Boolean!
  errorMessage1: String
  errorMessage2: String
}

type Cinema {
  id: ID!
  bonusProgram: BonusProgram
  userCustomership(userId: ID): CinemaCustomership
  movies(futureOnly: Boolean): [Movie!]!
  products(categories: [CinemaProductCategory!]): [CinemaProduct!]!
  name: String
  logo: String
  backgroundColor: String
  accentColor: String
  claim: String
  clusters: [String!]!
  hasBonusProgram: Boolean
  hasTrailerRating: Boolean @deprecated(reason: "[???]: trailerrating has been removed")
  headerImage: String
  reservationHotline: String
  imprint: String
  technologies: String
  history: String
  specialAboutUs: String
  currentInformation: String
  onlineReservationBaseUrl: String
  onlineTicketingBaseUrl: String
  ticketProvider: String
  onlineTicketingDisclaimer: String
  street: String
  houseNumber: String
  zipCode: String
  city: String
  parkingDescription: String
  locationDescription: String
  location: GeoCoordinates
  latitude: String @deprecated(reason: "[4.1.0]: use location instead")
  longitude: String @deprecated(reason: "[4.1.0]: use location instead")
  googleMapsLink: String
  appleMapsLink: String
  phone: String
  facebook: String
  twitter: String
  instagram: String
  tiktok: String
  youtube: String
  website: String
  email: String
  barrierFree: Boolean
  barrierFreeText: String
  hearingImpaired: Boolean
  hearingImpairedText: String
  blind: Boolean
  blindText: String
  pricesDisplay: [CinemaInformationParagraph!]!
  specialOffersDisplay: [CinemaInformationParagraph!]!
  giftVouchersDisplay: [CinemaInformationParagraph!]!
  sneaksDisplay: [CinemaInformationParagraph!]!
  openingHoursDisplay: [CinemaOpeningHours!]!
  additionalInfoSections: [CinemaInformationParagraph!]
  posTokens: [String!]!
  moviesWithLastChanceInfo(includeOutdated: Boolean!): [LastChanceItem!]!
  hasDynamicPricing: Boolean!
  brands: [Brand!]!
  accessRestrictedTo: AccessRestriction!
  infoLinks: [CinemaInfoLink!]
  currentScreeningAttributes: [ScreeningAttribute!]!
  hasMigratableCustomerCards: Boolean!
  externalCinemaId: String
  injectionScript: String
  beacons: [Beacon!]!
}

type CinemaCluster {
  id: ID!
  name: String!
  position: Int!
  cinemas: [Cinema!]!
}

input CinemaClusterData {
  position: Int!
  name: String!
  cinemaIds: [ID!]!
}

type CinemaCustomership {
  id: ID!
  cinema: Cinema
  user: User
}

type CinemaInfoLink {
  name: String!
  url: String!
}

type CinemaInformationParagraph {
  title: String
  image: String
  description: String
  price: String
}

input CinemaInformationParagraphInput {
  title: String
  image: String
  description: String
  price: String
}

input CinemaLocationInput {
  latitude: Float
  longitude: Float
}

type CinemaOpeningHours {
  weekdays: String
  hours: String
}

input CinemaOpeningHoursInput {
  weekdays: String
  hours: String
}

type CinemaOperatingCompany {
  id: ID!
  name: String
  targetGroupFilterConfig: [TargetGroupFilterConfig!]!
  allVoucherClassesOfCinemaOperatingCompany: [VoucherClass!]!
  accessRightDashboard: Boolean
  accessRightFilmStatistics: Boolean
  accessRightBonusProgram: Boolean
  accessRightCampaigning: Boolean
  externalPhoneNumber: String
  pricingLevel: String
  pricingScheme: String
  cinemas: [AssociatedCinema!]!
  associatedUsers: [User!]!
}

input CinemaOperatingCompanyData {
  name: String
  accessRightDashboard: Boolean
  accessRightFilmStatistics: Boolean
  accessRightBonusProgram: Boolean
  accessRightCampaigning: Boolean
  cinemasIds: [ID]
}

type CinemaPayoutInfo {
  cinema: Cinema!
  numOfSubscriptionTickets: Int!
  numAdditionalTickets: Int!
}

type CinemaProduct {
  id: ID
  name: String
  subtext: String
  bonusPoints: Int
  category: CinemaProductCategory
}

enum CinemaProductCategory {
  TICKET
}

type CinemaSpecificContent {
  cinema: Cinema!
  teaser: String
  attributes: [ScreeningAttribute!]!
  week: Int
}

type CinemaVisitSinceFilter {
  active: Boolean!
  amount: Int
  timePeriod: TimePeriod
}

input CinemaVisitSinceFilterInput {
  active: Boolean!
  amount: Int
  timePeriod: TimePeriod
}

type claimOrDiscardProvisionalBonusPointBookingReturnType {
  showExitPoll: Boolean
  bonusProgramMembership: BonusProgramMembership!
}

type Company {
  id: ID!
  numericalID: Int
  logo: String!
  name: String!
}

type ComscoreImdbEntry {
  id: ID!
  comscore: String!
  type: String!
  imdb: String!
  title: String!
}

enum ConfirmationCodeType {
  LOGIN_CREATION
  EMAIL_CHANGE
  PASSWORD_RESET
}

input ConsentInput {
  cookieId: String!
  datetime: DateTime!
  consent: Boolean!
}

interface ContentSection {
  id: ID!
}

type ContentSectionList implements ContentSection {
  id: ID!
  title: String
  displayType: ContentSectionListDisplayType
  sections: [ContentSection!]!
  headerImage: String
  headerImageHeight: String
}

enum ContentSectionListDisplayType {
  MOVIE_LIST_ROW
}

enum ContentSectionListName {
  CURATED
  RECOMMENDED
}

type CreateCampaignReturnType {
  campaign: Campaign
}

type createCinemaReturnType {
  success: Boolean!
  cinema: Cinema
}

type createGoogleWalletBonusCardReturnType {
  jwtUrl: String
}

type CreateIntercomLeadReturnType {
  success: Boolean!
}

type createOnlineTicketingOutlinkIdReturnType {
  identifier: String!
}

type createOrUpdateUserReturnType {
  user: User
}

type CreateSoonInCinemaItemReturnType {
  soonInCinemaItem: SoonInCinemaItem
  success: Boolean!
}

type CreateTargetGroupReturnType {
  targetGroup: TargetGroup
}

type Credit {
  id: ID!
  department: MovieDepartment
  role: String
  person: CastOrCrewMember
}

type CurrentOrdersSection implements ContentSection {
  id: ID!
  orders: [Order!]!
}

type CurrentTicketsSection implements ContentSection {
  id: ID!
  tickets: [Ticket!]!
}

"""
A date string, such as 2007-12-03, compliant with the `full-date` format
outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for
representation of dates and times using the Gregorian calendar.
"""
scalar Date

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the
`date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO
8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar DateTime

type deleteBeaconReturnType {
  success: Boolean!
}

type DeleteCampaignsReturnType {
  success: Boolean!
}

type deleteCineplexUserReturnType {
  success: Boolean!
}

type DeleteImageReturnType {
  success: Boolean
}

type DeleteReviewRatingNewReturnType {
  deletedReviewRating: ReviewRating!
}

type DeleteSeenInCinemaReturnType {
  deletedSeenInCinema: SeenInCinema!
}

type DeleteSoonInCinemaItemReturnType {
  soonInCinemaItemId: ID!
  success: Boolean!
}

type DeleteTargetGroupReturnType {
  success: Boolean
}

type DeleteUserReturnType {
  success: Boolean!
}

type deselectCinemaReturnType {
  success: Boolean
}

type DistanceFilter {
  active: Boolean!
  zipCode: Int
  distanceInKm: Int
}

input DistanceFilterInput {
  active: Boolean!
  zipCode: Int
  distanceInKm: Int
}

type EditCampaignEmailReturnType {
  campaign: Campaign
}

type EditCampaignReturnType {
  campaign: Campaign
}

type EditTargetGroupReturnType {
  targetGroup: TargetGroup
}

type EditUserGroupFilterOnReferencedMovieReturnType {
  userGroupFilterOnReferencedMovie: UserGroupFilterOnReferencedMovie
}

type EditUserGroupFilterReturnType {
  userGroupFilter: UserGroupFilter
}

type EmailComponent {
  id: ID!
  html: String!
}

enum EmailComponentName {
  Image
  Spacer
  MarkdownText
  MarkdownTextWithImage
  FullProgram
  RecommendationSection
  DashboardEmailHeader
  DashboardEmailFooter
  Button
  QRCode
  FilmSeries
}

type ErrorStatistics {
  id: ID!
  errorType: String!
  errorMessage: String
  queryName: String
  routeName: String
  stackTrace: String
  occurrences: Int!
  occurrencesByDay: [IntMap!]!
  occurrencesByDevice: [IntMap!]!
  affectedUsers: Int!
  affectedSessions: Int!
  affectedVersions: [String!]!
}

type estimateCampaignSizeReturnType {
  userGroupSize: Int
  exampleReferenceMovie: ExampleReferenceMovie
}

type ExampleReferenceMovie {
  userGroupSize: Int
  movie: Movie
}

type ExitPollSection implements ContentSection {
  id: ID!
  screening: Screening!
}

type ExternalNewsletterPreferences {
  id: ID!
  name: String
  category: String
  subscribed: Boolean
}

type FeaturedMovieSection implements ContentSection {
  id: ID!
  description: String
  title: String
  movie: Movie!
}

type ForeignIdType {
  title: String
  foreignId: String
}

type ForeignMovieIdentifier {
  id: ID!
  foreignIdType: String!
  title: String!
  currentMovie: Movie
}

enum Gender {
  MALE
  FEMALE
  DIVERSE
}

type GendersFilter {
  active: Boolean!
  genders: [Gender!]!
}

input GendersFilterInput {
  active: Boolean!
  genders: [Gender!]!
}

type Genre {
  id: ID!
  name: String
}

type GenreAffinityFilter {
  active: Boolean!
  genres: [String!]!
}

input GenreAffinityFilterInput {
  active: Boolean!
  genres: [String!]!
}

type GeocodeResult {
  location: GeoCoordinates
  route: String
  postalCode: String
  locality: String
  country: String
}

type GeoCoordinates {
  latitude: Float!
  longitude: Float!
}

type GoogleAdBannerSection implements ContentSection {
  id: ID!
  adUnitName: String
}

enum GroupSize {
  SINGLE
  PAIRS
  LARGEGROUP
}

type HeadingSection implements ContentSection {
  id: ID!
  text: String
  size: String
  align: String
  margin: String
}

type HeroPlayer implements ContentSection {
  id: ID!
  content: [HeroPlayerContent!]!
}

type HeroPlayerContent {
  id: ID!
  image: String!
  video: String
  title: String
  buttonLabel: String!
  appPath: String
  url: String
}

enum HostingProvider {
  YOUTUBE
  SHARC
  CINEPLEX
  CINEWEB
}

type Image {
  id: ID!
  datetime: DateTime!
  url: String!
  cinemaOperatingCompanyId: String!
}

type ImageCardSection implements ContentSection {
  id: ID!
  title: String
  description: String
  link: String
  imageUrl: String!
}

type ImageCardWithButtonSection implements ContentSection {
  id: ID!
  imageUrl: String!
  title: String
  link: String
  optionalButtonLabel: String
  heightFactor: Float!
}

type increaseUserTestingStatusReturnType {
  user: User
}

type InitialRateMoviesSection implements ContentSection {
  id: ID!
}

type InterestRating {
  id: ID!
  movie: Movie!
  user: User!
  value: Float!
  updatedAt: DateTime
}

type IntMap {
  key: String
  value: Int
}

type Invoice {
  id: ID!
  url: String!
  date: Date!
  priceInCents: Int!
}

type JoinBonusProgramReturnType {
  success: Boolean!
  bonusProgramMembership: BonusProgramMembership
}

scalar Json

type Language {
  id: ID!
  url: String!
  foreignIdType: String!
  programUrl: String!
  trailerAutoplay: Boolean!
}

type LastChanceItem {
  movie: Movie!
  lastKnownScreening: Screening
  screeningsMarkedAsLastChance: [Screening!]!
}

type LeaveBonusProgramReturnType {
  success: Boolean!
}

type LineItem {
  id: ID!
  productId: ID
  amount: Int
  price: Float
  name: String
  subItems: [LineItem!]!
}

type LinkedAccount {
  id: ID!
  username: String!
  email: String
  status: LinkedAccountStatus!
  acceptedDatetime: DateTime
  userId: ID!
}

enum LinkedAccountStatus {
  INVITED
  INCOMING_INVITE
  ACCEPTED
  DECLINED_BY_USER
  DECLINED_BY_FRIEND
}

type ListOfMovies {
  id: ID!
  items: [ListOfMoviesItem!]!
}

type ListOfMoviesItem {
  id: ID!
  movie: Movie
  description: String
}

type LocationForZipCodeResult {
  longitude: Float!
  latitude: Float!
  name: String
}

enum LoginError {
  WRONG_PASSWORD
  WRONG_EMAIL_OR_ID
  NEITHER_EMAIL_NOR_ID_PROVIDED
}

type LoginReturnType {
  user: User
  posApp: PosApp
  fileServerToken: String
  jwt: String
  csrf: String
  roles: [LoginRole!]
  refreshToken: String
}

enum LoginRole {
  USER
  POS_APP
  CINEMA_ADMIN
  ROOT
}

type LoginStatusType {
  success: Boolean
  errors: [LoginError!]
}

input LogItem {
  type: String
  datetime: DateTime
  value: String
}

type LogItemsReturnType {
  success: Boolean
}

input LogOptions {
  source: LogSource
  appVersion: String
  device: String
  session: String
  appId: String
  url: String
}

type LogoutReturnType {
  success: Boolean
}

enum LogSource {
  APP
  POS
  DASHBOARD
  OTHER
  NOTIFICATION_SERVICE
}

type LogUserScreeningInterestResponse {
  userId: ID!
  vc: String
  userName: String
  errorMessage: String
}

type LongetermBenefitsReceivedFilter {
  active: Boolean!
  status: LongTermBenefitsStatus
}

input LongetermBenefitsReceivedFilterInput {
  active: Boolean!
  status: LongTermBenefitsStatus
}

enum LongTermBenefitsStatus {
  LONGTERMBENEFITS_RECEIVED_ONLY
  NO_LONGTERMBENEFITS_RECEIVED_ONLY
}

type MissedPayoutInfo {
  user: User
  subscription: Subscription!
}

type Movie {
  id: ID!
  title: String
  banner: String
  poster: String
  directedBy: String
  synopsis: String
  duration: Int
  nonMovieEvent: Boolean
  averageRating: Float
  releaseDate: Date
  youtubeTrailerId: String
  ageRating: String
  genres: [String!]!
  credits(departments: [MovieDepartment!]): [Credit!]!
  cinemaSpecificContent(cinemaIds: [ID!]!): [CinemaSpecificContent!]!
  screenings(cinemaIds: [ID!], after: DateTime, before: DateTime): [Screening!]!
  firstScreening(cinemaIds: [ID!]): Screening @deprecated(reason: "[4.1.14] use currentOrNextAvailableInCinemaDate instead")
  reviewRatingByCurrentUser: ReviewRating
  interestRatingByCurrentUser: InterestRating
  seenInCinemaByCurrentUser: SeenInCinema
  statistics(userGroupDefinition: UserGroupDefinition): MovieStatistics
  currentOrNextAvailableInCinemaDate(cinemaIds: [ID!]!): Date
  trailers: [Trailer!]!
  country: String
  screeningStatus: ScreeningStatusType
  foreignMovieId(foreignIdType: String!): ForeignIdType
  isFromMasterFilmDB: Boolean
}

type MovieAffinityFilter {
  active: Boolean!
  movies: [Movie!]!
}

input MovieAffinityFilterInput {
  active: Boolean!
  movies: [ID!]!
}

type MovieAttributesAffinityFilter {
  active: Boolean!
  movieAttributes: [ScreeningAttribute!]!
}

input MovieAttributesAffinityFilterInput {
  active: Boolean!
  movieAttributes: [ScreeningAttributeInput!]!
}

enum MovieDepartment {
  ACTING
  DIRECTING
  WRITING
}

type MovieList {
  id: ID!
  title: String
  subTitle: String
  description: String
  artwork: String
  items: [MovieListItem!]!
  cinemas: [Cinema!]!
  itemDisplayType: MovieListItemDisplayType
  type: String
}

type MovieListItem {
  id: ID!
  description: String
  movie: Movie!
  screenings: [Screening!]!
}

enum MovieListItemDisplayType {
  RELEASE_DATE
  SYNOPSIS
  SCREENINGS
  POSTER_WALL
  POSTER_WALL_RATINGS
}

type MovieListSection implements ContentSection {
  id: ID!
  displayType: MovieListSectionDisplayType!
  title: String
  subTitle: String
  movieList: MovieList!
  emptyMessage: String
}

enum MovieListSectionDisplayType {
  COLLAGE_CARD
  ARTWORK_CARD
  POSTER_ROW
  BANNER_ROW
  BANNER_COLUMN
  SMALL_BANNER_COLUMN
}

type MoviesNotSeenFilter {
  active: Boolean!
  movies: [Movie!]!
}

input MoviesNotSeenFilterInput {
  active: Boolean!
  movies: [ID!]!
}

type MoviesOnWatchListFilter {
  active: Boolean!
  movies: [Movie!]!
}

input MoviesOnWatchListFilterInput {
  active: Boolean!
  movies: [ID!]!
}

type MoviesSeenFilter {
  active: Boolean!
  movies: [Movie!]!
}

input MoviesSeenFilterInput {
  active: Boolean!
  movies: [ID!]!
}

type MovieStatistics {
  id: ID!
  movie: Movie!
  numWatchlistings: Int!
  numInteractions: Int!
}

type Mutation {
  a(id: ID!): String
  upsertApp(id: ID!, fcmToken: String, os: String!, version: String, device: String): upsertAppReturnType
  updateCinemaOperatingCompanyTargetGroupFilterConfig(targetGroupFilterConfig: [TargetGroupFilterConfigInput!]!): updateCinemaOperatingCompanyTargetGroupFilterConfigReturnType!
  updateCinemaOperatingCompany(id: ID!, data: CinemaOperatingCompanyData!): CinemaOperatingCompany!
  createCinemaOperatingCompany(data: CinemaOperatingCompanyData!): CinemaOperatingCompany!
  updateBonusProgram(bonusProgramId: ID!, name: String!, legalEntity: String!, termsVersion: Int!, termsLink: String): UpdateBonusProgramReturnType!
  bookCinemaTicketPoints(amount: Int!, userQrCode: String!, movieId: ID, groupSize: Int, bonusProgramId: String, cinemaId: String): bookCinemaTicketPointsReturnType
  joinBonusProgram(bonusProgramId: ID!, voucherCode: String, voucherCodes: [String!]): JoinBonusProgramReturnType
  bookCoupon(couponIdentifier: ID!, bonusProgramId: ID): bookCouponReturnType
  acceptBonusProgramTerms(bonusProgramId: ID!): Boolean
  rejectBonusProgramTerms: Boolean
  addInvitedBy(inviteCode: String!, bonusProgramId: ID!): AddInvitedByReturnType!
  claimOrDiscardProvisionalBonusPointBooking(claim: Boolean!, provisionalBonusPointBookingId: ID!, screeningId: ID): claimOrDiscardProvisionalBonusPointBookingReturnType
  processScannedQrCode(qrCodeText: String!, bonusProgramId: ID!): ProcessScannedQrCodeReturnType
  createGoogleWalletBonusCard(bonusProgramId: ID!): createGoogleWalletBonusCardReturnType
  createCampaign(name: String!, type: CampaignType, trigger: CampaignTrigger, cinemaIds: [ID!]!, isNewCampaign: Boolean): CreateCampaignReturnType
  editCampaign(id: ID!, name: String!, cinemaIds: [ID!], channels: [Channel!]!, message: String, messageTitle: String, messageImage: String, link: String, sendingTime: Time, sendingDate: Date, sendExactlyMinutesAfterTrigger: Int, sendOnDayXAfterTrigger: Int, status: CampaignStatus, trigger: CampaignTrigger, startDate: String, endDate: String, startTime: String, endTime: String, timeUnit: CampaignTimeUnit, timeUnitFactor: Int, numberOfIntervals: Int, intervalEndType: CampaignIntervalEndType, eventType: CampaignEventType, emailSubject: String, targetGroupIds: [ID!], triggerThreshold: String): EditCampaignReturnType!
  setCampaignStatus(id: ID!, status: CampaignStatus!): SetCampaignStatusReturnType!
  editCampaignEmail(id: ID!, emailContent: Json!): EditCampaignEmailReturnType!
  deleteCampaigns(ids: [ID!]!): DeleteCampaignsReturnType!
  updateCampaignsStatus(ids: [ID!]!, status: CampaignStatus!): UpdateCampaignsStatusReturnType!
  updateCinema(cinema: UpdateCinemaInput!): updateCinemaReturnType!
  createCinema: createCinemaReturnType!
  createCompany(name: String!, logo: String!): Company!
  editCompany(id: ID!, name: String!, logo: String!): Company!
  deleteCompanies(ids: [ID!]!): Int!
  suggestCinema(cinemaName: String!, fromEmail: String, message: String): SuggestCinemaReturnType
  submitContactForm(fromName: String!, fromEmail: String!, fromPhone: String, message: String!): SubmitContactFormReturnType @deprecated(reason: "use createIntercomLead instead")
  createIntercomLead(visitorId: String, email: String!, name: String, phone: String, attributes: Json, utmSource: String, utmCampaign: String, utmContent: String, utmMedium: String, utmTerm: String, newsletterRequested: Boolean, company: String): CreateIntercomLeadReturnType!
  submitAppFeedback(likesApp: Boolean, agreedToRateOrGiveFeedback: Boolean!): SubmitAppFeedbackReturnType
  upsertInterestRating(movieId: ID!, value: Float!): upsertInterestRatingReturnType
  logItems(items: [LogItem], options: LogOptions): LogItemsReturnType
  updateForeignMovieMapping(foreignId: ID!, movieId: ID!): Boolean!
  upsertNewsItem(id: ID, title: String, teaser: String, body: String, movieId: ID, poster: String, link: String, image: String, linkText: String): upsertNewsItemReturnType!
  updateNotificationPreference(channel: String!, pushRequested: Boolean, emailRequested: Boolean): updateNotificationPreferenceReturnType
  updateExternalNewsletterPreference(externalNewsletterId: ID!, subscribed: Boolean!): updateExternalNewsletterPreferenceReturnType!
  startBookingProcess(screeningId: ID!): BookingProcessReturnType
  selectSeats(bookingProcessId: ID!, seatIds: [ID!]!, screeningId: ID!): BookingProcessReturnType
  cancelBookingProcess(bookingProcessId: ID!, screeningId: ID!): CancelBookingProcessResponse!
  selectSeatPricingCategories(bookingProcessId: ID!, seatPricingCategories: [SeatPricingCategoryInput!]!, screeningId: ID!): BookingProcessReturnType
  selectSeatingAreaPricingCategories(bookingProcessId: ID!, pricingCategoryIds: [ID!]!, screeningId: ID!, seatingAreaId: ID): BookingProcessReturnType
  submitBookingProcess(bookingProcessId: ID!, screeningId: ID!): BookingProcessReturnType
  upsertReviewRating(movieId: ID!, value: Float!): upsertReviewRatingReturnType
  upsertQuizReviewRating(sentEmailId: String!, movieId: ID!, value: Float): upsertReviewRatingReturnType
  deleteReviewRating(movieId: ID!): Boolean
  deleteReviewRating_2(movieId: ID!): DeleteReviewRatingNewReturnType
  createOnlineTicketingOutlinkId(screeningId: ID!): createOnlineTicketingOutlinkIdReturnType
  updateScreening(id: ID!, lastChance: Boolean!): UpdateScreeningReturnType
  cancelTicket(id: ID!): CancelTicketReturnType
  updateTicket(id: ID!, scanned: Boolean!): Ticket
  openTicket(id: ID!): Ticket
  cancelOrder(id: ID!): CancelOrderReturnType!
  startOrderPreparation(id: ID!): StartPreparationReturnType!
  createSoonInCinemaItem(cinemaId: ID!, movieId: ID, movieTitle: String, startDate: Date): CreateSoonInCinemaItemReturnType
  updateSoonInCinemaItem(id: ID!, movieTitle: String, startDate: Date): UpdatedSoonInCinemaItemReturnType
  deleteSoonInCinemaItem(id: ID!): DeleteSoonInCinemaItemReturnType
  acceptAppTerms: acceptAppTermsReturnType
  allowDataUsage: acceptAppTermsReturnType
  allowAppTracking(allowed: Boolean!): acceptAppTermsReturnType
  optInToMarketingEmails(rejected: Boolean, token: String): acceptAppTermsReturnType
  login(email: String!, password: String!, privileged: Boolean, appId: ID, logoutFromOtherApps: Boolean): LoginReturnType
  socialLogin(provider: SocialAuthProvider!, accessToken: String, idToken: String, appId: ID, email: String, name: String, firstName: String, lastName: String, socialLoginSession: String): LoginReturnType
  loginPOS(authToken: String!): LoginReturnType
  logout(appId: ID): LogoutReturnType
  refreshLogin(refreshToken: String!, privileged: Boolean): LoginReturnType
  createAnonymousUser(appId: ID, bonusProgramId: ID): LoginReturnType
  requestLoginCreation(email: String!, password: String!, name: String, firstName: String, lastName: String, gender: Gender, birthDate: Date, telephone: String, street: String, houseNumber: String, zipCode: String, city: String, country: String): RequestCodeReturnType
  requestEmailChange(email: String!): RequestCodeReturnType
  resendEmailConfirmation(email: String!, isChangeRequest: Boolean): resendActivationEmailReturnType
  confirmEmail(email: String!, code: String!, appId: ID, isChangeRequest: Boolean): LoginReturnType
  requestPasswordReset(email: String!): RequestPasswordResetReturnType
  changePassword(email: String!, password: String!, code: String!, appId: ID): LoginReturnType
  updateUserProfile(name: String, firstName: String, lastName: String, street: String, houseNumber: String, zipCode: String, city: String, country: String, gender: Gender, telephone: String, birthDate: Date): createOrUpdateUserReturnType
  selectCinemas(cinemaIds: [ID!]!): selectCinemasReturnType
  selectCinema(cinemaId: ID!): selectCinemaReturnType
  deselectCinema(cinemaId: ID!): deselectCinemaReturnType
  increaseUserTestingStatus(testingStatus: TestingStatus!): increaseUserTestingStatusReturnType
  updateUserAdminStatus(userId: ID!, cinemaOperatingCompanyId: ID!, setAdminPrivilege: Boolean): updateUserAdminStatusReturnType!
  deleteUser: DeleteUserReturnType!
  deleteCineplexUser(id: String!, apiKey: String!): deleteCineplexUserReturnType!
  leaveBonusProgram(bonusProgramId: ID!): LeaveBonusProgramReturnType!
  rememberSkippedOnboardingStep(step: OnboardingStep!): rememberSkippedOnboardingStepReturnType!
  updateUser(userId: ID!, blocked: Boolean, blockedText: String, firstname: String, lastname: String, street: String, houseNumber: String, zipCode: String, city: String, email: String, adminCinemaOperatingCompanyIds: [ID!], resetAppChangeBlockedUntil: Boolean): User!
  updateUserSubscription(id: ID!, blocked: Boolean!, blockedText: String): Subscription!
  addLogin(email: String!, password: String!, appId: ID, name: String, firstName: String, lastName: String): LoginReturnType @deprecated(reason: "[5.2.3] use requestLoginCreation instead")
  verifyEmail(email: String, appId: ID, token: String!, updating: Boolean): LoginReturnType @deprecated(reason: "[5.2.3] use confirmEmail instead")
  updateEmail(email: String!): createOrUpdateUserReturnType @deprecated(reason: "[5.2.3] use requestEmailChange instead")
  updatePassword(oldPassword: String, appId: ID, token: String, password: String!, email: String): LoginReturnType @deprecated(reason: "[5.2.3] use requestPasswordReset instead")
  sendResetPasswordEmail(email: String!): RequestPasswordResetReturnType! @deprecated(reason: "[5.0.12] use requestPasswordReset instead")
  resendActivationEmail(email: String, userId: ID, updating: Boolean): resendActivationEmailReturnType! @deprecated(reason: "[5.0.12] use resendConfirmationEmail instead")
  acceptCinuruTerms: Boolean @deprecated(reason: "[5.0.12] use acceptAppTerms instead")
  setInitialRatedMovies: SetInitialRatedMoviesReturnType
  editUserGroupFilter(userGroupFilter: UserGroupFilterInput!): EditUserGroupFilterReturnType!
  editUserGroupFilterOnReferencedMovie(userGroupFilterOnReferencedMovie: UserGroupFilterOnReferencedMovieInput!): EditUserGroupFilterOnReferencedMovieReturnType!
  buyVoucher(voucherClassId: ID!): buyVoucherReturnType
  createVoucherInstances(voucherClassId: ID!, companyId: ID, numberOfVouchers: Int!, reason: String, validUntil: Date): [VoucherInstance!]!
  redeemVoucher(voucherQR: String!): redeemVoucherReturnType
  buyAndRedeemVoucher(voucherClassId: ID!, userId: ID!): buyAndRedeemVoucherReturnType
  updateVoucherInstance(id: ID!, isValid: Boolean!): VoucherInstance
  storeEikonaPreshowData(authorization: String!, data: String!): Boolean
  sendNotifications(authToken: String!, userIds: [Int!], title: String!, body: String!, appLink: String!, imageUrl: String!, inAppNotification: Boolean!, pushNotificationChannel: String!, language: String): String!
  sendTestNotification(title: String!, body: String!, link: String!, imageUrl: String!, inAppNotification: Boolean!, pushNotificationChannel: String!, language: String, cinemaIds: [ID!]!): SendTestNotificationReturnType
  sendShowtimeAnalyticsCampaign(authKey: String!, id: String!, name: String!, channels: [String!]!, customerIds: [String!]!, callbackEvents: [String!]!, pushData: ShowtimesCampaignData!): String!
  dismissExitPoll(movieId: ID!): Boolean!
  storeSeatPlan(screeningId: ID!): StoreSeatPlanReturnType!
  recordTicketPurchase(screeningId: ID!, selectedSeats: [SeatSelection!]!): RecordTicketPurchaseReturnType!
  confirmOnlineTicketingBooking(screeningId: ID!, confirmPageLink: String, voucherCodes: [String!], userTokens: [String!], bookingId: String): RecordTicketPurchaseReturnType!
  syncTicket(id: ID!): Ticket
  storeSDKLogin(data: SDKLoginInput!): StoreSDKLoginReturnType
  storeConsent(data: ConsentInput!): StoreConsentReturnType
  upsertSeenInCinema(movieId: ID!): UpsertSeenInCinemaReturnType
  deleteSeenInCinema(movieId: ID!): DeleteSeenInCinemaReturnType
  subscribeToNewsletter(brand: Brand!, email: String!, channel: String!): SubscribeToNewsletterReturnType
  sendTestEmail(brand: Brand!, componentData: String!): SendTestEmail!
  createTargetGroup(name: String!, cinemaIds: [ID!]!): CreateTargetGroupReturnType!
  editTargetGroup(id: ID!, name: String!, cluster: [TargetGroupClusterInput!]!): EditTargetGroupReturnType!
  deleteTargetGroups(ids: [ID!]!): DeleteTargetGroupReturnType
  updateTargetGroupsStatus(ids: [ID!]!, newStatus: UpdatedTargetGroupStatus): UpdateTargetGroupsStatusReturnType
  saveImagePath(filePath: String!): SaveImagePathReturnType
  deleteImage(id: ID!): DeleteImageReturnType
  subscribe(subscriptionTierId: ID, voucherCode: String): SubscribeResponse!
  reportApprovedSubscription(paypalApproveLink: String!): [Subscription!]! @deprecated(reason: "[1350/1351] completed subscriptions will be recognized via User.subscriptions")
  unsubscribe(subscriptionId: ID!): [Subscription!]!
  createUserTokenForSubscriptionValidation: UserTokenResponse!
  logUserScreeningInterests(cinemaId: ID!, screeningId: ID!, linkedUserIds: [ID!]): [LogUserScreeningInterestResponse!]!
  reportScreeningError(voucherCode: String!, userId: ID, screeningId: ID!): ReportScreeningErrorResponse! @deprecated(reason: "[>1394] rely on prevalidation")
  validateVoucherCode(voucherCode: String!): ValidateVoucherCodeResponse!
  requestLinkAccount(email: String!): User!
  acceptLinkAccount(id: ID!): User!
  declineLinkAccount(id: ID!): User!
  deleteLinkedAccount(id: ID!): User!
  capturePaypalOrderAndCreateTickets(bookingProcessId: ID!): [Ticket!]!
  createBeacon(cinemaId: ID!, comments: String, lastBatteryChange: DateTime): Beacon!
  updateBeacon(id: ID!, comments: String, lastBatteryChange: DateTime): Beacon!
  deleteBeacon(id: ID!): deleteBeaconReturnType!
  registerBeaconScanned(instanceId: Int!): RegisterBeaconScannedReturnType
  updateCinemaCluster(id: ID!, data: CinemaClusterData!): CinemaCluster!
  createCinemaCluster(data: CinemaClusterData!): CinemaCluster!
  deleteCinemaClusters(ids: [ID!]!): Int!
}

type NewsItem {
  id: ID!
  title: String
  date: Date
  teaser: String
  body: String
  image: String
  poster: String
  overviewPoster: String
  movie: Movie
  screenings: [Screening!]!
  hideOnOverview: Boolean
  link: String
  linkText: String
}

type NewsListSection implements ContentSection {
  id: ID!
  title: String
  newsItems: [NewsItem!]!
}

type NewSubscriptionStatisticsResponse {
  numberOfUsers: Int!
  numberOfActiveSubscriptions: Int!
  averageData(dataType: StatisticDataType!, groupBy: StatisticGroupBy!): [AveragePerDateStatistic!]!
}

type NoCinemaVisitSinceFilter {
  active: Boolean!
  amount: Int
  timePeriod: TimePeriod
}

input NoCinemaVisitSinceFilterInput {
  active: Boolean!
  amount: Int
  timePeriod: TimePeriod
}

type Notification {
  id: ID!
  title: String
  body: String
  image: String
  createdAt: DateTime
  showUntil: DateTime
  link: String
}

enum NotificationChannelPriority {
  MIN
  LOW
  DEFAULT
  HIGH
}

type NotificationInbox implements ContentSection {
  id: ID!
  notifications: [Notification!]!
}

type NotificationPreference {
  id: ID!
  channel: String!
  pushRequested: Boolean
  emailRequested: Boolean
  name: String!
  description: String
  channelPriority: NotificationChannelPriority!
  askPermissionMessage: String
}

type OccupiedSeat {
  id: ID!
  seatId: String
  seatStatus: SeatStatus!
  type: SeatType!
}

type OccupiedSeatingArea {
  id: ID!
  seatingAreaId: ID!
  numFreeSeats: Int!
  numUserSelectableSeats: Int!
}

type OnboardingContent {
  movies: [Movie!]!
  notifications: [Notification!]!
  bonusType: BonusType
}

enum OnboardingStep {
  REGISTRATION
  JOIN_BONUS_PROGRAM
}

type OnlineTicketingBooking {
  bookingId: String
  tickets: [OnlineTicketingBookingTicket!]
  auditorium: String
  ticketStatus: String
  ticketsQRCode: String
  isCancelable: Boolean
}

type OnlineTicketingBookingTicket {
  seatName: String
  rowName: String
  ticketNumber: String
  price: Float
  code: String
  isSubscriptionTicket: Boolean
  ticketStatus: String
  isCancelable: Boolean
  qrCode: String
  cancelUrl: String
}

type Order {
  id: ID!
  datetime: DateTime
  user: User!
  qrCode: String
  qrCodeImage: String
  status: OrderStatus!
  refundable: Boolean!
  pkpass: String
  lineItems: [LineItem!]!
  cinema: Cinema
  screening: Screening
  pickupCode: Int
  canStartPreparation: Boolean!
  startPreparationLink: String
}

enum OrderStatus {
  INITIAL
  REFUNDED
  REFUND_FAILED
  REFUND_WAITING
  PREPARATION
}

enum ParentStatus {
  PARENTS_ONLY
  NON_PARENTS_ONLY
}

type ParentStatusFilter {
  active: Boolean!
  status: ParentStatus
}

input ParentStatusFilterInput {
  active: Boolean!
  status: ParentStatus
}

input PayoutMissedFilter {
  from: Date
  to: Date
}

type PaypalBillingCycleConfig {
  sequence: Int!
  total_cycles: Int!
  frequency: PaypalFrequency!
  tenure_type: PaypalTenureType!
  pricing_scheme: PaypalPricingScheme!
}

type PaypalFixedPrice {
  value: String!
  currency_code: String!
}

type PaypalFrequency {
  interval_unit: String!
  interval_count: Int!
}

type PaypalPricingScheme {
  fixed_price: PaypalFixedPrice!
}

enum PaypalTenureType {
  TRIAL
  REGULAR
}

type PosApp {
  id: ID
  cinemaId: ID
  bonusProgramId: ID
}

type Price {
  id: ID!
  name: String
  description: String
  amount: Float
}

type PricingCategory {
  id: ID!
  name: String!
  description: String
  priceInCents: Int!
  maxNumTickets: Int
  validForUserId: String
  isSubscription: Boolean
}

type ProcessScannedQrCodeReturnType {
  bonusProgram: BonusProgram
  numberOfBookedPoints: Int
}

type ProvisionalBonusPointBooking {
  id: ID!
  scannedAt: DateTime!
  bonusProgram: BonusProgram!
  cinema: Cinema!
  potentialScreenings: [Screening!]!
}

type QRCodeScannerSection implements ContentSection {
  id: ID!
  buttonLabel: String!
  margin: String
  modalTitle: String
  modalDescription: String
  modalNoPermissionsDisclaimer: String
  modalRequestPersmissionsButtonLabel: String
  modalManualInputSeparator: String
  modalManualInputLabel: String
  modalManualInputHint: String
}

type Query {
  test(inputVal: String): String
  onboardingContent(bonusProgramId: ID): OnboardingContent
  appReleases(latestOnly: Boolean): [AppRelease!]!
  audioFingerprintingConfig: AudioFingerprintingConfig
  bonusPrograms(ids: [ID!]!): [BonusProgram!]!
  bonusProgram(id: ID!): BonusProgram
  checkVoucherCode(voucherCode: String!): CheckVoucherCodeReturnType! @deprecated(reason: "[6.1.0] use checkVoucherCodes i.e. plural instead")
  checkVoucherCodes(voucherCodes: [String!]): CheckVoucherCodesReturnType!
  voucherCatalog(cinemaId: ID!): VoucherCatalog @deprecated(reason: "[5.0.6] use BonusProgramMembership -> voucher Catalog instead (voucher catalog might be user specific)")
  campaign(id: ID): Campaign
  campaignsByCinemas(cinemaIds: [ID!]!, newCampaigns: Boolean): CampaignsByCinemasReturnType!
  estimateCampaignSize(campaignId: ID!): estimateCampaignSizeReturnType!
  searchCastOrCrewMember(fullName: String!): [CastOrCrewMember!]!
  currentCinema: Cinema
  cinema(id: ID!): Cinema
  cinemas(ids: [ID!], bonusProgramId: ID): [Cinema!]!
  companies: [Company!]!
  company(id: ID!): Company
  errorStatistics(pastDays: Int): [ErrorStatistics!]!
  geocode(query: String): GeocodeResult @deprecated(reason: "[5.0.11] use  instead")
  reverseGeocode(latitude: Float!, longitude: Float!): ReverseGeocodeResult @deprecated(reason: "[5.0.11] removed")
  locationForZipCode(zipCode: String): LocationForZipCodeResult
  movie(id: ID!): Movie
  movies(ids: [ID!]!): [Movie!]!
  allMovies(isFromMasterFilmDB: Boolean): [Movie!]!
  similarMovies(movieId: ID!, cinemaIds: [ID!]): [Movie!]!
  searchMovie(title: String!): [Movie!]!
  screenedMovies(cinemaIds: [ID!]!, from: DateTime, to: DateTime): [screenedMoviesReturnType!]!
  incomingForeignMovieIdentifiers(cinemaIds: [ID!]!, force: Boolean): [ForeignMovieIdentifier!]!
  newsItem(id: ID!): NewsItem
  notification(id: ID!): Notification!
  screeningAuditorium(screeningId: ID!, nRows: Int, nCols: Int): ScreeningAuditorium!
  externalUrl(appDeepLink: String!): String
  appDeepLink(externalUrl: String!): String
  screenings(cinemaIds: [ID!], movieIds: [ID!], before: DateTime, after: DateTime): [Screening!]!
  screening(id: ID!): Screening
  allScreeningAttributes: [ScreeningAttribute!]!
  ticket(id: ID!): Ticket
  order(id: ID!): Order
  movieList(id: ID!): MovieList
  allCinemaFilmSeries(cinemaIds: [ID!]!, before: DateTime): [MovieList!]!
  soonInCinemaItems(cinemaId: ID!): [SoonInCinemaItem!]!
  usageStatistics(cinemaIds: [ID!]): UsageStatistics
  movieStatistics: [MovieStatistics!]!
  subscriptionsStatistics(from: Date!, to: Date!): SubscriptionStatisticsResponse
  newSubscriptionStatistics(cinemaIds: [ID!]): NewSubscriptionStatisticsResponse!
  payoutInfo(from: Date!, to: Date!): [CinemaPayoutInfo!]!
  missedPayouts(filter: PayoutMissedFilter!): [MissedPayoutInfo!]!
  currentUser: User
  currentPOS: PosApp
  userByQr(qrCode: String!): User
  userById(id: ID!): User
  adminUsers: [User!]!
  searchUsers(queryText: String, filterActiveSubscriptions: Boolean, limit: Int, skip: Int): [User!]!
  searchUsersTotal(queryText: String, filterActiveSubscriptions: Boolean): Int!
  testing_getConfirmationCode(email: String!, type: ConfirmationCodeType!): String
  testing_forceDeleteUser(email: String!): DeleteUserReturnType
  allVoucherClassesOfCinemaOperatingCompany: [VoucherClass!]!
  getAvailableVoucherClasses: [VoucherClass!]!
  voucherInstanceByQR(qrCode: String!): VoucherInstance
  voucherInstancesByOrderNumber(orderNumber: String!): [VoucherInstance!]!
  discoverContent: ContentSectionList! @deprecated(reason: "[5.2.1] Use contentSectionListByNameAndCinemas(name:CURATED, cinemaIds:[...]) instead")
  contentSectionList(id: ID!): ContentSectionList
  contentSectionListByNameAndCinemas(name: ContentSectionListName!, cinemaIds: [ID!]!): ContentSectionList!
  newsListSection(id: ID!): NewsListSection
  getImdbIds(key: String!): [ComscoreImdbEntry!]!
  getServiceStatus: ServiceStatus!
  getOnlineTicketingBooking(ticketId: String, referenceUrl: String, ticketProvider: String, bookingId: String): OnlineTicketingBooking
  moviesToRateToInitializeRecommender(amount: Int!, sentEmailId: String!): [Movie]!
  emailComponent(identifier: ID!, componentName: EmailComponentName!, componentProps: Json!, brand: Brand!): EmailComponent!
  appLanguage(language: String): Language
  targetGroup(id: ID!): TargetGroup!
  allTargetGroupsByCinemaOperatingCompanies(cinemaOperatingCompanyIds: [ID!]!): TargetGroupsByCinemaOperatingCompanysReturnType!
  allGenres: [Genre!]!
  allPerks: [StatusLevelPerk!]!
  allAchievements: [Achievement!]!
  allImages: [Image!]!
  validateSubscriptionUsers(screeningId: ID!): ValidateSubscriptionUsersResponse
  subscriptionTiers(ids: [ID]): [SubscriptionTier!]
  invoice(id: ID!): Invoice!
  beacons: [Beacon!]!
  beaconById(id: ID!): Beacon
  cinemaCluster(id: ID!): CinemaCluster!
  cinemaClusters: [CinemaCluster!]!
}

type QuizReviewRating {
  id: ID!
  movie: Movie!
  value: Float!
  updatedAt: DateTime
}

type RecordTicketPurchaseReturnType {
  success: Boolean!
}

type RedeemedVoucher {
  id: ID
  name: String
  redeemedAt: DateTime
  amountPoints: Int
}

type redeemVoucherReturnType {
  success: Boolean!
}

type RegisterBeaconScannedReturnType {
  success: Boolean!
}

type rememberSkippedOnboardingStepReturnType {
  user: User
}

type ReportScreeningErrorResponse {
  nvc: String
}

type RequestCodeReturnType {
  user: User
}

type RequestPasswordResetReturnType {
  success: Boolean!
}

type resendActivationEmailReturnType {
  success: Boolean!
  sentToEmail: String
}

type ReverseGeocodeResult {
  route: String
  postalCode: String
  locality: String
  country: String
}

type ReviewRating {
  id: ID!
  movie: Movie!
  user: User!
  value: Float!
  updatedAt: DateTime
}

type SaveImagePathReturnType {
  success: Boolean
  uploadedImage: Image!
}

type screenedMoviesReturnType {
  id: ID!
  movie: Movie!
  firstScreening: DateTime!
}

type Screening {
  id: ID!
  movie: Movie
  cinema: Cinema
  datetime: DateTime
  onlineTicketingId: ID
  specialDescription: String @deprecated(reason: "[5.1.4]: use movieList.description instead")
  is3d: Boolean @deprecated(reason: "[5.1.3]: use attributes instead")
  is2d: Boolean @deprecated(reason: "[5.1.3]: use attributes instead")
  isOV: Boolean @deprecated(reason: "[5.1.3]: use attributes instead")
  isSpecial: Boolean @deprecated(reason: "[5.1.3]: use movieList instead")
  isLastChance: Boolean
  traileredMovies: [Movie!]!
  movieList(cinemaIds: [ID!]!): MovieList
  attributes: [ScreeningAttribute!]!
  auditoriumName: String
}

type ScreeningAttribute {
  id: ID!
  label: String
  icon: String
  image: String
  type: ScreeningAttributeType
  sectionTitle: String!
  description: String
  name: String! @deprecated(reason: "[5.1.4]: use label instead")
  displayType: ScreeningAttributeDisplayType @deprecated(reason: "[5.1.4]: use type instead")
}

enum ScreeningAttributeDisplayType {
  TOP
  BOTTOM
}

input ScreeningAttributeInput {
  id: ID!
  label: String
}

enum ScreeningAttributeType {
  SCREENING
  AUDITORIUM
  MOVIE
}

type ScreeningAuditorium {
  id: ID!
  name: String
  seats: [ScreeningAuditoriumSeat!]!
  seatingAreas: [ScreeningAuditoriumSeatingArea!]!
  seatSelectionType: SeatSelectionType!
}

type ScreeningAuditoriumSeat {
  id: ID!
  seatId: String!
  rowName: String!
  seatName: String
  x: Float!
  y: Float!
  width: Float!
  height: Float!
  type: SeatType!
  seatingAreaId: String!
}

type ScreeningAuditoriumSeatingArea {
  id: ID!
  name: SeatingAreaName!
  pricingCategories: [PricingCategory!]!
  color: String!
}

enum ScreeningStatusType {
  CURRENT
  SOON
  NONE
}

input SDKLoginInput {
  cookieId: String!
  datetime: DateTime!
  userId: String
}

type Seat {
  id: ID!
  auditorium: Auditorium!
  rowName: String!
  seatName: String
  indexInRow: Int!
  x: Float!
  y: Float!
  width: Float!
  height: Float!
  type: SeatType!
  status: SeatStatus
  categoryId: ID!
  neighborLeftId: ID
  neighborRightId: ID
}

type SeatCategory {
  id: ID!
  name: String
  prices: [Price!]!
}

enum SeatingAreaName {
  LOGE
  PARKETT
}

input SeatPricingCategoryInput {
  seatId: ID!
  pricingCategoryId: ID!
}

input SeatSelection {
  seatNumber: Int!
  seatRow: Int!
}

enum SeatSelectionType {
  FIXED_SEATING
  SEATING_AREA
}

enum SeatStatus {
  OCCUPIED
  BLOCKED
  INACTIVE
}

enum SeatType {
  STANDARD_SEAT
  WHEEL_CHAIR_SEAT
  LOVE_SEAT_LEFT
  LOVE_SEAT_RIGHT
}

type SeenInCinema {
  id: ID!
  movie: Movie!
  user: User!
  updatedAt: DateTime
}

type SeenMovies {
  movie: Movie!
  numVisits: Int!
}

type selectCinemaReturnType {
  cinemaCustomership: CinemaCustomership!
}

type selectCinemasReturnType {
  cinemas: [Cinema!]!
  user: User!
}

type SelectedSeat {
  id: ID!
  seatId: String
  seatingAreaId: String!
  pricingCategoryId: String
  priceInCents: Int
  validForUserId: String
  usedSubscription: Boolean
}

type SendTestEmail {
  success: Boolean!
}

type SendTestNotificationReturnType {
  success: Boolean
}

type SentMessages {
  numberOfCountedMessages: Int
  numberOfFreeMessages: Int
}

type ServiceStatus {
  type: ServiceStatusType!
  text: String
}

enum ServiceStatusType {
  OPERATIONAL
  PARTIAL
  DOWN
}

type SetCampaignStatusReturnType {
  campaign: Campaign
}

type SetInitialRatedMoviesReturnType {
  success: Boolean!
}

input ShowtimesCampaignData {
  title: String!
  body: String!
  image: String
  targetType: String!
  targetMovieId: String
}

enum SocialAuthProvider {
  FACEBOOK
  APPLE
  GOOGLE
}

type SoonInCinemaItem {
  id: ID!
  movie: Movie
  movieTitle: String
  startDate: Date
}

type SponsoredBy {
  name: String!
  logo: String!
}

type StartPreparationReturnType {
  order: Order
  ticket: Ticket
}

enum StatisticDataType {
  MOVIE
  SUBSCRIPTION
  TICKET
}

enum StatisticGroupBy {
  DAY
  WEEK
  MONTH
}

type StatusLevel {
  id: ID!
  number: Int!
  name: String!
  pointsNeeded: Int!
  nextLevel: StatusLevel
  perks: [StatusLevelPerk!]!
}

type StatusLevelFilter {
  active: Boolean!
  minStatusLevel: Int!
  maxStatusLevel: Int
}

input StatusLevelFilterInput {
  active: Boolean!
  minStatusLevel: Int!
  maxStatusLevel: Int
}

type StatusLevelPerk {
  id: ID!
  title: String
  description: String
  image: String
  name: String @deprecated(reason: "[5.1.16] use title instead")
}

type StatusPointsFilter {
  active: Boolean!
  minStatusPoints: Int!
  maxStatusPoints: Int
}

input StatusPointsFilterInput {
  active: Boolean!
  minStatusPoints: Int!
  maxStatusPoints: Int
}

type StickersReceivedFilter {
  active: Boolean!
  stickers: [Achievement!]!
}

input StickersReceivedFilterInput {
  active: Boolean!
  stickers: [ID!]!
}

type StoreConsentReturnType {
  success: Boolean!
}

type StoreSDKLoginReturnType {
  success: Boolean!
}

type StoreSeatPlanReturnType {
  success: Boolean!
}

type SubmitAppFeedbackReturnType {
  user: User!
  success: Boolean!
}

type SubmitContactFormReturnType {
  success: Boolean
}

type SubscribeResponse {
  approveLink: String
  infoText: String @deprecated(reason: "not used anymore, but we need to keep it for backwards compatibility of old apps")
}

type SubscribeToNewsletterReturnType {
  success: Boolean!
}

type Subscription {
  id: ID!
  subscriptionTier: SubscriptionTier!
  validFrom: Date
  payedUntil: Date
  blocked: Boolean
  active: Boolean
  canceled: Boolean
  company: Company
  noShowWarningText: String
  blockedText: String
  blockedReason: BlockedReason
  cancellationEffectiveDate: Date
  paypalOrderId: String
  paypalStatus: String
}

type SubscriptionStatisticsResponse {
  numberOfUsers: Int!
  numberOfActiveSubscriptions: Int!
  numberOfSubscriptionTickets: Int!
  numberOfNewSubscriptions: Int!
  canceledSubscriptions: Int!
  numberOfUsersPerTier: [TierStatistics!]!
  visitFrequency: VisitFrequency
  seenMovies: [SeenMovies!]!
}

type SubscriptionTier {
  id: ID!
  name: String!
  description: String!
  details: String
  paypalBillingPlanId: String!
  billingCycleConfigs: [PaypalBillingCycleConfig!]!
  isAutoRenewal: Boolean!
}

type SuggestCinemaReturnType {
  success: Boolean
}

type TargetGroup {
  id: ID!
  name: String
  createdDatetime: DateTime
  lastEditedDatetime: DateTime
  cinemas: [Cinema!]!
  cinemaOperatingCompanies: [CinemaOperatingCompany!]!
  cluster: [TargetGroupCluster!]!
  targetGroupSize: Int
  status: TargetGroupStatus
}

type TargetGroupCluster {
  id: ID!
  position: Int!
  targetGroupId: ID!
  name: String
  clusterSize: Int
  createdDatetime: DateTime
  lastEditedDatetime: DateTime
  ageFilter: AgeFilter
  gendersFilter: GendersFilter
  parentStatusFilter: ParentStatusFilter
  longTermBenefitsReceivedFilter: LongetermBenefitsReceivedFilter
  distanceFilter: DistanceFilter
  movieAffinityFilter: MovieAffinityFilter
  genreAffinityFilter: GenreAffinityFilter
  castOrCrewMemberAffinityFilter: CastOrCrewMemberAffinityFilter
  movieAttributesAffinityFilter: MovieAttributesAffinityFilter
  moviesSeenFilter: MoviesSeenFilter
  moviesNotSeenFilter: MoviesNotSeenFilter
  moviesOnWatchListFilter: MoviesOnWatchListFilter
  bonusProgramMemberStatusFilter: BonusProgramMemberStatusFilter
  bonusPointsFilter: BonusPointsFilter
  statusPointsFilter: StatusPointsFilter
  statusLevelFilter: StatusLevelFilter
  vouchersReceivedFilter: VouchersReceivedFilter
  vouchersRedeemedFilter: VouchersRedeemedFilter
  stickersReceivedFilter: StickersReceivedFilter
  averageCinemaVisitsFilter: AverageCinemaVisitsFilter
  noCinemaVisitSinceFilter: NoCinemaVisitSinceFilter
  cinemaVisitSinceFilter: CinemaVisitSinceFilter
  averageGroupSizesFilter: AverageGroupSizesFilter
}

input TargetGroupClusterInput {
  id: ID!
  position: Int!
  targetGroupId: ID!
  name: String
  createdDatetime: DateTime
  lastEditedDatetime: DateTime
  clusterSize: Int
  ageFilter: AgeFilterInput
  gendersFilter: GendersFilterInput
  parentStatusFilter: ParentStatusFilterInput
  longTermBenefitsReceivedFilter: LongetermBenefitsReceivedFilterInput
  distanceFilter: DistanceFilterInput
  movieAffinityFilter: MovieAffinityFilterInput
  genreAffinityFilter: GenreAffinityFilterInput
  castOrCrewMemberAffinityFilter: CastOrCrewMemberAffinityFilterInput
  movieAttributesAffinityFilter: MovieAttributesAffinityFilterInput
  moviesSeenFilter: MoviesSeenFilterInput
  moviesNotSeenFilter: MoviesNotSeenFilterInput
  moviesOnWatchListFilter: MoviesOnWatchListFilterInput
  bonusProgramMemberStatusFilter: BonusProgramMemberStatusFilterInput
  bonusPointsFilter: BonusPointsFilterInput
  statusPointsFilter: StatusPointsFilterInput
  statusLevelFilter: StatusLevelFilterInput
  vouchersReceivedFilter: VouchersReceivedFilterInput
  vouchersRedeemedFilter: VouchersRedeemedFilterInput
  stickersReceivedFilter: StickersReceivedFilterInput
  averageCinemaVisitsFilter: AverageCinemaVisitsFilterInput
  noCinemaVisitSinceFilter: NoCinemaVisitSinceFilterInput
  cinemaVisitSinceFilter: CinemaVisitSinceFilterInput
  averageGroupSizesFilter: AverageGroupSizesFilterInput
}

type TargetGroupFilterConfig {
  name: String!
  showFilter: Boolean!
}

input TargetGroupFilterConfigInput {
  name: String!
  showFilter: Boolean!
}

type TargetGroupsByCinemaOperatingCompanysReturnType {
  targetGroups: [TargetGroup!]!
}

enum TargetGroupStatus {
  NOT_IN_USE
  IN_USE
  ARCHIVED
}

enum TestingStatus {
  TESTING
  PRODUCTION
  STAGING
  DEVELOPMENT
  CINEMA_EMPLOYEE
}

type TestUserTokenReturnType {
  emailToken: String
  passwordToken: String
}

type Ticket {
  id: ID!
  screening: Screening!
  user: User!
  seats: [Seat!]!
  qrCode: String
  boughtAt: DateTime
  status: TicketStatus!
  refundedAt: DateTime
  refundable: Boolean!
  vouchers: [TicketVoucher!]
  onlineTicketingId: ID
  pkpass: String
  googlePayPass: String
  displayAllowedAfter: DateTime
  isFlatrateTicket: Boolean
  sponsoredBy: SponsoredBy
  auditoriumName: String
  cancelationLink: String
  provider: TicketProvider
  scanned: Boolean
  bluetoothRequired: Boolean
}

enum TicketProvider {
  CINETIXX
  KINOHELD
  MARS
  TICKET_INTERNATIONAL
  COMPESO
}

enum TicketStatus {
  RESERVATION
  COMPLETE
  REFUNDED
  REFUND_FAILED
  REFUND_WAITING
  PREPARATION
  UNKNOWN
}

type TicketVoucher {
  image: String
  qrCode: String!
}

type TierStatistics {
  tierName: String!
  numActiveSubscribers: Int!
}

scalar Time

enum TimePeriod {
  DAY
  WEEK
  MONTH
  YEAR
}

type Trailer {
  id: ID!
  hostingProvider: HostingProvider
  externalId: String
  releaseDate: Date
  movie: Movie!
}

type UpdateBonusProgramReturnType {
  success: Boolean!
  bonusProgram: BonusProgram
}

type UpdateCampaignsStatusReturnType {
  campaigns: [Campaign!]!
}

input UpdateCinemaInput {
  id: ID!
  name: String!
  logo: String
  claim: String
  hasBonusProgram: Boolean
  headerImage: String
  imprint: String
  technologies: String
  history: String
  specialAboutUs: String
  currentInformation: String
  onlineTicketingBaseUrl: String
  ticketProvider: String
  street: String
  houseNumber: String
  zipCode: String
  city: String
  parkingDescription: String
  locationDescription: String
  latitude: String
  longitude: String
  location: CinemaLocationInput
  googleMapsLink: String
  appleMapsLink: String
  phone: String
  facebook: String
  twitter: String
  instagram: String
  tiktok: String
  website: String
  email: String
  barrierFree: Boolean
  barrierFreeText: String
  hearingImpaired: Boolean
  hearingImpairedText: String
  blind: Boolean
  blindText: String
  openingHoursDisplay: [CinemaOpeningHoursInput!]!
  pricesDisplay: [CinemaInformationParagraphInput!]!
  specialOffersDisplay: [CinemaInformationParagraphInput!]!
  giftVouchersDisplay: [CinemaInformationParagraphInput!]!
  sneaksDisplay: [CinemaInformationParagraphInput!]!
  brands: [Brand!]!
  accessRestrictedTo: AccessRestriction!
}

type updateCinemaOperatingCompanyTargetGroupFilterConfigReturnType {
  success: Boolean!
}

type updateCinemaReturnType {
  cinema: Cinema
  success: Boolean!
}

type UpdatedSoonInCinemaItemReturnType {
  soonInCinemaItem: SoonInCinemaItem
  success: Boolean!
}

enum UpdatedTargetGroupStatus {
  NOT_IN_USE
  ARCHIVED
}

type updateExternalNewsletterPreferenceReturnType {
  externalNewsletterPreference: ExternalNewsletterPreferences
}

type updateNotificationPreferenceReturnType {
  notificationPreference: NotificationPreference
}

type UpdateScreeningReturnType {
  screening: Screening
  success: Boolean!
}

type UpdateTargetGroupsStatusReturnType {
  targetGroups: [TargetGroup!]!
}

type updateUserAdminStatusReturnType {
  success: Boolean!
  user: User
}

type upsertAppReturnType {
  app: App
}

type upsertInterestRatingReturnType {
  interestRating: InterestRating
}

type upsertNewsItemReturnType {
  newsItem: NewsItem
  user: User
}

type upsertReviewRatingReturnType {
  reviewRating: ReviewRating
}

type UpsertSeenInCinemaReturnType {
  seenInCinema: SeenInCinema
}

type UsageStatistics {
  id: ID!
  sentMessagesInInterval(from: Date, to: Date): SentMessages
  installationsInInterval(from: Date, to: Date): Int
  watchlistUsedInInterval(from: Date, to: Date): Int
  activeUsersInInterval(from: Date, to: Date): Int
}

type User {
  externalNewsletterPreferences(cinemaIds: [ID!]): [ExternalNewsletterPreferences!]
  id: ID!
  name: String
  fullName: String
  active: Boolean @deprecated(reason: "[3.3.1] use blocked instead")
  blocked: Boolean!
  blockedReason: UserBlockedReason
  blockedText: String
  email: String
  selectedCinemas: [Cinema!] @deprecated(reason: "[4.0.15] use cinemaCustomerships instead")
  cinemaCustomerships(currentlySelectedOnly: Boolean): [CinemaCustomership!]!
  watchlist(cinemaIds: [ID!], lastChance: Boolean): MovieList!
  ratedlist: MovieList!
  seenInCinemaList: MovieList!
  reviewRatings: [ReviewRating!]! @deprecated(reason: "[5.2.3] use ratedlist instead")
  interestRatings(removeReviewRatedMovies: Boolean): [InterestRating!]! @deprecated(reason: "[5.2.3] use watchlist instead")
  bonusProgramMembership(bonusProgramId: ID, cinemaId: ID): BonusProgramMembership
  tickets(cinemaIds: [ID!], openOnly: Boolean, sync: Boolean): [Ticket!]!
  orders(cinemaIds: [ID!], openOnly: Boolean, sync: Boolean): [Order!]!
  bonusProgramMemberships: [BonusProgramMembership!]!
  onlineTicketingToken: String
  testingStatus: TestingStatus
  termsStatus: AppTermsStatus
  dataUsageAllowed: Boolean
  marketingEmailsAllowed: Boolean
  notificationPreferences: [NotificationPreference!]!
  privileges: UserPrivileges!
  notifications: [Notification!]
  anonymous: Boolean!
  askForAppFeedback: Boolean!
  askToRegister: Boolean!
  askToJoinBonusProgram: Boolean!
  zipCode: String
  gender: Gender
  birthDate: Date
  firstName: String
  lastName: String
  street: String
  houseNumber: String
  city: String
  country: String
  lastSync: DateTime
  telephone: String
  subscriptions: [Subscription!]!
  invoices: [Invoice!]!
  vouchers(onlyRedeemed: Boolean): [VoucherInstance!]!
  appTrackingAllowed: Boolean
  watchlistInCinema: MovieList!
  linkedAccounts: [LinkedAccount!]!
}

enum UserBlockedReason {
  MISSING_EMAIL_VERIFICATION
  WRONG_EMAIL
  OTHER_ACCOUNT_EXISTED
  ANONYMOUS_USER_LOGGED_OUT
  OTHER
}

input UserGroupDefinition {
  clientOfCinemasAny: [ID!]
}

type UserGroupFilter {
  id: ID!
  name: String
  moviesOnWatchlist: [Movie!]
  moviesSeen: [Movie!]
  excludeIfMovieSeen: [Movie!]
  moviesNotSeen: [Movie!]
  moviesRatedPositively: [Movie!]
  moviesRatedNegatively: [Movie!]
  bonusPointsGeq: Int
  bonusPointsLeq: Int
  visitFrequency: VisitFrequency
}

input UserGroupFilterInput {
  id: ID!
  name: String
  moviesOnWatchlistIds: [ID!]
  moviesSeenIds: [ID!]
  moviesNotSeenIds: [ID!]
  moviesRatedPositivelyIds: [ID!]
  moviesRatedNegativelyIds: [ID!]
  bonusPointsGeq: Int
  bonusPointsLeq: Int
  visitFrequencyVisits: Int
  visitFrequencyIntervalLengthDays: Int
}

type UserGroupFilterOnReferencedMovie {
  id: ID!
  name: String
  onWatchlist: Boolean
  seen: Boolean
  ratedPositively: Boolean
  ratedNegatively: Boolean
}

input UserGroupFilterOnReferencedMovieInput {
  id: ID!
  name: String
  onWatchlist: Boolean
  seen: Boolean
  ratedPositively: Boolean
  ratedNegatively: Boolean
}

type UserPrivileges {
  belongsToCinemaOperatingCompanies: [CinemaOperatingCompany!]!
  adminForCinemas: [Cinema!]!
  adminForBonusPrograms: [BonusProgram!]!
  accessRightDashboard: Boolean!
  accessRightFilmStatistics: Boolean!
  accessRightBonusProgram: Boolean!
  accessRightCampaigns: Boolean!
  adminRole: Boolean!
  rootRole: Boolean!
  supportRole: Boolean!
}

type UserTokenResponse {
  userToken: String!
}

type ValidatedUser {
  userId: ID!
  userName: String
  allowed: Boolean
  errorMessage: String
}

type ValidateSubscriptionUsersResponse {
  maxLinkedAccounts: Int!
  validatedUsers: [ValidatedUser!]!
}

type ValidateVoucherCodeResponse {
  title: String
  description: String
  company: Company
}

type VisitFrequency {
  zero: Int!
  one: Int!
  two: Int!
  three: Int!
  four: Int!
  five: Int!
  sixToTen: Int!
  elevenToTwenty: Int!
  twentyPlus: Int!
  id: ID!
  visits: Int!
  intervalLengthDays: Int!
}

type VoucherCatalog {
  voucherClasses: [VoucherClass!]!
}

type VoucherClass {
  id: ID!
  image: String
  iconName: String
  title: String
  description: String
  initialCapacity: Int
  remainingCapacity: Int
  maxQuantityPerPerson: Int
  priceCoins: Int
  priceEuros: Float
  discount: Int
  bookableUntil: DateTime
  redeemableUntil: DateTime
  autoRedeem: Boolean
  cinema: Cinema
  bonusProgram: BonusProgram!
  minStatusPoints: Int
}

type VoucherInstance {
  id: ID!
  voucherClass: VoucherClass
  company: Company
  companyId: ID
  user: User
  createdDatetime: DateTime
  redeemedDatetime: DateTime
  valid: Boolean
  qrCode: String
  redeemable: Boolean
  descriptionOverwrite: String
  reason: String
}

type VouchersReceivedFilter {
  active: Boolean!
  vouchers: [VoucherClass!]!
}

input VouchersReceivedFilterInput {
  active: Boolean!
  vouchers: [ID!]!
}

type VouchersRedeemedFilter {
  active: Boolean!
  vouchers: [VoucherClass!]!
}

input VouchersRedeemedFilterInput {
  active: Boolean!
  vouchers: [ID!]!
}
