BEGIN;

CREATE TABLE priv.p_cinema_cluster (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    position INTEGER NOT NULL UNIQUE
);

CREATE TABLE priv.p_cinema_cluster_cinema (
    cinema_cluster_id INTEGER NOT NULL REFERENCES priv.p_cinema_cluster(id) ON DELETE CASCADE,
    cinema_id INTEGER NOT NULL REFERENCES priv.p_cinema(id) ON DELETE CASCADE,
    PRIMARY KEY (cinema_cluster_id, cinema_id)
);

--DROP TABLE priv.p_cinema_cluster_cinema;
--DROP TABLE priv.p_cinema_cluster;

COMMIT;
