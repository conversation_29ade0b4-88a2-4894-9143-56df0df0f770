import { Brand } from '@cinuru/utils';
import { PublicMovieId } from './src/typescript-types';

// This STAGE variable is set by serverless via the serverless.yml file

export const NODE_ENV = process.env.NODE_ENV;
export const ENV = process.env.ENV || 'development'; // use this to distinguish development/staging/production
export const STAGE = process.env.STAGE || 'development';

export const APP_LINK_SCHEME = process.env.APP_LINK_SCHEME || 'cinurudev://';

export const DB_CONF = {
	user: process.env.PGUSER || 'cinuru',
	host: process.env.PGHOST || 'test-env.cxbf41ddiwk5.eu-central-1.rds.amazonaws.com',
	database: process.env.PGDATABASE || 'cinuru_staging',
	port: Number(process.env.PGPORT) || 5432,
	//Allow to specify empty passwords in .env.development for local connections
	password:
		process.env.PGPASSWORD !== undefined ? process.env.PGPASSWORD : 'aWPCVHmAmQcqDEviA9xqtpzC',
	ssl: process.env.PGSSLMODE === 'disable' ? undefined : { rejectUnauthorized: true },
	max: process.env.NUM_DB_CONNECTIONS
		? Number(process.env.NUM_DB_CONNECTIONS)
		: process.env.NODE_ENV === 'test'
		? 1
		: 3,
	query_timeout: Number(process.env.QUERY_TIMEOUT) || 10000,
};

export const GRAPHQL_API_URL = process.env.GRAPHQL_API_URL || 'http://localhost:3000';
export const FILESERVER_API_URL = process.env.FILESERVER_API_URL || 'https://static.cinuru.com';

export const DEFAULT_APP_LANGUAGE = process.env.DEFAULT_APP_LANGUAGE || 'de-cinuru';
export const DEFAULT_APP_BRAND = DEFAULT_APP_LANGUAGE.split('-')[1].toUpperCase() as Brand;
export const IsCinfinity = ['CINFINITY', 'CINFINITY-WEB'].includes(DEFAULT_APP_BRAND);

export const ENABLE_MOCK_APIS = Boolean(process.env.ENABLE_MOCK_APIS) || false;
export const USE_REDIS = process.env.USE_REDIS === 'true' || false;
export const REDIS_HOST = process.env.REDIS_HOST || '127.0.0.1';
export const REDIS_PORT = process.env.REDIS_PORT;
export const REDIS_PASSWORD = process.env.REDIS_PASSWORD;
export const REDIS_TLS = process.env.REDIS_TLS || false;
export const REDIS_PREFIX = process.env.REDIS_PREFIX || 'dev';

export const TEST_USER_PASSWORD_TEMPLATE = 'youShallNotPass_kfcqDEviA9xqtpzC';
export const TEST_USER_EMAIL_TEMPLATE = '<EMAIL>';
export const RUN_CINEPLEX_TESTS = process.env.RUN_CINEPLEX_TESTS === 'true' ? true : false;
export const TEST_SEATPLAN_SCRAPING = false;
export const IS_PRODUCTION = process.env.IS_PRODUCTION ? true : false;

export const SECRET_IMAGE_UPLOAD_KEY = process.env.SECRET_IMAGE_UPLOAD_KEY;

export const RATE_LIMIT = process.env.RATE_LIMIT ? Number(process.env.RATE_LIMIT) : 2;
export const LOG_DB_ERRORS = process.env.LOG_DB_ERRORS || 'QUERY';
export const PREVENT_EMAIL_SENDING =
	process.env.PREVENT_EMAIL_SENDING === 'true'
		? true
		: process.env.PREVENT_EMAIL_SENDING === 'NONE'
		? 'NONE'
		: false;

export const USE_LOCAL_DB = process.env.USE_LOCAL_DB || false;
export const JWT_SECRET = process.env.JWT_SECRET || 'ngsurndne899usidaen0h824h';
export const FILESERVER_JWT_SECRET =
	process.env.FILESERVER_JWT_SECRET || 'nlahlkshlfpjlweo99usidaen0h824h';
export const COOKIE_SECRET = process.env.COOKIE_SECRET || 'dfsu9i8asedfutc08e8undtrneui90ae';
export const GRAPHQL_API_PORT = 3000;
export const CONTACT_EMAIL = '<EMAIL>';
export const ERROR_MESSAGE_EMAIL = '<EMAIL>'; // DEV_DO_NOT_STORE_NOTIFICATIONS :true
export const APOLLO_ENGINE_API_KEY = process.env.APOLLO_ENGINE_API_KEY;

export const NOTIFICATIONS_SENDER_API_KEY: string =
	process.env.NOTIFICATIONS_SENDER_API_KEY || 'dutirane9u79iase8vuhglisern';
export const SHOWTIME_ANALYTICS_AUTH_KEY: string =
	process.env.SHOWTIME_ANALYTICS_AUTH_KEY ||
	'dutrane9sn4ghbDTRNa7px7os384qvseßbxmuvl7i8eaNNRBZsnbutdiraentdgunia9e83vlniuaenr8';
export const INTEGRATION_TEST_USER_EMAIL = '<EMAIL>';
export const INTEGRATION_TEST_USER_PASSWORD = 'EudtriaUX4dtrnuna';
export const INTEGRATION_TEST_USER_ID_DB = 7735;
export const INTEGRATION_TEST_ADMIN_USER_EMAIL = '<EMAIL>';
export const INTEGRATION_TEST_ADMIN_USER_PASSWORD = 'fgn8ctrnc98';

export const INTEGRATION_TEST_CINEMA_ID = 'Q2luZW1hOjEy';
export const INTEGRATION_TEST_CINEMA_NAME = 'Schwarz-Weiß';
export const INTEGRATION_TEST_CINEMA_BONUS_PROGRAM_ID = 'Qm9udXNQcm9ncmFtOjEy'; // Schwarz Weiss cinemab Qm9udXNQcm9ncmFtOjEy
export const INTEGRATION_TEST_CINEMA_POS_AUTH_TOKEN = 'mahelias-test-device-9848n09trn9';

export const INTEGRATION_TEST_CINEMA_TICKET_INTERNATIONAL_ID = 'Q2luZW1hOjU1';
export const INTEGRATION_TEST_CINEMA_BONUS_PROGRAM_TICKET_INTERNATIONAL_ID = 'Qm9udXNQcm9ncmFtOjUz';
export const INTEGRATION_TEST_CINEMA_TICKET_INTERNATIONAL_POS_AUTH_TOKEN = 'euehR-VnMAw-NSwLY';

export const INTEGRATION_TEST_EXAMPLE_MOVIE: PublicMovieId = 'TW92aWU6MQ==';

export const SHARC_TRAILER_BASE_URL = 'https://broker.sharc.de/clips/';

export const CINURUFILE_BASE_URL = 'https://static.cinuru.com/public/'; // On changing this line, also change the base url in the main movie query as the cinurufile_base_url is hardcoded there as well (we cannot do it differently there)

export const GOOGLE_WEB_CLIENT_ID_BY_LANGUAGE = {
	'de-cinuru': '772188462629-4n1o5fq1f96k6n299tbphpeschg065pi.apps.googleusercontent.com',
};
export const APPLE_SIGNIN_CLIENT_IDS = [
	'com.cinuru.app',
	'com.cinuru.app.debug',
	'com.cinuru.app.staging',
	'com.cinuru.web',
	'de.cineplex.app',
	'de.cineplex.app.staging',
	'de.cineplex.website',
	'de.cineplex.androidapp',
];

export const CINEPLEX_LOGIN_CLIENT_ID = '4a64660b-979a-4fb4-a671-909d2d943f0f';
export const CINEPLEX_LOGIN_PUBLIC_KEY = IS_PRODUCTION
	? `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlf/EtjoRaiZd40lOcJXp
NGs3XiThZP5k5PZYf+jt00s0Runq+iryH/RIyyK/ouQzGY4sDxMy5yNkLKWVnZHC
vuh78w5/RUE7g8oRKBJdtY6/eEiqCK97reTOqlH0YnoivQ6RYKj9lY2iX/bVOcGi
eU2Psm12o/Nl9hQyo1c12O8amaeVCt4JBEqQZnG0Ajo41nd7YH9E0WZh+dosWJwu
mZdDsYBQMc8E4I0zunLTCBQH3SYinhCYKkbuvmlY7xJgsW8/2A28kzVd6uG95uBB
kn2obRil1RJyGy3nT2aBb4ikiTR+0w+7hFkwEtXS5GLTjjCFK33TvQ+sTrQTyL1o
AwIDAQAB
-----END PUBLIC KEY-----`
	: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkmNsAazVdX4HQmxai++o
ltph511OSBqGEdudbnHRLx1QQMvCDc1UM/Jypp6hC1Qn7y0rXWXhj7SDQVBbRYWE
aMdHgHfssaIJ3bRgygAc3nvnbOmsVFcEVw+b4MdlsCpSmqR8TneSlYbfWUPCjkI2
W77PutU6Vxl9QiNJcrQt5O4JxRZRm9XJi6SkGjrf5O17tBGhC45iWI/ab2gwM9iW
zXzo9FSRrDqNXHkxZvor6X1fkeL0A/oV9x0CcXIVhQOn3tujJARuyEAYqOHJ/vHC
/n84oYQkzXHTO5psg7F4JS1+36SUDi2D76WZIHYiTxmFCR/Bun28Z2wcVHzeY3PF
PQIDAQAB
-----END PUBLIC KEY-----`;
export const CINEPLEX_OLD_CUSTOMER_SERVICE_URL =
	process.env.CINEPLEX_OLD_CUSTOMER_SERVICE_URL || 'https://uat.cineplex.de/booking/TicketBoxXNG';

export const CINEPLEX_LOGIN_SERVICE_URL =
	process.env.CINEPLEX_LOGIN_SERVICE_URL || 'https://buchung-uat.cineplex.de/gateway/auth';
export const CINEPLEX_CUSTOMER_DB_API_URL =
	process.env.CINEPLEX_CUSTOMER_DB_API_URL || 'https://api-app-uat.cineplex.de/api/v1';
export const CINEPLEX_BOOKING_SERVICE_URL =
	process.env.CINEPLEX_BOOKING_SERVICE_URL || 'https://booking-uat.cineplex.de/TicketBoxXNG';

export const CINEPLEX_MATOMO_URL = 'https://ost.cineplex.de/piwik.php';
export const CDN_BASE_URL = process.env.CDN_BASE_URL || 'https://cdn-uat.cineplex.de';

//export const CINEPLEX_CUSTOMER_DB_API_URL =
//'https://centralcustomerservice-sit.azurewebsites.net/api/v1';
export const CINPLEX_CUSTOMER_UPDATES_OUR_API_KEY = 'dutiranedtiunraedtruniaedtrniutadneduitae';
export const ENGAGE_OUR_API_KEY = process.env.ENGAGE_OUR_API_KEY;
export const ENGAGE_THEIR_API_KEY = process.env.ENGAGE_THEIR_API_KEY || '3slcudaenburinabedrtniae';
export const ENGAGE_CALLBACK_API_URL =
	process.env.ENGAGE_CALLBACK_API_URL || 'http://localhost:3000';

export const CINEPLEX_CUSTOMER_SERVICE_API_KEY =
	process.env.CINEPLEX_CUSTOMER_SERVICE_API_KEY || 'ABCDEFG1234';
export const CINEPLEX_DELETE_USER_API_KEY =
	process.env.CINEPLEX_DELETE_USER_API_KEY || 'odvf4zQgwkKDQdEf6GGck4axmZQY2UiWPUvmA';

export const CINEPLEX_MATOMO_SITE_ID = 2;
export const CINEPLEX_MATOMO_AUTH_TOKEN = 'e6be8bc614ef3c1917825d9a905c32ac';

export const CINEORDER_API_URL =
	process.env.CINEORDER_API_URL || 'https://cpd-web-az-uat-ol.cpx.local/api/v1';
export const CINEORDER_API_USER_NAME =
	process.env.CINEORDER_API_USER_NAME || 'a7b8bf88-dee8-4bae-8a5b-491087ff4c9c';
export const CINEORDER_API_PASSWORD =
	process.env.CINEORDER_API_PASSWORD || 'd8fe544c-8604-4ffa-81bc-e00bf87217d0';

export const SYNC_USER_FUNCTION_NAME = process.env.SYNC_USER_FUNCTION_NAME || 'syncCineplexUser';

export const ANALYTICS_OUR_API_KEY = process.env.ANALYTICS_OUR_API_KEY;

export const USE_MOCK_DATA = process.env.USE_MOCK_DATA === 'true' || false;
export const MOCK_DATA_IDS = {
	cinemaIds: [3],
	trailerList: [5683],
	watchlistInCinema: [4974, 5372, 4916, 4595],
	nowInCinema: [4334, 4624, 4327, 5961],
	screenings: [
		{ movieId: 4974, times: ['16:30', '19:00', '21:00'] },
		{ movieId: 5372, times: ['18:30', '21:30'] },
		{ movieId: 4916, times: ['15:30', '18:00', '20:00'] },
		{ movieId: 4595, times: ['20:00', '22:00'] },
		{ movieId: 4334, times: ['14:30', '17:00'] },
		{ movieId: 4624, times: ['16:45'] },
	],
	bonusCurrencyId: 3,
};

export const RATE_LIMIT_OPTIONS = {
	interval: { m: 0, s: 10 },
	maxReqAmount: 50,
};

export const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID;
export const PAYPAL_APP_SECRET = process.env.PAYPAL_APP_SECRET;
export const PAYPAL_API_BASE_URL =
	process.env.PAYPAL_API_BASE_URL || 'https://api-m.sandbox.paypal.com';
export const PAYPAL_CURRENCY = 'EUR';
export const PAYPAL_WEBHOOK_ROUTE = 'paypal-webhook';

export const LOCAL_TUNNEL_SUBDOMAIN =
	process.env.LOCAL_TUNNEL_SUBDOMAIN || 'beep-vestige-fovea-burlap-situated-spurt-wicket-twiddle';
export const LOCAL_TUNNEL_URL = `https://${LOCAL_TUNNEL_SUBDOMAIN}.loca.lt/paypal-webhook`;
export const PAYPAL_WEBHOOK_URL =
	STAGE === 'development' ? LOCAL_TUNNEL_URL : `${GRAPHQL_API_URL}/${PAYPAL_WEBHOOK_ROUTE}`;
export const PAYPAL_ENABLED = Boolean(PAYPAL_CLIENT_ID && PAYPAL_APP_SECRET);

export const USER_TOKEN_PREFIX = 'ABCD';

export const USER_TOKEN_VALIDATION_API_KEYS = process.env.USER_TOKEN_VALIDATION_API_KEYS
	? JSON.parse(process.env.USER_TOKEN_VALIDATION_API_KEYS)
	: [];

export const PUPPETEER_HELPER_URL = process.env.PUPPETEER_HELPER_URL;
export const PUPPETEER_HELPER_API_KEY = process.env.PUPPETEER_HELPER_API_KEY;

export const LUMOS_TSE_PUBLIC_KEY =
	'BGzOeYiF4NFdUERvDV8BFmAwLKzbg5oObXjY8pIF+p5M2ONwUPpZ/2cihgj+EOutCpTuEpt6dP9crIIlgeRKnvY=';

export const MAX_LINKED_ACCOUNTS_FOR_BOOKING = 5; // the maximum number of linked accounts that can be used for one booking
export const ONLINE_TICKETING_INTEGRATIONS =
	DEFAULT_APP_BRAND === 'CINFINITY'
		? ['KINOHELD', 'MARS', 'CINETIXX', 'COMPESO', 'TICKET_INTERNATIONAL']
		: [];

export const USER_TOKEN_BASED_INTEGRATIONS = ['KINOHELD', 'MARS', 'CINETIXX'];
export const VOUCHER_CODE_BASED_INTEGRATIONS = ['COMPESO', 'TICKET_INTERNATIONAL'];
export const CONCESSION_INTEGRATIONS = ['MARS', 'TICKET_INTERNATIONAL'];

export const ONLINE_TICKETING_LANGUAGES = ['de-lumos', 'de-cinfinity', 'de'];

export const CINETIXX_API_KEY = process.env.CINETIXX_API_KEY;
export const CINETIXX_API_BASE_URL = process.env.CINETIXX_API_BASE_URL;

export const TICKET_INTERNATIONAL_API_KEY = process.env.TICKET_INTERNATIONAL_API_KEY;

export const FOUR_MONHTS_UPFRONT_SUBSCRIPTION_TIER_ID = 3;
export const TWELVE_MONTHS_UPFRONT_SUBSCRIPTION_TIER_ID = 5;

export const APPLE_DEVELOPER_MERCHANT_DOMAIN_ASSOCIATION =
	DEFAULT_APP_BRAND !== 'CINFINITY'
		? ''
		: `MIIQYwYJKoZIhvcNAQcCoIIQVDCCEFACAQExCzAJBgUrDgMCGgUAMHIGCSqGSIb3DQEHAaBlBGN7
InRlYW1JZCI6IkZCRkdRWFNDVFEiLCJkb21haW4iOiJhcGkuY2luZmluaXR5LmRlIiwiZGF0ZUNy
ZWF0ZWQiOiIyMDI1LTA1LTA2LDA4OjM5OjM0IiwidmVyc2lvbiI6MX2ggg0_MIIENDCCAxygAwIB
AgIIPVn4N-rR2J8wDQYJKoZIhvcNAQELBQAwczEtMCsGA1UEAwwkQXBwbGUgaVBob25lIENlcnRp
ZmljYXRpb24gQXV0aG9yaXR5MSAwHgYDVQQLDBdDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTETMBEG
A1UECgwKQXBwbGUgSW5jLjELMAkGA1UEBhMCVVMwHhcNMjQxMjE2MTkyMTAxWhcNMjkxMjExMTgx
MzU5WjBZMTUwMwYDVQQDDCxBcHBsZSBpUGhvbmUgT1MgUHJvdmlzaW9uaW5nIFByb2ZpbGUgU2ln
bmluZzETMBEGA1UECgwKQXBwbGUgSW5jLjELMAkGA1UEBhMCVVMwggEiMA0GCSqGSIb3DQEBAQUA
A4IBDwAwggEKAoIBAQDQkzGr-FMe360eL2T3yU3Xlr5fSz15ZFxD7inDQNL6LxygtrwRc-_wIrDf
3vn5zlXj9UIBtSNlQRI793xGfYkTRZVE7JaVYmaXkAMA2eCuxdmxnCAtluCF7W9r01LTgzzUASMl
6eXG1BHYeaoIwt0VWPXvipNLDUvuDbaHk3UREFXLDHgH2zdwRjuX-sTreCng9Ox442HaQFeAB7uI
nKXHSW3IJtOVH00hoo1oc4Me5llahZZVVLCVLiXcdw1hh4GNiaQDezwAdyyJalnTZETZdKVbirFS
YV9uBDDU8-IQNAyVjEvhe9YrhYPyJzmNcAgj4hSI_5P0LW1MJuKLN4YtAgMBAAGjgeUwgeIwDAYD
VR0TAQH_BAIwADAfBgNVHSMEGDAWgBRv8ZUYYlzgyPHF7WwYyeDTZFKYIDBABggrBgEFBQcBAQQ0
MDIwMAYIKwYBBQUHMAGGJGh0dHA6Ly9vY3NwLmFwcGxlLmNvbS9vY3NwMDMtYWlwY2EwNzAvBgNV
HR8EKDAmMCSgIqAghh5odHRwOi8vY3JsLmFwcGxlLmNvbS9haXBjYS5jcmwwHQYDVR0OBBYEFLy1
xem9_MvQntyUsB74IZRc_wkuMA4GA1UdDwEB_wQEAwIHgDAPBgkqhkiG92NkBjoEAgUAMA0GCSqG
SIb3DQEBCwUAA4IBAQAyNMLpd6pf4MbKovN3WeeKZ7jxUfP7sPaXv7ERo-GhPLsjkukSk6JxHbzx
mnFvpDxvNpK24EeCopPj57iPu98lRGICz8289_uNiglweGXCGU0ly3JiIeSpaPkQmi56Dyi-pgMG
0VVhH698-JTUcQ7i0yVUi86t1WgHW70te8diRXm5qYdM-Bg7gK4QS3HYeCvmCq-a59lNzOpbt6OX
VWvsDaB4x8Cwz8vcEEwwkhNxlNav4OzJ6cmbziUy7hENIRcki_GVLiJiRkV_4NvXFlTBuT7T2oq5
Zfa0tCN_eF3ZcnZkY6cIVcfZt5TbI7_zk6fUBERDDcriVrIrlCikPXKHMIIERDCCAyygAwIBAgII
XGPK5Eo3U8kwDQYJKoZIhvcNAQELBQAwYjELMAkGA1UEBhMCVVMxEzARBgNVBAoTCkFwcGxlIElu
Yy4xJjAkBgNVBAsTHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MRYwFAYDVQQDEw1BcHBs
ZSBSb290IENBMB4XDTE3MDUxMDIxMjczMFoXDTMwMTIzMTAwMDAwMFowczEtMCsGA1UEAwwkQXBw
bGUgaVBob25lIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MSAwHgYDVQQLDBdDZXJ0aWZpY2F0aW9u
IEF1dGhvcml0eTETMBEGA1UECgwKQXBwbGUgSW5jLjELMAkGA1UEBhMCVVMwggEiMA0GCSqGSIb3
DQEBAQUAA4IBDwAwggEKAoIBAQDJRWoBDz6DBIbH_L_cXvAege4XMHNjJi7ePXokzZM-TzlHunW-
88DS8Vmiqx_-CoY82S2aB_IOa7kpkRpfIgqL8XJYBa5MS0TFeaeAPLCI4IwMJ4RdGeWHGTbL48V2
t7D0QXJR9AVcg0uibaZRuPEm33terWUMxrKYUYy7fRtMwU7ICMfS7WQLtN0bjU9AfRuPSJaSW_PQ
mH7ZvKQZDplhu0FdAcxbd3p9JNDc01P_w9zFlCy2Wk2OGCM5vdnGUj7R8vQliqEqh_3YDEYpUf_t
F2yJJWuHv4ppFJ93n8MVt2iziEW9hOYGAkFkD60qKLgVyeCsp4q6cgQ0sniM-LKFAgMBAAGjgeww
gekwDwYDVR0TAQH_BAUwAwEB_zAfBgNVHSMEGDAWgBQr0GlHlHYJ_vRrjS5ApvdHTX8IXjBEBggr
BgEFBQcBAQQ4MDYwNAYIKwYBBQUHMAGGKGh0dHA6Ly9vY3NwLmFwcGxlLmNvbS9vY3NwMDMtYXBw
bGVyb290Y2EwLgYDVR0fBCcwJTAjoCGgH4YdaHR0cDovL2NybC5hcHBsZS5jb20vcm9vdC5jcmww
HQYDVR0OBBYEFG_xlRhiXODI8cXtbBjJ4NNkUpggMA4GA1UdDwEB_wQEAwIBBjAQBgoqhkiG92Nk
BgISBAIFADANBgkqhkiG9w0BAQsFAAOCAQEAOs-smI2-kiAhCa2V87FcIfo2LVcgRHRzZJIIs5as
922X-ls0OCfPEkbTPBHwB8mZkLHR6BEJpeOla2xjCD-eJfrVmZxM5uXOjrJNaOyLq6OiT4oRFT7c
FCscxkS2b2fFW0-VKS2HXD_cgx53T-3aVKct5xOBwWPEVAsbSwpqKCII1DeSfH9nKF-vPT-3rFkd
ODRkWu4zShlCRCnEyhhr4cFTLS30TcIV9jMyGHjxJm-KTeuUTKPo_w-zA4tl2usu2GVQn9yfit8x
qIRU3FJSQdKyEx0xRkeIXz7uw_KMIwSV66yKPoJsBp8u44tDmmJbNA30mc8s7rpyhhkjpfyOtTCC
BLswggOjoAMCAQICAQIwDQYJKoZIhvcNAQEFBQAwYjELMAkGA1UEBhMCVVMxEzARBgNVBAoTCkFw
cGxlIEluYy4xJjAkBgNVBAsTHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MRYwFAYDVQQD
Ew1BcHBsZSBSb290IENBMB4XDTA2MDQyNTIxNDAzNloXDTM1MDIwOTIxNDAzNlowYjELMAkGA1UE
BhMCVVMxEzARBgNVBAoTCkFwcGxlIEluYy4xJjAkBgNVBAsTHUFwcGxlIENlcnRpZmljYXRpb24g
QXV0aG9yaXR5MRYwFAYDVQQDEw1BcHBsZSBSb290IENBMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A
MIIBCgKCAQEA5JGpCR-R2x5HUOsF7V55hC3rNqJXTFXsixmJ3vlLbPUHqyIwAugYPvhQCdN_QaiY
-dHKZpwkaxHQo7vkGyrDH5WeegykR4tb1BY3M8vED03OFGnRyRly9V0O1X9fm_IlA7pVj01dDfFk
NSMVSxVZHbOU9_acns9QusFYUGePCLQg98usLCBvcLY_ATCMt0PPD5098ytJKBrI_s61uQ7ZXhzW
yz21Oq30Dw4AkguxIRYudNU8DdtiFqujcZJHU1XBry9Bs_j743DN5qNMRX4fTGtQlkGJxHRiCxCD
QYczioGxMFjsWgQyjGizjx3eZXP_Z15lvEnYdp8zFGWhd5TJLQIDAQABo4IBejCCAXYwDgYDVR0P
AQH_BAQDAgEGMA8GA1UdEwEB_wQFMAMBAf8wHQYDVR0OBBYEFCvQaUeUdgn-9GuNLkCm90dNfwhe
MB8GA1UdIwQYMBaAFCvQaUeUdgn-9GuNLkCm90dNfwheMIIBEQYDVR0gBIIBCDCCAQQwggEABgkq
hkiG92NkBQEwgfIwKgYIKwYBBQUHAgEWHmh0dHBzOi8vd3d3LmFwcGxlLmNvbS9hcHBsZWNhLzCB
wwYIKwYBBQUHAgIwgbYagbNSZWxpYW5jZSBvbiB0aGlzIGNlcnRpZmljYXRlIGJ5IGFueSBwYXJ0
eSBhc3N1bWVzIGFjY2VwdGFuY2Ugb2YgdGhlIHRoZW4gYXBwbGljYWJsZSBzdGFuZGFyZCB0ZXJt
cyBhbmQgY29uZGl0aW9ucyBvZiB1c2UsIGNlcnRpZmljYXRlIHBvbGljeSBhbmQgY2VydGlmaWNh
dGlvbiBwcmFjdGljZSBzdGF0ZW1lbnRzLjANBgkqhkiG9w0BAQUFAAOCAQEAXDaZTC14t-2Mm9zz
d5vydtJ3ME_BH4WDhRuZPUc38qmbQI4s1LGQEti-9HOb7tJkD8t5TzTYoj75eP9ryAfsfTmDi1Mg
0zjEsb-aTwpr_yv8WacFCXwXQFYRHnTTt4sjO0ej1W8k4uvRt3DfD0XhJ8rxbXjt57UXF6jcfiI1
yiXV2Q_Wa9SiJCMR96Gsj3OBYMYbWwkvkrL4REjwYDieFfU9JmcgijNq9w2Cz97roy_5U2pbZMBj
M3f3OgcsVuvaDyEO2rpzGU-12TZ_wYdV2aeZuTJC-9jVcZ5-oVK3G72TQiQSKscPHbZNnF5jyEuA
F1CqitXa5PzQCQc3sHV1ITGCAoUwggKBAgEBMH8wczEtMCsGA1UEAwwkQXBwbGUgaVBob25lIENl
cnRpZmljYXRpb24gQXV0aG9yaXR5MSAwHgYDVQQLDBdDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTET
MBEGA1UECgwKQXBwbGUgSW5jLjELMAkGA1UEBhMCVVMCCD1Z-Dfq0difMAkGBSsOAwIaBQCggdww
GAYJKoZIhvcNAQkDMQsGCSqGSIb3DQEHATAcBgkqhkiG9w0BCQUxDxcNMjUwNTA2MDgzOTM0WjAj
BgkqhkiG9w0BCQQxFgQU9aMmghZqM9narOMdmb29ScxCdt4wKQYJKoZIhvcNAQk0MRwwGjAJBgUr
DgMCGgUAoQ0GCSqGSIb3DQEBAQUAMFIGCSqGSIb3DQEJDzFFMEMwCgYIKoZIhvcNAwcwDgYIKoZI
hvcNAwICAgCAMA0GCCqGSIb3DQMCAgFAMAcGBSsOAwIHMA0GCCqGSIb3DQMCAgEoMA0GCSqGSIb3
DQEBAQUABIIBAIQ5_nzP2Zzxfy43JUAy80fmeez2OiW3VxWU8T93m-DVaULOdG2cJSoAM2ZSGea1
3mqjH329sxBXbwr4ezVcQBKaB_jcCYi_e0wMuJ3Kr-bmEbZlJzf70HPc8ef45zG-v7_vGZYlamGd
sT7z0zlv_BhLClwZ2AOR2irWRHjtBN69agCFcZwXXI1OhRyZe-8_EcYqfTgFoZ5zPyy6r4pC_7UK
9gTkXZQjNR4E6kz_uDSq8z7UW_ZaZ0rvAx-ulJMo2ciD5NSAI51WuTvXtzxpw7XE3LIJgWl_zkjt
IGvWSCd-Z6LZtlzZ9k-NYFbeYtyIDEXPDDMx5w_mQETTNe5lvKU`;
