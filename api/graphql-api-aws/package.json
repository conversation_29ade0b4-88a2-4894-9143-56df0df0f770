{"name": "api", "version": "1.2.3", "private": true, "license": "UNLICENSED", "scripts": {"reinstall": "rm -rf node_modules/ && yarn install", "precommit": "ts-node -T precommit.ts && yarn typecheck", "precommit-old-to-be-added-back-soon": "yarn test", "typecheck": "tsc -p ./tsconfig.json", "test": "jest", "start": "serverless offline --reload<PERSON><PERSON>ler --stage dev start", "start:test": "NODE_ENV=test serverless offline --dontPrintOutput --skipCacheInvalidation start", "start:cineplex": "NODE_ENV=development.cineplex serverless offline --dontPrintOutput --stage development start", "start:cinfinity": "NODE_ENV=development.cinfinity serverless offline  --stage dev start", "start:debug:cineplex": "NODE_ENV=development.cineplex node --inspect ./node_modules/serverless/bin/serverless.js offline --dontPrintOutput --stage development start", "deploy:cineplexStagingDev": "NODE_ENV=cineplexStagingDev node predeploy && NODE_ENV=cineplexStagingDev serverless deploy --stage cineplexStagingDev --region eu-central-1", "deploy:cineplexRelease": "NODE_ENV=cineplexRelease node predeploy && NODE_ENV=cineplexRelease serverless deploy --stage cineplexRelease --region eu-central-1", "deploy:cinuruStagingDev": "NODE_ENV=cinuruStagingDev node predeploy && NODE_ENV=cinuruStagingDev serverless deploy --stage cinuruStagingDev --region eu-central-1", "deploy:cinuruStagingProd": "NODE_ENV=cinuruStagingProd node predeploy && NODE_ENV=cinuruStagingProd serverless deploy --stage cinuruStagingProd --region eu-central-1", "deploy:cinuruRelease": "NODE_ENV=cinuruRelease node predeploy && NODE_ENV=cinuruRelease serverless deploy --stage cinuruRelease --region eu-central-1 ", "deploy:cinfinityStaging": "NODE_ENV=cinfinityStaging node predeploy && NODE_ENV=cinfinityStaging serverless deploy --stage cinfinityStaging --region eu-central-1 --verbose", "deploy:cinfinityRelease": "NODE_ENV=cinfinityRelease node predeploy && NODE_ENV=cinfinityRelease serverless deploy --stage cinfinityRelease --region eu-central-1 --verbose", "deploy:testLambda:cinuruRelease": "NODE_ENV=cinuruRelease serverless deploy --function testLambda --stage cinuruRelease --region eu-central-1 ", "testLambda": "NODE_ENV=dev serverless invoke local --function testLambda", "notifications:local": "serverless invoke local --function notifications", "achievements:local": "NODE_ENV=development.cineplex serverless invoke local --function computeCineplexAchievements", "notifications:cineplex:local": "NODE_ENV=development.cineplex node --inspect ./node_modules/serverless/bin/serverless.js invoke local --function cineplexBgTasksAndNotifications", "testLambda:cineplex:local": "NODE_ENV=cineplexRelease node --inspect ./node_modules/serverless/bin/serverless.js invoke local --function testLambda", "syncPOS:local": "serverless invoke local --function syncAllPOS", "missingSharcData:local": "serverless invoke local --function missingSharcData", "cleanupDatabase:local": "serverless invoke local --function cleanupDatabase", "monitoringCinuru:local": "NODE_ENV=cinuruRelease serverless invoke local --function monitoring", "monitoringCineplex:local": "NODE_ENV=cineplexRelease serverless invoke local --function monitoring", "confirmTicketsAndBookBonusPoints:local": "serverless invoke local --function confirmTicketsAndBookBonusPoints", "revalidateCompesoBookings:local": "NODE_ENV=dev serverless invoke local --function revalidateCompesoBookings", "cinfinityRegularTasks:local": "NODE_ENV=dev serverless invoke local --function cinfinityRegularTasks", "checkScannedStatus:local": "NODE_ENV=dev serverless invoke local --function checkScannedStatus", "retryConfirmOnlineTicketing:local": "NODE_ENV=dev serverless invoke local --function retryConfirmOnlineTicketing", "sendRecommenderEmails:local": "serverless invoke local --function sendRecommenderEmails", "translateSubscriptionTiersPaypal:local": "NODE_ENV=dev serverless invoke local --function translateSubscriptionTiersPaypal", "serverless:local": "NODE_ENV=dev serverless invoke local --function", "publish-schema-apollo": "apollo service:push --key=\"service:cinuru-graphql-api:AvOFTcogEeu4FVOxXs0JCw\" --endpoint=\"https://api.cinuru.com\"", "ngrok": "ngrok http 3000", "copy-env-to-decrypted": "./check-branch.sh && git pull && cd ../../credentials && yarn decrypt && cd ../api/graphql-api-aws && rm ../../credentials/decrypted/graphql-api-aws/.env.*&& cp .env.ci* ../../credentials/decrypted/graphql-api-aws && cd ../../credentials && yarn encrypt && cd ../api/graphql-api-aws", "copy-env-from-decrypted": "git pull && cd ../../credentials && yarn decrypt && cd ../api/graphql-api-aws && rm .env.ci* && cp ../../credentials/decrypted/graphql-api-aws/.env.* ./", "testSubscribeToPaypalEvents": "ts-node src/testUtils/testSubscribeToPaypalEvents.ts", "validateIncompleteSubscriptions:local": "NODE_ENV=dev serverless invoke local --function validateIncompleteSubscriptions", "resendPaypalWebhookEvents:local": "NODE_ENV=dev serverless invoke local --function resendPaypalWebhookEvents"}, "dependencies": {"@aws-sdk/client-lambda": "^3.600.0", "@cinuru/emails": "link:../../emails", "@cinuru/utils": "link:../../utils", "@react-pdf/renderer": "^3.4.5", "@types/markdown-it": "^12.0.3", "@types/markdown-it-emoji": "^2.0.2", "@types/qrcode.react": "^1.0.2", "abort-controller": "^3.0.0", "ansi-to-html": "^0.6.11", "apollo-link-http": "^1.5.17", "apollo-server": "^3.5.0", "apollo-server-core": "^3.12.1", "apollo-server-express": "^3.5.0", "apollo-server-lambda": "^3.5.0", "apollo-server-testing": "^2.15.1", "base-64": "^1.0.0", "body-parser": "^1.19.0", "chroma-js": "^2.1.0", "common-tags": "^1.8.0", "cookie-session": "2.0.0", "copy-webpack-plugin": "6.2.1", "cors": "^2.8.5", "create-react-app": "^4.0.3", "crypto-random-string": "^3.2.0", "csrf": "^3.1.0", "date-fns": "^2.13.0", "date-fns-tz": "^1.0.10", "dotenv": "^8.2.0", "encoding": "^0.1.12", "express": "^4.17.1", "express-rate-limit": "^5.1.3", "fast-csv": "^4.3.6", "fast-xml-parser": "^3.12.20", "fb": "^2.0.0", "firebase-admin": "^8.12.1", "fuse.js": "^6.4.0", "generate-password": "^1.4.2", "google-auth-library": "^9.11.0", "graphql": "^14.5.6", "graphql-iso-date": "^3.6.1", "graphql-tools": "^4.0.5", "graphql-type-json": "^0.3.1", "graphql-validation-complexity": "^0.3.0", "he": "^1.2.0", "i18next": "^19.5.4", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^1.6.0", "lodash": "^4.17.21", "markdown-it": "^12.3.2", "markdown-it-emoji": "^2.0.0", "mjml": "^4.8.1", "mjml-react": "^2.0.0", "node-fetch": "^2.6.7", "nodemailer": "^6.9.9", "object-hash": "^1.3.1", "path-parser": "^6.1.0", "pdfkit": "^0.14.0", "pg": "^8.7.1", "qrcode": "^1.5.4", "qrcode.react": "^1.0.1", "rate-limit-redis": "^2.0.0", "react": "^17.0.0", "react-dom": "^17.0.0", "redis": "^3.0.2", "request": "^2.88.0", "source-map-support": "^0.5.19", "uuid": "^3.3.3"}, "devDependencies": {"@types/base-64": "^0.1.3", "@types/common-tags": "^1.8.0", "@types/date-fns": "^2.6.0", "@types/express": "^4.17.6", "@types/graphql": "^14.5.0", "@types/he": "^1.1.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^8.3.5", "@types/node": "^18", "@types/node-fetch": "^2.5.0", "@types/nodemailer": "^6.2.2", "@types/pdfkit": "^0.13.9", "@types/pg": "^8.11.11", "aws-sdk": "^2.939.0", "cache-loader": "^4.1.0", "fork-ts-checker-webpack-plugin": "^5.0.5", "jest": "^29.7.0", "localtunnel": "^2.0.2", "ngrok": "^3.2.7", "serverless": "3.38.0", "serverless-dotenv-plugin": "^2.4.2", "serverless-offline": "^13.3.3", "serverless-prune-plugin": "^1.4.3", "serverless-webpack": "^5.5.1", "thread-loader": "^2.1.3", "ts-jest": "^29.2.5", "ts-loader": "^7.0.5", "ts-node": "^10.9.2", "ts-node-dev": "^1.0.0-pre.59", "typescript": "^5.7.2", "webpack": "^5.21.2", "webpack-node-externals": "^1.7.2"}, "bundledDependencies": ["chroma-js@^2.1.0", "path-parser@^6.1.0", "mjml@^4.8.1", "react@^17.0.0", "react-dom@^17.0.0", "mjml-react@^2.0.0", "@types/markdown-it@^12.0.3", "@types/markdown-it-emoji@^2.0.2", "markdown-it@^12.3.2", "markdown-it-emoji@^2.0.0", "@types/qrcode.react@^1.0.2", "qrcode.react@^1.0.1", "@types/qrcode.react@^1.0.2"]}