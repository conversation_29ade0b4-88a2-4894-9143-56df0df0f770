/* eslint-disable no-catch-all/no-catch-all */

import { asyncCineplexCustomerSync } from "../graphql/User/cineplexCustomerService";
import { InternalUserId } from "../typescript-types";

export const run = async ({ userIdDb,
	requestId,
	syncId }: {
		userIdDb: InternalUserId,
		requestId: string,
		syncId: string
	}) => {
	//This function is called via a lambda invoke, to allow the user sync to be async
	//The sync with cineplex might take quite a while, but we do not want to block the api request for that time 
	//To allow the sync to finish even after the qraphql query returned, we need to run it in a seperate lambda.

	console.log(
		`Starting to sync Cineplex user with id ${userIdDb}.`
	);
	const result = await asyncCineplexCustomerSync(userIdDb, requestId, syncId);
	console.log('Finished syncing Cineplex user.', result);
	return result;
};
