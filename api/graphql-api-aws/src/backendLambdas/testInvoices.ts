import { writeFileSync } from 'fs';
import generateInvoicePdf from '../utils/invoice/generateInvoicePdfNew';
import { getPriceInCents } from '../utils/paypal/utils';
import { getPriceTaxDetails } from '../utils/invoice/createInvoice';
import { addMonths } from 'date-fns';

const createInvoice = () => {
	const lastPaymentAmount = (1000.02).toString();
	const now = new Date();

	return generateInvoicePdf({
		invoiceId: '12345',
		invoiceDate: now.toDateString(),
		brand: 'CINFINITY',
		isPaid: true,
		items: [
			{
				item: 'Subscription',
				description: 'Subscription to Cinfinity',
				priceInCents: getPriceTaxDetails(getPriceInCents(lastPaymentAmount))
					.priceWithoutTaxesInCents,
				taxesInCents: getPriceTaxDetails(getPriceInCents(lastPaymentAmount)).taxesInCents,
				quantity: 1,
			},
		],
		shipping: {
			name: `<PERSON>`,
			country: 'PT',
			state: 'Leiria',
			city: 'Leiria',
			address: 'Rua da Liberdade 100',
			deliveryDate: addMonths(now, 1).toDateString(),
		},
	});
};

export const run = async () => {
	console.log('testInvoices');

	const bufferData = await createInvoice();

	writeFileSync('/tmp/latestInvoice.pdf', bufferData);
};
