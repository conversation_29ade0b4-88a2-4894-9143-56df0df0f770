/* eslint-disable no-await-in-loop */
import https from 'https';
import fetch from 'node-fetch';

import { getCinetixxBooking } from '../graphql/ConfirmTicketPurchase/cinetixx';
import { getOnlineTicketingBookingFn } from '../graphql/ConfirmTicketPurchase/getOnlineTicketingBooking';
import { syncTicketsFn } from '../graphql/ConfirmTicketPurchase/syncTicket';
import { InternalTicketId } from '../graphql/Ticket/Ticket';
import { db, sendErrorMail, sql } from '../utils';

let marsAPIDict;
const verifyMarsTicket = async (qrCode: string, cinemaIdDb: number) => {
	if (!marsAPIDict) {
		const res = await db.query(
			sql`SELECT id, api_base_url FROM priv.p_cinema WHERE ticket_provider = 'MARS'`
		);
		marsAPIDict = new Map(res.rows.map((row) => [row.id, row.api_base_url]));
	}
	const apiBaseUrl = marsAPIDict.get(cinemaIdDb);
	if (!apiBaseUrl) {
		await sendErrorMail(`No API base URL found for cinema ${cinemaIdDb}`, '');
		throw new Error(`No API base URL found for cinema ${cinemaIdDb}`);
	}
	const apiRes = await fetch(`${apiBaseUrl}/reservation?ticketId=${qrCode}`, {
		method: 'get',
		agent: new https.Agent({
			rejectUnauthorized: false,
		}),
	});
	const apiData = await apiRes.json();

	return { scanned: apiData.punched === 'YES' };
};

const verifyCinetixxTicket = async (bookingId: string, qrCode: string) => {
	const res = await getCinetixxBooking(bookingId);
	const ticket = res.data.filter((t) => t.number === qrCode)?.[0];
	if (!ticket) {
		throw new Error('Ticket not found');
	}
	return {
		scanned: ticket.statusKey === 'TCK_STAMPED' || ticket.statusKey === 'TCK_PRINTED',
	};
};

const verifyKinoheldTicket = async (ticketId: InternalTicketId) => {
	console.log('verifyKinoheldTicket ', ticketId);
	const res = await getOnlineTicketingBookingFn({ ticketId });
	if (!res.success) {
		console.log('Error getting booking', res);
		throw new Error('Booking not found');
	}
	if (res.booking.ticketStatus === 'cancelled') {
		console.log('Ticket is cancelled');
		return {
			scanned: false,
			status: 'cancelled',
		};
	}
	console.log('verifyKinoheldTicket res', res);
	const ticket = res.booking.tickets.find((t) => t.qrCode === res.ticketInfo.qr_code);
	console.log('verifyKinoheldTicket ticket', ticket);
	if (!ticket) {
		throw new Error('Ticket not found');
	}
	// if the ticket is from a cinema that supports the scanned flag (kinoheld_scan), the ticket is only interpreted as scanned if the ticket status is redeemed
	// if the ticket is from a cinema that does not support the scan flag, the ticket is always interpreted as scanned
	return {
		scanned: res.ticketInfo.kinoheld_scan ? ticket.ticketStatus === 'redeemed' : true,
	};
};

const verifyTicketInternationalTicket = async (ticketId: InternalTicketId) => {
	console.log('verifyTicketInternationalTicket ', ticketId);
	const res = await getOnlineTicketingBookingFn({ ticketId });
	if (!res.success) {
		console.log('Error getting booking', res);
		throw new Error('Booking not found');
	}
	if (res.booking.ticketStatus === 'cancelled') {
		console.log('Ticket is cancelled');
		return {
			scanned: false,
			status: 'cancelled',
		};
	}
	console.log('verifyTicketInternationalTicket res', res);
	const ticket = res.booking.tickets.find((t) => t.qrCode === res.ticketInfo.qr_code);
	console.log('verifyTicketInternationalTicket ticket', ticket);
	if (!ticket) {
		throw new Error('Ticket not found');
	}
	return {
		scanned: ticket.ticketStatus === 'redeemed',
	};
};

export const checkScannedStatus = async () => {
	const res = await db.query(
		sql`SELECT id, ticket_provider, qr_code, cinema_id,cf_booking_id FROM priv.p_ticket WHERE screening_datetime < now() - interval '30 minutes' AND scan_checked IS NOT TRUE`
	);

	for (const row of res.rows) {
		try {
			console.log(`Checking Ticket `, row);
			let scanned = false;
			let cancelled = undefined;
			switch (row.ticket_provider) {
				case 'MARS': {
					const marsRes = await verifyMarsTicket(row.qr_code, row.cinema_id);
					scanned = marsRes.scanned;
					break;
				}
				case 'KINOHELD': {
					const kinoheldRes = await verifyKinoheldTicket(row.id);
					scanned = kinoheldRes.scanned;
					cancelled = kinoheldRes.status === 'cancelled';
					break;
				}
				case 'COMPESO':
					scanned = true;
					break;
				case 'CINETIXX': {
					const cinetixxRes = await verifyCinetixxTicket(row.cf_booking_id, row.qr_code);
					scanned = cinetixxRes.scanned;
					break;
				}
				case 'TICKET_INTERNATIONAL':
					const ticketInternationalRes = await verifyTicketInternationalTicket(row.id);
					scanned = ticketInternationalRes.scanned;
					cancelled = ticketInternationalRes.status === 'cancelled';
				default:
					throw new Error('Not implemented yet');
			}

			// We need to sync the ticket status in the database
			// TODO: Integrate the ticket Status into syncTicketsFn once we get the data from all online ticketings.
			await syncTicketsFn([row.id]);
			const updateRes = await db.query(sql`
      UPDATE priv.p_ticket
      SET scan_checked = TRUE
      WHERE id = ${row.id}
			Returning status`);

			if (cancelled === true && updateRes.rows[0].status !== 'REFUNDED') {
				await db.query(sql`
				UPDATE priv.p_ticket
				SET status = 'REFUNDED', refunded_at = COALESCE(refunded_at,now())
				WHERE id = ${row.id}`);
			}
			// eslint-disable-next-line no-catch-all/no-catch-all
		} catch (e) {
			await sendErrorMail('Could not check ticket status', e);

			console.log(`Error checking ticket`, e);
		}
	}
};
