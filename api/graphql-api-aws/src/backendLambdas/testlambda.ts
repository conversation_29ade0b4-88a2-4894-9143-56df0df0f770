/* eslint-disable no-await-in-loop */
import { db, sql } from '../utils';
import createInvoice from '../utils/invoice/createInvoice';
module.exports.run = async () => {
	const paymentIdsWithNoInvoices = (
		await db.query(sql`
			SELECT id FROM priv.p_payment WHERE id NOT IN (SELECT payment_id FROM priv.p_invoice)
	`)
	).rows.map((r) => r.id) as number[];
	console.log('paymentIdsWithNoInvoices', paymentIdsWithNoInvoices);
	const firstPaymentId = paymentIdsWithNoInvoices[0];
	console.log('firstPaymentId', firstPaymentId);
	await createInvoice({ paymentId: firstPaymentId });
};
