import { db, sql } from '.';

import { User, CinemaOperatingCompany, Cinema, BonusProgram } from '../graphql';
import { PublicCinemaOperatingCompanyId, PublicUserId } from '../typescript-types';

export const belongsToCinemaOperatingCompanies = async (
	userId: PublicUserId
): Promise<PublicCinemaOperatingCompanyId[]> => {
	const privRes = await db.query(
		`SELECT privilege, up.cinema_operating_company_id, c.id as cinema_id
 FROM priv.p_user_privilege up
LEFT OUTER JOIN priv.p_cinema c ON up.cinema_operating_company_id = c.cinema_operating_company_id WHERE user_id = $1`,
		[User.decodePublicId(userId)]
	);
	let rootRole = false;
	let cinemaOperatingCompanyIdsDb = [];
	privRes.rows.forEach((el) => {
		if (el.privilege === 'ROOT') {
			rootRole = true;
		}
		if (
			el.privilege === 'CINEMA_ADMIN' &&
			el.cinema_operating_company_id &&
			!cinemaOperatingCompanyIdsDb.includes(el.cinema_operating_company_id)
		) {
			cinemaOperatingCompanyIdsDb.push(el.cinema_operating_company_id);
		}
	});
	if (rootRole) {
		//Root user is admin for each cinema
		const cinemaOperatingCompanyRes = await db.query(
			sql`SELECT id FROM priv.p_cinema_operating_company ORDER BY name`
		);
		cinemaOperatingCompanyIdsDb = cinemaOperatingCompanyRes.rows.map((r) => r.id);
	}

	return cinemaOperatingCompanyIdsDb.map((id) => CinemaOperatingCompany.encodeDbId(id));
};

export const getPrivileges = async (userId: PublicUserId, isRoot: boolean, isAdmin: boolean) => {
	if (isRoot || isAdmin) {
		return {
			accessRightDashboard: true,
			accessRightFilmStatistics: true,
			accessRightBonusProgram: true,
			accessRightCampaigns: true,
		};
	}
	const cinemaOperatingCompanyIdsDb = (await belongsToCinemaOperatingCompanies(userId)).map((id) =>
		CinemaOperatingCompany.decodePublicId(id)
	);

	const accessRightsRes = await db.query(
		`SELECT 
            bool_and(access_right_dashboard) as access_right_dashboard, 
            bool_and(access_right_film_statistics) as access_right_film_statistics,  
            bool_and(access_right_bonus_program) as access_right_bonus_program,  
            bool_and(access_right_campaigning) as access_right_campaigning
        FROM priv.p_cinema_operating_company WHERE id  = ANY ($1);`,
		[cinemaOperatingCompanyIdsDb]
	);

	const r = accessRightsRes.rows[0];
	const accessRightDashboard = r.access_right_dashboard || false;
	const accessRightFilmStatistics = r.access_right_film_statistics || false;
	const accessRightBonusProgram = r.access_right_bonus_program || false;
	const accessRightCampaigns = r.access_right_campaigning || false;
	return {
		accessRightDashboard,
		accessRightFilmStatistics,
		accessRightBonusProgram,
		accessRightCampaigns,
	};
};

export const adminForCinemas = async (userId: PublicUserId) => {
	const privRes = await db.query(sql`
		SELECT 
		privilege, 
		up.cinema_operating_company_id, 
		c.id as cinema_id
		FROM priv.p_user_privilege up
		LEFT OUTER JOIN priv.p_cinema c ON up.cinema_operating_company_id = c.cinema_operating_company_id 
		WHERE user_id = ${User.decodePublicId(userId)}`);
	let rootRole = false;
	let adminRole = false;
	let adminCinemas = [];
	privRes.rows.forEach((el) => {
		if (el.privilege === 'ROOT') {
			rootRole = true;
			adminRole = true;
		} else if (el.privilege === 'ADMIN') {
			adminRole = true;
		} else if (
			(el.privilege === 'CINEMA_ADMIN' || el.privilege === 'SUPPORT') &&
			el.cinema_id &&
			!adminCinemas.includes(el.cinema_id)
		) {
			adminCinemas.push(el.cinema_id);
		}
	});
	if (rootRole || adminRole) {
		//Root user is admin for each cinema
		const cinemaRes = await db.query(sql`SELECT id FROM priv.p_cinema ORDER BY name`);
		adminCinemas = cinemaRes.rows.map((r) => r.id);
	}

	return adminCinemas.map((id) => Cinema.encodeDbId(id));
};

export const adminForBonusPrograms = async (userId: PublicUserId) => {
	const adminCinemas = (await adminForCinemas(userId)).map((id) => Cinema.decodePublicId(id));
	const cinemaConfRes = await db.query(
		'SELECT DISTINCT bonus_currency_id FROM priv.p_cinema WHERE id = ANY ($1)',
		[adminCinemas]
	);
	return cinemaConfRes.rows
		.filter((i) => i !== null && i.bonus_currency_id)
		.map((r) => BonusProgram.encodeDbId(r.bonus_currency_id));
};

export const getIsRootOrAdmin = async (userId: PublicUserId) => {
	const privRes = await db.query(
		`SELECT privilege FROM priv.p_user_privilege up WHERE user_id = $1`,
		[User.decodePublicId(userId)]
	);
	let root = false,
		admin = false,
		support = false;
	privRes.rows.forEach((el) => {
		if (el.privilege === 'ROOT') {
			root = true;
			admin = true;
		}
		if (el.privilege === 'ADMIN') {
			admin = true;
		}
		if (el.privilege === 'SUPPORT') {
			support = true;
		}
	});
	return { root, admin, support };
};
