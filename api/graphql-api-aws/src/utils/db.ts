import {
	DB_CONF,
	LOG_DB_ERRORS,
	REDIS_HOST,
	REDIS_PASSWORD,
	REDIS_TLS,
	REDIS_PREFIX,
	USE_REDIS,
	REDIS_PORT,
} from '../../consts';
import { Pool, PoolClient } from 'pg';
import { stripIndent } from 'common-tags';
import redis from 'redis';
import { promisify } from 'util';
import crypto from 'crypto';
import { deflateSync, inflateSync } from 'zlib';

if (!pool) {
	//We use var here to hoist the pool up, but only redeclare it once
	//eslint-disable-next-line no-var
	var pool: Pool = new Pool(DB_CONF);
}
if (!redisClient && USE_REDIS) {
	// eslint-disable-next-line no-var
	var redisClient = redis.createClient({
		host: REDIS_HOST,
		port: REDIS_PORT,
		password: REDIS_PASSWORD ? REDIS_PASSWORD : undefined,
		tls: REDIS_TLS ? { servername: REDIS_HOST } : undefined,
		prefix: REDIS_PREFIX,
	});
	redisClient.on('error', (error) => {
		console.error('[Redis error]: Client ', error);
		redisClient = undefined;
	});

	// eslint-disable-next-line no-var
	var redisGet = promisify(redisClient.get).bind(redisClient);
	// eslint-disable-next-line no-var
	var redisSet = promisify(redisClient.set).bind(redisClient);
}

type QueryArguments = [string, any[]] | [SqlQuery];

type CacheOptions = {
	ttlMinutes?: number;
	forceReload?: boolean;
	shouldStoreLargeObjectInCache?: boolean;
};

type QueryResultType<T extends Record<string, any>> = {
	rowCount: number;
	rows?: T[];
};

//db transaction helper, handles everything for you, just gotta call client.query() instead of the regular pool.query() in the callback
const withTransaction = async <T>(
	callback: (client: PoolClient) => Promise<T> //eslint-disable-line promise/prefer-await-to-callbacks
): Promise<T | undefined> => {
	const client = await pool.connect();

	let res: T | undefined;

	try {
		await client.query('BEGIN');

		res = await callback(client); //eslint-disable-line promise/prefer-await-to-callbacks

		await client.query('COMMIT');

		client.release();

		return res;
	} catch (err) {
		await client.query('ROLLBACK');

		client.release();

		console.error(err);

		throw err;
	}
};

interface QueryWithCache {
	<T extends Record<string, any> = any>(sqlQuery: SqlQuery, options?: CacheOptions): Promise<
		QueryResultType<T>
	>;

	<T extends Record<string, any> = any>(
		sql: string,
		dependencies: any[],
		options?: CacheOptions
	): Promise<QueryResultType<T>>;
}

//allow caching without using the sql tag
const queryWithCache: QueryWithCache = async (...args) => {
	const [arg1, arg2, arg3] = args;

	let options: CacheOptions | undefined;
	let sql: string;
	let dependencies: any[] = [];
	let queryDb: () => Promise<any>;

	//when raw string query
	if (typeof arg1 === 'string') {
		options = arg3;
		dependencies = arg2;
		sql = arg1;

		queryDb = () => db.query(arg1, dependencies);
	}
	//when using sql tag
	else {
		options = arg2;
		sql = arg1.log();

		queryDb = () => db.query(arg1);
	}

	if (!USE_REDIS) {
		return await queryDb();
	}

	let ttl = options && options.ttlMinutes ? Math.round(options.ttlMinutes * 60000) : 5 * 60 * 1000;

	//Add a random value to ttl, so that not all lamdas refresh the redis cache simultaneously.
	ttl = Math.round(ttl + (ttl / 5) * Math.random());

	const str = crypto
		.createHash('sha1')
		.update(JSON.stringify({ sql, dependencies }))
		.digest('base64');
	const forceReload = options && options.forceReload ? options.forceReload : false;

	if (!forceReload) {
		const cached = await getObjectFromCache(str);
		if (cached && cached.storedAt + ttl > Date.now()) {
			//console.log('[Redis] Returning Cached result');
			return cached;
		}
		//console.log('Redis] Cache miss', str);
	} else {
		console.log('[Redis] Forced reload', str);
	}
	const queried = await queryDb();

	await storeObjectInCache(
		str,
		{ rows: queried.rows, storedAt: Date.now() },
		ttl,
		options?.shouldStoreLargeObjectInCache || false
	);
	return queried;
};

const query = async <T extends Record<string, any> = any>(
	...args: QueryArguments
): Promise<QueryResultType<T>> => {
	try {
		//@ts-ignore
		return await pool.query(...args);
	} catch (e) {
		//LOG_DB_ERRORS can be set by .env.
		// NONE: No logging
		// QUERY: Only Query, no Values
		// FULL: Query + Values
		// In production it should never be set to FULL, as this would
		// possibly lead to cleartext passwords beeing logged.

		let queryToLog, argsToLog;

		if (LOG_DB_ERRORS !== 'NONE')
			if (typeof args[0] == 'string') {
				// A "Text" query is passed in
				queryToLog = args[0];
				argsToLog = LOG_DB_ERRORS === 'FULL' ? args[1] : '';
			} else {
				// A sql`` template string query is passed in
				queryToLog =
					LOG_DB_ERRORS === 'FULL' ? args[0].log() : stripIndent(args[0].text).replace(/\t/g, '  ');
			}
		console.error('An error occured on query\n', queryToLog, argsToLog, e);

		if (e?.message === 'Query read timeout') {
			// await sendErrorMail(
			// 	'DB Query timed out',
			// 	`
			// Error ${printError(e)},
			// Query: ${queryToLog},
			// Args: ${argsToLog}
			// `
			// );
		}

		throw e;
	}
};
const queryOne = async <T extends Record<string, any> = any>(
	...args: QueryArguments
): Promise<null | T> => {
	const res = await query(...args);
	if (res.rows.length === 0) {
		return null;
	} else if (res.rows.length === 1) {
		return res.rows[0];
	} else {
		const queryString = typeof args[0] === 'string' ? args[0] : args[0].text;
		throw new Error(
			`Unexpected return of ${res.rows.length} rows when 1 was expected for:\n${queryString}`
		);
	}
};

const end = () => {
	return pool.end();
};

export const db = {
	withTransaction,
	queryWithCache,
	query,
	queryOne,
	end,
};

export interface SqlQuery {
	strings: TemplateStringsArray;
	values: any[];
	text: string;
	log: () => string;
}

const _format = (val: any) => {
	if (typeof val === 'string') return "'" + val + "'";
	return val;
};

export const sql: (strings: TemplateStringsArray, ...values: any[]) => SqlQuery = (
	strings,
	...values
) => {
	return {
		text: strings.reduce((prev, curr, i) => prev + '$' + i + curr),
		values,
		strings,
		log: () => {
			const queryWithVals = strings.reduce((prev, curr, i) => prev + curr + _format(values[i]), '');
			return stripIndent(queryWithVals).replace(/\t/g, '  ');
		},
	};
};

export const getFromCache = async (key: string) => {
	if (!USE_REDIS) {
		return;
	}
	try {
		const res = await redisGet(key);
		return res;
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (e) {
		console.error('[Redis error]: Error getting from cache', e);
		return null;
	}
};

export const storeObjectInCache = async (
	key: string,
	value: unknown,
	ttl = 5 * 60 * 1000,
	hugeObjectAllowed = false
) => {
	if (!USE_REDIS) {
		return;
	}
	try {
		const stringified = JSON.stringify(value);
		if (stringified.length > 10000000 && !hugeObjectAllowed) {
			//~10MB
			console.error('Object too large to store in cache', stringified.length, key);
			console.error('Here is the huge object:', stringified);
			return;
		}
		const compressed = deflateSync(stringified).toString('base64');

		await redisSet(key, compressed, 'PX', Math.round(ttl));
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (e) {
		console.error('[Redis error]: Error storing in cache', e);
		return;
	}
};
export const getObjectFromCache = async (key: string) => {
	const data = await getFromCache(key);
	if (!data) {
		return null;
	}

	//@ts-ignore
	const decompressed = inflateSync(Buffer.from(data, 'base64')).toString();
	return JSON.parse(decompressed);
};
