/* eslint-disable no-catch-all/no-catch-all, no-await-in-loop */
import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';
import { ApolloError, db, sql } from '..';
import { BillingCycleConfig, createBillingPlan } from './billingPlan';
import { ProductDetails } from './product';
import { PRODUCT_CATALOG_ROUTE, getBearerHeaders } from './utils';

export const createDigitalProduct = async ({
	id,
	name,
}): Promise<{ success: boolean; errorMessage?: string; productId?: string }> => {
	const productId = `${id}-${uuidv4()}`;

	try {
		const response = await fetch(PRODUCT_CATALOG_ROUTE, {
			method: 'post',
			body: JSON.stringify({
				id: productId,
				name,
				type: 'DIGITAL',
			}),
			headers: await getBearerHeaders(),
		});
		if (response.status !== 201) {
			const errorMessage = await response.text();
			return { success: false, errorMessage };
		}
		const result: ProductDetails = await response.json();
		return { success: true, productId: result.id };
	} catch (e) {
		console.log('>>> createDigitalProduct error: ', e);
		return { success: false, errorMessage: e.message };
	}
};

const translateSubscriptionTiersPaypal = async () => {
	const subscriptionTiers = (
		await db.query(
			sql`
				SELECT 
				id::text, 
				name, 
				description, 
				paypal_product_id as "paypalProductId",
				paypal_billing_plan_id as "paypalBillingPlanId",
				billing_cycles_config as "billingCycleConfigs"
				FROM priv.p_subscription_tier
				WHERE is_hidden = false

			`
		)
	).rows as {
		id: string;
		name: string;
		description: string;
		paypalProductId?: string;
		paypalBillingPlanId?: string;
		billingCycleConfigs: BillingCycleConfig[];
	}[];
	for (const subscriptionTier of subscriptionTiers) {
		const { name, id, paypalProductId, paypalBillingPlanId } = subscriptionTier;
		let createdPaypalProductId;

		console.log(`\n>>> check subscriptionTier: ${name}`);
		console.log(`- paypalProductId: ${paypalProductId}`);
		console.log(`- paypalBillingPlanId: ${paypalBillingPlanId}`);

		if (!paypalProductId) {
			const { success, errorMessage, productId } = await createDigitalProduct({
				id: subscriptionTier.id,
				name: subscriptionTier.name,
			});
			if (!success) throw new ApolloError(errorMessage, 'PAYPAL_ERROR');

			createdPaypalProductId = productId;

			await db.query(
				sql`
					UPDATE priv.p_subscription_tier
					Set 
					paypal_product_id = ${productId}, 
					updated_paypal_at = NOW()
					WHERE id = ${id}
				`
			);
			console.log(`- no product assigned, created DigitalProduct: ${productId}`);
		} else {
			console.log(`- product already assigned, skipping`);
		}

		if (!paypalBillingPlanId) {
			const { success, errorMessage, billingPlanId } = await createBillingPlan({
				paypalProductId: paypalProductId || createdPaypalProductId,
				productName: name,
				productDescription: name,
				billingCycleConfigs: subscriptionTier.billingCycleConfigs,
			});
			if (!success) {
				throw new ApolloError(errorMessage, 'PAYPAL_ERROR');
			}
			console.log(`- no billingPlan assigned, create billing plan: ${billingPlanId}`);

			await db.query(
				sql`
					UPDATE priv.p_subscription_tier
					SET 
					paypal_billing_plan_id = ${billingPlanId}, 
					updated_paypal_at = NOW()
					WHERE id = ${id}
				`
			);
		} else {
			console.log(`- billing plan already assigned, skipping`);
		}
	}
};

export default translateSubscriptionTiersPaypal;
