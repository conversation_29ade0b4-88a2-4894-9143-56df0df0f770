import { ApolloError, db, sql } from '../../utils';
import { InternalUserId } from '../../typescript-types';
import generateInvoicePdf from './generateInvoicePdfNew';
import { format } from 'date-fns';
import { getOrder } from '../paypal/order';
import { getPriceInCents } from '../paypal/utils';
import { getSubscription } from '../paypal/subscription';
import { getBillingPlanDetails } from '../paypal/billingPlan';

export const getPriceTaxDetails = (
	priceInCents: number
): {
	priceWithoutTaxesInCents: number;
	taxesInCents: number;
} => {
	const taxRate = 0.07;
	const taxesInCents = Math.round(priceInCents * taxRate);
	const priceWithoutTaxesInCents = priceInCents - taxesInCents;

	return {
		priceWithoutTaxesInCents,
		taxesInCents,
	};
};

const createInvoice = async ({
	paymentId,
}: {
	paymentId: number;
}): Promise<{ success: boolean }> => {
	const paymentRes = (await db.queryOne(
		sql`SELECT 
			p.user_id as "userId",
			p.provider,
			p.paid_price_in_cents as "priceInCents",
			s.paypal_order_id as "subscriptionPaypalOrderId",
			bp.paypal_order_id as "orderPaypalOrderId"
			FROM priv.p_payment p
			LEFT JOIN priv.p_booking_process bp ON bp.id = p.booking_process_id
			LEFT JOIN priv.p_subscription s ON s.id = p.subscription_id
			WHERE p.id = ${paymentId}
			
	`
	)) as
		| null
		| {
				userId: InternalUserId;
				provider: 'PAYPAL';
				priceInCents: number;
				subscriptionPaypalOrderId: string;
				orderPaypalOrderId: undefined;
		  }
		| {
				userId: InternalUserId;
				provider: 'PAYPAL';
				priceInCents: number;
				subscriptionPaypalOrderId: undefined;
				orderPaypalOrderId: string;
		  };

	if (!paymentRes) {
		throw new ApolloError('PAYMENT_NOT_FOUND', 'PAYMENT_NOT_FOUND');
	}

	if (paymentRes.provider !== 'PAYPAL') {
		throw new ApolloError('PAYMENT_PROVIDER_NOT_SUPPORTED', 'PAYMENT_PROVIDER_NOT_SUPPORTED');
	}

	const subscriptionPaypalOrderId = paymentRes.subscriptionPaypalOrderId;
	const orderPaypalOrderId = paymentRes.orderPaypalOrderId;

	if (orderPaypalOrderId) {
		const orderDetailsRes = await getOrder(orderPaypalOrderId);
		if (!orderDetailsRes.success) {
			throw new ApolloError('CANNOT_GET_PAYPAL_ORDER', 'CANNOT_GET_PAYPAL_ORDER');
		}

		const orderStatus = orderDetailsRes.data.status;
		if (orderStatus !== 'COMPLETED') {
			throw new ApolloError('PAYPAL_ORDER_NOT_COMPLETED', 'PAYPAL_ORDER_NOT_COMPLETED');
		}

		const invoiceDbId = (
			await db.queryOne(sql`
			INSERT INTO priv.p_invoice (user_id, payment_id, price_in_cents, datetime)
			VALUES (
				${paymentRes.userId},
				${paymentId},
				${paymentRes.priceInCents},
				NOW()
			)
			RETURNING id::text
		`)
		)?.id as string;

		//const orderDate = orderDetailsRes.data.update_time; <- TODO: Discuss. This does not change so the invoice date would always be the date of the first payment
		const formattedOrderDate = format(new Date(), 'dd.MM.yyyy');

		const items = orderDetailsRes.data.purchase_units[0].items;
		const shipping = orderDetailsRes.data.purchase_units[0].shipping;

		const invoicePdfBuffer = await generateInvoicePdf({
			invoiceId: `PP-${new Date().getFullYear}1${new Date().getMonth() + 18}${invoiceDbId}`,
			invoiceDate: formattedOrderDate,
			brand: 'CINFINITY',
			isPaid: true,
			items: items.map((item) => ({
				item: 'Ticket',
				description: item.name,
				priceInCents: getPriceTaxDetails(getPriceInCents(item.unit_amount.value))
					.priceWithoutTaxesInCents,
				taxesInCents: getPriceTaxDetails(getPriceInCents(item.unit_amount.value)).taxesInCents,
				quantity: Number(item.quantity),
			})),
			shipping: {
				name: shipping.name.full_name,
				country: shipping.address.country_code,
				state: shipping.address.admin_area_1,
				city: shipping.address.admin_area_2,
				address: shipping.address.address_line_1,
				deliveryDate: formattedOrderDate,
			},
		});

		await db.queryOne(sql`
			UPDATE priv.p_invoice
			SET pdf_data = ${invoicePdfBuffer}
			WHERE id = ${invoiceDbId}
		`);
	}

	if (subscriptionPaypalOrderId) {
		const subscriptionDetailsRes = await getSubscription(subscriptionPaypalOrderId);
		if (!subscriptionDetailsRes.success) {
			throw new ApolloError('CANNOT_GET_SUBSCRIPTION', 'CANNOT_GET_SUBSCRIPTION');
		}

		const subscriptionStatus = subscriptionDetailsRes.data.status;
		if (subscriptionStatus === 'APPROVAL_PENDING') {
			throw new ApolloError('SUBSCRIPTION_NOT_APPROVED', 'SUBSCRIPTION_NOT_APPROVED');
		}

		const getBillingPlanRes = await getBillingPlanDetails(subscriptionDetailsRes.data.plan_id);
		if (!getBillingPlanRes.success) {
			throw new ApolloError('CANNOT_GET_BILLING_PLAN', 'CANNOT_GET_BILLING_PLAN');
		}

		const invoiceDbId = (
			await db.queryOne(sql`
			INSERT INTO priv.p_invoice (user_id, payment_id, datetime, price_in_cents)
			VALUES (
				${paymentRes.userId},
				${paymentId},
				NOW(),
				${paymentRes.priceInCents}
			)
			RETURNING id::text
		`)
		)?.id as string;

		//const orderDate = subscriptionDetailsRes.data.start_time; <- TODO: Discuss. This does not change so the invoice date would always be the date of the first payment
		const formattedOrderDate = format(new Date(), 'dd.MM.yyyy');

		const billingPlan = getBillingPlanRes.billingPlan;
		const address = subscriptionDetailsRes.data.subscriber.shipping_address.address;
		const name = subscriptionDetailsRes.data.subscriber.name;
		const lastPaymentAmount = subscriptionDetailsRes.data.billing_info.last_payment.amount.value;

		const invoicePdfBuffer = await generateInvoicePdf({
			invoiceId: invoiceDbId,
			invoiceDate: formattedOrderDate,
			brand: 'CINFINITY',
			isPaid: true,
			items: [
				{
					item: 'Subscription',
					description: `Subscription to Cinfinity`,
					priceInCents: getPriceTaxDetails(getPriceInCents(lastPaymentAmount))
						.priceWithoutTaxesInCents,
					taxesInCents: getPriceTaxDetails(getPriceInCents(lastPaymentAmount)).taxesInCents,
					quantity: 1,
				},
			],
			shipping: {
				name: `${name.given_name} ${name.surname}`,
				country: address.country_code,
				state: address.admin_area_1,
				city: address.admin_area_2,
				address: address.address_line_1,
				deliveryDate: formattedOrderDate,
			},
		});

		await db.queryOne(sql`
			UPDATE priv.p_invoice
			SET pdf_data = ${invoicePdfBuffer}
			WHERE id = ${invoiceDbId}
		`);
	}
	return { success: true };
};

export default createInvoice;
