/* eslint-disable react-perf/jsx-no-new-array-as-prop */
/* eslint-disable react-native/no-color-literals */

import { Document, Page, Text, View, Image, StyleSheet, renderToBuffer } from '@react-pdf/renderer';
import { format } from 'date-fns';
import fetch from 'node-fetch';
import React from 'react';

const formatCurrency = (cents) => {
	return Intl.NumberFormat('de-DE', { style: 'currency', currency: 'EUR' }).format(cents / 100);
};

const mmToPx = (px: number) => {
	return px * (1 / 0.3528);
};

const FONT_SIZE = 8;

type InvoiceType = {
	invoiceId: string;
	invoiceDate: string;
	shipping: {
		name: string;
		address: string;
		city: string;
		state: string;
		country: string;
		deliveryDate: string;
	};
	items: {
		item: string;
		description: string;
		priceInCents: number;
		taxesInCents: number;
		quantity: number;
	}[];
	brand: 'CINFINITY';
	isPaid?: boolean;
};

type Company = {
	companyName: string;
	companyStreet: string;
	companyCity: string;
	taxId: string;
	logo: string;
};

const getCompany = (brand: 'CINFINITY'): Company | undefined => {
	return brand === 'CINFINITY'
		? {
				companyName: 'Cinfinity GmbH',
				companyStreet: 'Theaterplatz 1',
				companyCity: '23909 Ratzeburg',
				taxId:
					'GF: Martin Turowski, Ralf Thomsen; Handelsregisternummer: HRB 23792 HL; USt-IdNr.: DE362226247',
				logo: 'https://static.cinuru.com/public/cinema-logos/cinfinity-header-red.png',
		  }
		: undefined;
};

const dict = {
	invoice: 'Rechnung',
	unitCost: 'Kosten',
	unitTaxes: 'Steuern (7%)',
	unitTotal: 'Gesamt',
	quantity: 'Anzahl',
	lineTotal: 'Kosten gesamt',
	subTotal: 'Zwischensumme',
	paidToDate: 'Bezahlt',
	balanceDue: 'Restbetrag',
	invoiceNumber: 'Rechnungsnummer',
	invoiceDate: 'Rechnungsdatum',
	item: 'Posten',
	description: 'Beschreibung',
	deliveryDate: 'Lieferdatum',
	taxId: 'Steuernummer',
	address: 'Adresse',
} as const;

const getLabel = (key: keyof typeof dict) => {
	return dict[key] || key;
};

const styles = StyleSheet.create({
	page: {
		paddingTop: mmToPx(20),
		paddingRight: mmToPx(20),
		paddingBottom: mmToPx(10),
		paddingLeft: mmToPx(25),
		flexDirection: 'column',
		justifyContent: 'flex-start',
		gap: 20,
		fontSize: FONT_SIZE,
	},
	companyInfo: {
		marginLeft: 'auto',
		flexDirection: 'column',
		alignItems: 'flex-start',
		gap: 8,
	},
	companyDetailsBlock: {
		width: mmToPx(80),
		height: mmToPx(30),
		flexDirection: 'column',
		justifyContent: 'space-evenly',
		alignItems: 'flex-start',
	},
	companyDetailsText: {
		fontSize: 6,
	},
	alignRight: {
		textAlign: 'right',
	},
	logo: {
		width: 150,
		height: 'auto',
		paddingBottom: 12,
	},
	table: {
		width: '100%',
	},
	tableRow: {
		flexDirection: 'row',
		alignItems: 'center',
		borderBottomWidth: 1,
		borderBottomColor: '#aaaaaa',
		paddingVertical: 12,
	},
	tableHeader: {
		fontFamily: 'Helvetica-Bold',
	},
	tableCell: {
		width: 100,
	},
	tableCellRight: {
		flex: 1,
		textAlign: 'center',
	},
	footer: {
		marginTop: 'auto',
		flexDirection: 'row',
		justifyContent: 'space-evenly',
		alignItems: 'center',
	},
	footerItem: {
		flexDirection: 'column',
	},
});

const InvoiceDocument: React.FC<{
	invoice: InvoiceType;
	company: Company;
	imageBuffer: Buffer;
}> = ({ invoice, company, imageBuffer }) => {
	const total = formatCurrency(
		invoice.items.reduce((acc, item) => acc + item.priceInCents + item.taxesInCents, 0)
	);

	return (
		<Document>
			<Page size="A4" style={styles.page}>
				<View style={styles.companyInfo}>
					<Image src={imageBuffer} style={styles.logo} />
					<View>
						<Text>{company.companyName}</Text>
						<Text>{company.companyStreet}</Text>
						<Text>{company.companyCity}</Text>
					</View>
					<View>
						<Text>Tel.: +49 4541 - 885 82 - 0</Text>
						<Text>Fax: +49 4541 - 885 82 - 99</Text>
						<Text><EMAIL></Text>
						<Text>www.cinfinity.de</Text>
					</View>
				</View>
				<View style={styles.companyDetailsBlock}>
					<Text style={styles.companyDetailsText}>
						{company.companyName} | {company.companyStreet} | {company.companyCity}
					</Text>
					<View>
						<Text>{invoice.shipping.name}</Text>
						<Text>{invoice.shipping.address}</Text>
						<Text>
							{invoice.shipping.city} {invoice.shipping.state ?? ''} {invoice.shipping.country}
						</Text>
					</View>
				</View>
				<Text style={styles.alignRight}>{format(new Date(invoice.invoiceDate), 'dd.MM.yyyy')}</Text>
				<Text>
					{getLabel('invoice')} {invoice.invoiceId}
				</Text>

				<View style={styles.table}>
					<View style={[styles.tableRow, styles.tableHeader]}>
						<Text style={styles.tableCell}>{getLabel('description')}</Text>
						<Text style={styles.tableCellRight}>{getLabel('unitCost')}</Text>
						<Text style={styles.tableCellRight}>{getLabel('unitTaxes')}</Text>
						<Text style={styles.tableCellRight}>{getLabel('unitTotal')}</Text>
						<Text style={styles.tableCellRight}>{getLabel('quantity')}</Text>
						<Text style={styles.tableCellRight}>{getLabel('lineTotal')}</Text>
					</View>

					{invoice.items.map((item, index) => (
						<View key={index} style={styles.tableRow}>
							<Text style={styles.tableCell}>{item.description}</Text>
							<Text style={styles.tableCellRight}>{formatCurrency(item.priceInCents)}</Text>
							<Text style={styles.tableCellRight}>{formatCurrency(item.taxesInCents)}</Text>
							<Text style={styles.tableCellRight}>
								{formatCurrency(item.priceInCents + item.taxesInCents)}
							</Text>
							<Text style={styles.tableCellRight}>{item.quantity.toString()}</Text>
							<Text style={styles.tableCellRight}>
								{formatCurrency((item.priceInCents + item.taxesInCents) * item.quantity)}
							</Text>
						</View>
					))}

					<View style={styles.tableRow}>
						<Text style={styles.tableCell}>Gesamtkosten</Text>
						<Text style={styles.tableCellRight}></Text>
						<Text style={styles.tableCellRight}></Text>
						<Text style={styles.tableCellRight}></Text>
						<Text style={styles.tableCellRight}></Text>
						<Text style={styles.tableCellRight}>{total}</Text>
					</View>
				</View>
				<View style={styles.footer}>
					<View style={styles.footerItem}>
						<Text>CINFINITY GmbH</Text>
						<Text>GF: Ralf Thomsen,</Text>
						<Text>Martin Turowski</Text>
					</View>
					<View>
						<Text>Sitz der Gesellschaft: Ratzeburg</Text>
						<Text>Amtsgericht Lübeck HRB 23792 HL</Text>
						<Text>USt.ID DE362226247</Text>
					</View>
					<View>
						<Text>VReG Ratzeburg</Text>
						<Text>DE56 2019 0109 0012 7721 90</Text>
						<Text>GENODEF1HH4</Text>
					</View>
				</View>
			</Page>
		</Document>
	);
};

const generateInvoicePdf = async (invoice: InvoiceType): Promise<Buffer> => {
	const company = getCompany(invoice.brand);
	if (!company) {
		throw new Error('Unknown brand');
	}

	const imageResponse = await fetch(company.logo);
	const imageBuffer = await imageResponse.buffer();

	const pdfDoc = <InvoiceDocument invoice={invoice} company={company} imageBuffer={imageBuffer} />;

	return await renderToBuffer(pdfDoc);
};

export default generateInvoicePdf;
