import { Request, Response, Express } from 'express';
import RateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';
import redis from 'redis';
import {
	RATE_LIMIT_OPTIONS as limitParams,
	REDIS_HOST,
	REDIS_PASSWORD,
	REDIS_TLS,
	USE_REDIS,
} from '../../consts';
import { sendErrorMail } from './sendEmail';

type Options = {
	windowMs: number;
	max: number;
	message: string;
	statusCode: number;
	headers: boolean;
	draft_polli_ratelimit_headers: boolean;
	skipFailedRequests: boolean;
	skipSuccessfulRequests: boolean;
	store: any;
};

const whitelistedIps = [
	'*************', // Compeso Customer DB Production IP
];

export const activateRateLimiter = (app: Express) => {
	if (USE_REDIS) {
		console.log('activating rate limiter');
		const client = redis.createClient({
			host: REDIS_HOST,
			password: REDIS_PASSWORD ? REDIS_PASSWORD : undefined,
			tls: REDIS_TLS ? { servername: REDIS_HOST } : undefined,
			prefix: 'ratelimit',
		});
		const externalRateLimiter = new RateLimit({
			store: new RedisStore({
				client: client,
				//in seconds
				expiry: limitParams.interval.m * 60 + limitParams.interval.s,
			}),
			max: limitParams.maxReqAmount,
			message: `ERROR: Can't handle too many requests at once.`,
			onLimitReached: async (req: Request, _res: Response, options: Options): Promise<Options> => {
				await sendErrorMail(
					'RATE_LIMITER',
					`limit-reached => limiter-options: ${JSON.stringify(options, null, 2)}
					requestIP: ${req.header('x-forwarded-for') || req.connection.remoteAddress}
					`
				);
				return options;
			},
			skipFailedRequests: true,
			skip: (req: Request) => {
				const ipAddress = req.header('x-forwarded-for') || req.connection.remoteAddress;
				if (whitelistedIps.includes(ipAddress)) {
					return true;
				}
				return false;
			},
		});
		//app.use(externalRateLimiter);
	}
};
