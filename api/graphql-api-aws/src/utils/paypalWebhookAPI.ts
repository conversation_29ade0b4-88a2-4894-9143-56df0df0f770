/* eslint-disable no-catch-all/no-catch-all */
import { Request, Response } from 'express';
import { db, sendErrorMail, sql } from '.';
import { DEFAULT_APP_BRAND, PAYPAL_WEBHOOK_URL } from '../../consts';
import {
	sendSubscriptionInfoEmail,
	syncSubscriptions,
} from '../graphql/Subscription/syncSubscriptions';
import {
	InternalBookingProcessId,
	InternalSubscriptionId,
	InternalUserId,
} from '../typescript-types';
import createInvoice from './invoice/createInvoice';
import { getPriceInCents } from './paypal/utils';
import {
	BillingSubscriptionCancelledEvent,
	BillingSubscriptionPaymentFailedEvent,
	getWebhooks,
	PaymentCaptureCompletedEvent,
	PaymentSaleCompletedEvent,
	PaypalEvent,
	PaypalEventType,
	verifyWebhookSignature,
} from './paypal/webhook';

export const maybeCreatePaymentEntry = async ({
	userId,
	paidPriceInCents,
	eventType,
	bookingProcessId,
	subscriptionId,
	paypalSaleId,
}:
	| {
			userId: InternalUserId;
			paidPriceInCents: number;
			eventType: PaypalEventType;
			bookingProcessId: InternalBookingProcessId;
			subscriptionId?: undefined;
			paypalSaleId: string;
	  }
	| {
			userId: InternalUserId;
			paidPriceInCents: number;
			eventType: PaypalEventType;
			bookingProcessId?: undefined;
			subscriptionId: InternalSubscriptionId;
			paypalSaleId: string;
	  }): Promise<number | undefined> => {
	const exists = await db.queryOne(sql`
		SELECT id FROM priv.p_payment WHERE paypal_sale_id = ${paypalSaleId}
	`);

	if (!exists) {
		const paymentId = (
			await db.queryOne(sql`
			INSERT INTO priv.p_payment
			(user_id, paypal_sale_id, subscription_id, booking_process_id, paid_price_in_cents, datetime, provider, status)
			VALUES
			(${userId}, ${paypalSaleId}, ${subscriptionId}, ${bookingProcessId}, ${paidPriceInCents}, NOW(), 'PAYPAL', ${eventType})
			RETURNING id
		`)
		).id;

		return paymentId;
	}

	return null;
};

const validatePaypalEvent = async (req: Request): Promise<boolean> => {
	const webhooksRes = await getWebhooks();
	if (!webhooksRes.success) {
		console.log('>>> validatePaypalEvent webhooksRes error: ', webhooksRes.errorMessage);
		return false;
	}
	const webhookId = webhooksRes.data.find((webhook) => webhook.url === PAYPAL_WEBHOOK_URL)?.id;
	const verifyWebhookSignatureRes = await verifyWebhookSignature({
		headers: req.headers as any,
		body: req.body as PaypalEvent,
		webhookId,
	});
	if (!verifyWebhookSignatureRes.success) {
		console.log(
			'>>> validatePaypalEvent verifyWebhookSignatureRes error: ',
			verifyWebhookSignatureRes.errorMessage
		);
		await sendErrorMail(
			'paypalWebhookAPI: invalid webhook request',
			`endpoint: ${PAYPAL_WEBHOOK_URL} body: ${JSON.stringify(req.body)}; headers: ${JSON.stringify(
				req.headers
			)}`
		);
		return false;
	}
	return true;
};

const subscriptionEventTypes = [
	'PAYMENT.SALE.COMPLETED',
	'BILLING.SUBSCRIPTION.PAYMENT.FAILED',
	'BILLING.SUBSCRIPTION.CANCELLED',
	'BILLING.SUBSCRIPTION.ACTIVATED',
];

const orderEventTypes = ['PAYMENT.CAPTURE.COMPLETED'];

export const paypalWebhookAPI = async (
	req: Request & {
		body: unknown;
		headers: { Authorization?: string };
	},
	res: Response
) => {
	let paypalEventId;
	try {
		const eventType = req.body.event_type as PaypalEventType;
		console.log('paypalWebhookAPI body: ', req.body);
		const isValidPaypalEvent = await validatePaypalEvent(req);
		if (!isValidPaypalEvent) {
			return res.sendStatus(200);
		}
		paypalEventId = (
			await db.query(sql`
			INSERT INTO priv.p_paypal_event 
			(event_type, datetime, body)
			VALUES
			(${req.body.event_type}, NOW(), ${JSON.stringify(req.body)})
			RETURNING id
		`)
		).rows[0].id;

		// subscription events
		if (subscriptionEventTypes.includes(eventType)) {
			let orderId;
			let paidPriceInCents = 0;
			if (eventType === 'PAYMENT.SALE.COMPLETED') {
				const body = req.body as PaymentSaleCompletedEvent;
				orderId = body.resource.billing_agreement_id;
				paidPriceInCents = getPriceInCents(body.resource.amount.total);
			}
			if (
				eventType === 'BILLING.SUBSCRIPTION.PAYMENT.FAILED' ||
				eventType === 'BILLING.SUBSCRIPTION.CANCELLED' ||
				eventType === 'BILLING.SUBSCRIPTION.ACTIVATED'
			) {
				const body = req.body as BillingSubscriptionPaymentFailedEvent;
				orderId = body.resource.id;
			}
			const subscription = (await db.queryOne(sql`
				SELECT 
				id, 
				user_id as "userId",
				valid_from as "validFrom",
				paypal_order_id as "paypalOrderId",
				blocked,
				blocked_reason as "blockedReason"
				FROM priv.p_subscription
				WHERE paypal_order_id = ${orderId}
			`)) as null | {
				id: InternalSubscriptionId;
				userId: InternalUserId;
				validFrom?: Date;
				paypalOrderId: string;
				blocked?: boolean;
				blockedReason?: string;
			};

			if (!subscription) {
				// this should not happen (but it did happen in the past), so we want to log it
				await sendErrorMail(
					`Unknown subscription!`,
					`Received paypal: ${eventType} for unknown subscription.paypal_order_id ${orderId}.`
				);
				// the orderId cannot be found in the subscriptions, therefore this event is not relevant for this api
				await db.query(
					sql`UPDATE priv.p_paypal_event SET status = 'NOT_FOUND' WHERE id = ${paypalEventId}`
				);
				return res.sendStatus(200);
			}

			// we want to update the subscription on every relevant event, so that we get the update as soon as possible and not just when syncSubscriptions is called during User generation
			// PAYMENT.SALE.COMPLETED: we need to update subscription.payedUntil
			// BILLING.SUBSCRIPTION.PAYMENT.FAILED: we need to adjust subscriptions.blocked
			// BILLING.SUBSCRIPTION.CANCELLED: we need update subscriptions.canceled, subscription.cancellationEffectiveAt and subscriptions.paypalStatus
			// BILLING.SUBSCRIPTION.ACTIVATED: we need to update s.paypalStatus, s.validFrom, s.payedUntil
			await syncSubscriptions({
				userId: subscription.userId,
			});

			// will be fired when
			// a) the subscription was initialized and we received the initial payment (not the case if it was for free e.g. via a subscription voucherCode, or if the payment is on hold)
			// b) we received a recurrent payment for the subscription
			if (eventType === 'PAYMENT.SALE.COMPLETED') {
				const saleId = (req.body as PaymentSaleCompletedEvent).resource.id;
				const newPaymentId = await maybeCreatePaymentEntry({
					userId: subscription.userId,
					subscriptionId: subscription.id,
					paidPriceInCents,
					eventType,
					paypalSaleId: saleId,
				});
				if (newPaymentId) {
					await createInvoice({
						paymentId: newPaymentId,
					});
					await sendSubscriptionInfoEmail({
						userDbId: subscription.userId,
						brand: DEFAULT_APP_BRAND,
						type: 'update',
					});
				}
			}
		}

		// order events
		if (orderEventTypes.includes(eventType)) {
			let orderId;
			if (eventType === 'PAYMENT.CAPTURE.COMPLETED') {
				const body = req.body as PaymentCaptureCompletedEvent;
				orderId = body.resource.supplementary_data.related_ids.order_id;
			}

			const bookingProcess = (await db.queryOne(sql`
				SELECT 
				id, 
				user_id as "userId", 
				price_in_cents as "priceInCents"
				FROM priv.p_booking_process
				WHERE paypal_order_id = ${orderId}
			`)) as null | {
				id: number;
				userId: number;
				priceInCents: number;
			};

			if (!bookingProcess) {
				// the orderId cannot be found in the bookingProcesses, therefore this event is not relevant for this api
				return res.sendStatus(200);
			}

			const saleId = (req.body as PaymentCaptureCompletedEvent).resource.id;

			if (eventType === 'PAYMENT.CAPTURE.COMPLETED') {
				const paymentId = await maybeCreatePaymentEntry({
					userId: bookingProcess.userId,
					paidPriceInCents: bookingProcess.priceInCents,
					eventType,
					bookingProcessId: bookingProcess.id,
					paypalSaleId: saleId,
				});
				if (paymentId) {
					await createInvoice({
						paymentId,
					});
				}
			}
		}
		await db.query(
			sql`UPDATE priv.p_paypal_event SET status = 'SUCCESS' WHERE id = ${paypalEventId}`
		);
		res.sendStatus(200);
	} catch (error) {
		console.error('paypalWebhookAPI error: ', error);
		await db.query(
			sql`UPDATE priv.p_paypal_event SET status = 'ERROR' WHERE id = ${paypalEventId}`
		);
		await sendErrorMail(
			'paypalWebhookAPI: ran into catch block',
			`error: ${JSON.stringify(error)}, endpoint: ${PAYPAL_WEBHOOK_URL}, body: ${JSON.stringify(
				req.body
			)}, headers: ${JSON.stringify(req.headers)}`
		);
		res.sendStatus(500);
	}
};
