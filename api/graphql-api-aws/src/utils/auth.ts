import { COOKIE_SECRET, DEFAULT_APP_LANGUAGE, STAGE } from '../../consts';

/*
	Purpose: Authentication on incoming request.
	This code handles all the logic to authenticate a user on incoming requests. For example It
	verifies the jwt and csrf tokens and creates a viewer object from it used in the resolvers
	It doesn't handle login state/mutations. All code related to login or out is located in
	mutations/login. As login and logout (changing auth state) is different than authentication
	(verifying it) this seperation makes sense (at least for now). There is only a slight deviation
	from this separation: to prolong the validity of a session, we transform the session and jwt.
*/

import cors from 'cors';
import cookieSession from 'cookie-session';
import CSRFTokens from 'csrf';
import url from 'url';
import { createJWT, createViewerFromJWT } from './authHelpers';
import { AuthenticationError, ForbiddenError } from './errors';

import { Language } from '@cinuru/utils/i18n';
import { Viewer } from '../typescript-types';

const csrf = new CSRFTokens();

const refreshJWT = (
	data: {
		[key: string]: unknown;
	},
	expiresIn: string
) => {
	delete data.iat;
	delete data.exp;
	delete data.nbf;
	delete data.jti;
	return createJWT(data, expiresIn);
};

export const authenticate = (req) => {
	const ipAddress = (req.header('x-forwarded-for') || req.connection.remoteAddress || '').split(
		','
	)[0];
	const userAgent = req.header('user-agent') || '';
	const requestId = req.header('requestId');
	const clientVersion = req.header('version');

	let language = 'de';
	const brand = req.header('brand');
	if (
		brand &&
		['CINURU', 'CINEPLEX', 'LUMOS', 'CINEWEB', 'ELSELICHTSPIELE', 'DREHWERK', 'CINFINITY'].includes(
			brand
		)
	) {
		language += '-' + brand.toLowerCase();
	} else if (brand === 'NONE') {
		language = 'de'; // the dashboard will send NONE on the login request and retrieve the actual brand from the api response to set the correct brand in following requests
	} else {
		language = DEFAULT_APP_LANGUAGE; // in old apps header.brand will be undefined
	}
	console.log(language);

	// header token authentication
	const authHeaderToken = req.header('authorization');
	if (authHeaderToken) {
		const viewer = createViewerFromJWT(
			authHeaderToken.substr(7),
			language as Language,
			null,
			ipAddress,
			userAgent,
			requestId,
			clientVersion
		);
		if (viewer.decodedJWT) {
			return viewer;
		} else {
			req.session = null;
			throw new AuthenticationError('INVALID_JWT');
		}
	}
	// cookie session authentication
	const csrfToken = req.get('CSRF-Token');
	const sessionCookieToken = req.session && req.session.jwt;
	if (sessionCookieToken && csrfToken) {
		// check csrf token header against csrf cookie
		if (csrf.verify(req.session.secret, csrfToken)) {
			const viewer = createViewerFromJWT(
				sessionCookieToken,
				language as Language,
				null,
				ipAddress,
				userAgent,
				requestId
			);
			if (viewer.decodedJWT) {
				// prolong validity of jwt and update csrf token
				req.session.jwt = refreshJWT(viewer.decodedJWT, req.session.expiresIn);
				return viewer;
			} else {
				req.session = null;
				// return viewer anyways, errors will be thrown, when accessing viewer
				return createViewerFromJWT(
					null,
					language as Language,
					'INVALID_JWT',
					ipAddress,
					userAgent,
					requestId
				);
			}
		} else {
			req.session = null;
			// return viewer anyways, errors will be thrown, when accessing viewer
			return createViewerFromJWT(
				null,
				language as Language,
				'INVALID_CSRF',
				ipAddress,
				userAgent,
				requestId
			);
		}
	}
	// not authenticated, handle errors with viewer
	return createViewerFromJWT(null, language as Language, null, ipAddress, userAgent, requestId);
};

export const corsConfig = {
	origin: (origin, resolve) => {
		// allow apps
		if (!origin || origin === 'null') {
			resolve(null, true);
			return;
		}
		// allow local testing environments
		const currentUrl = url.parse(origin);
		if (
			currentUrl.hostname === 'd33ayamkx1rwq2.cloudfront.net' ||
			currentUrl.hostname === 'cinuru-dashboard.s3-eu-west-1.amazonaws.com' ||
			currentUrl.hostname === 'v80qrfuevk.execute-api.eu-central-1.amazonaws.com' ||
			currentUrl.hostname === 'cinuru.webflow.io' ||
			currentUrl.hostname === 'localhost' ||
			currentUrl.hostname === '127.0.0.1' ||
			currentUrl.hostname.substring(0, 3) === '10.' ||
			currentUrl.hostname.substring(0, 8) === '192.168.'
		) {
			resolve(null, true);
			return;
		}
		// allow cinuru.com and cineplex.de subdomains
		const hostnameParts = currentUrl.hostname.split('.');
		if (hostnameParts.length > 1) {
			const tld = hostnameParts[hostnameParts.length - 1];
			const domain = hostnameParts[hostnameParts.length - 2];
			if (
				(tld === 'com' && domain === 'cinuru') ||
				(tld === 'de' && domain === 'cinfinity') ||
				(tld === 'de' && domain === 'cineplex')
			) {
				resolve(null, true);
				return;
			}
		}
		// forbid everything else
		resolve(new Error('Not allowed by CORS'));
	},
	credentials: true,
};

export const applyAuthMiddleware = (app) => {
	app.use(
		'*',
		cookieSession({
			name: 'session',
			keys: [COOKIE_SECRET],
			secure: process.env.NODE_ENV === 'production',
			httpOnly: true,
			maxAge: 356 * 24 * 60 * 60 * 1000, // 1 year, real expiration is handled in jwt
			sameSite:
				STAGE === 'cinuruStagingDev' ||
				STAGE === 'cinuruStagingProd' ||
				STAGE === 'cinfinityRelease' || // TODO: Remove this line after cinfinity release
				STAGE === 'cinfinityStaging'
					? 'none'
					: 'strict',
		})
	);
	app.use('*', cors(corsConfig));
};

export const ensureIsAdmin = (viewer: Viewer) => {
	if (!viewer.isAdmin && !viewer.isRoot) {
		throw new ForbiddenError('This query is only available for admin users');
	}
};

export const ensureIsLoggedInAndNotAnonymous = (viewer: Viewer) => {
	if (!viewer.hasUserId && !viewer.isAnonymous) {
		throw new ForbiddenError('This query is only available for logged in users');
	}
};
