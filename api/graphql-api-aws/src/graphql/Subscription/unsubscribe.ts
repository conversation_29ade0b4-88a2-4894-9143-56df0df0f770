import { renderEmailTemplate } from '@cinuru/emails';
import { Brand } from '@cinuru/utils';
import { ApolloError, ForbiddenError } from 'apollo-server';
import { addMonths, subDays } from 'date-fns';
import { Subscription } from '..';
import {
	Context,
	InternalSubscriptionId,
	InternalUserId,
	Language,
	PublicSubscriptionId,
} from '../../typescript-types';
import { db, sql } from '../../utils';
import { getFixedI18n } from '../../utils/i18nHelpers';
import { getUserName } from '../../utils/nameHelpers';
import {
	cancelSubscription,
	getSubscription,
	PaypalSubscription,
} from '../../utils/paypal/subscription';
import { sendEmail } from '../../utils/sendEmail';
import { BillingCycleConfig } from '../../utils/paypal/billingPlan';

export const getCancelInfos = async (
	subscription: PaypalSubscription
): Promise<{
	cancellationEffectiveDate: Date | null;
	canPaypalCancelSubscription: boolean | null;
	success: boolean;
}> => {
	try {
		const trialPhase = subscription.billing_info.cycle_executions.find(
			(cycle) => cycle.tenure_type === 'TRIAL'
		);
		const regularPhase = subscription.billing_info.cycle_executions.find(
			(cycle) => cycle.tenure_type === 'REGULAR'
		);
		const billingCyclesConfig = (
			await db.queryOne(
				sql`SELECT billing_cycles_config FROM priv.p_subscription_tier WHERE paypal_billing_plan_id = ${subscription.plan_id} AND is_hidden = false`
			)
		).billing_cycles_config as BillingCycleConfig[];

		const currentPhase = regularPhase.cycles_completed === 0 ? 'TRIAL' : 'REGULAR';
		const subscriptionType = billingCyclesConfig[0].total_cycles === 1 ? 'UPFRONT' : 'MONTHLY';

		// UPFRONT-4M TRIAL
		// UPFRONT-4M REGULAR
		// UPFRONT-12M TRIAL
		// UPFRONT-12M REGULAR
		// MONTHLY-4M TRIAL
		// MONTHLY-4M REGULAR
		// MONTHLY-12M TRIAL
		// MONTHLY-12M REGULAR

		let canPaypalCancelSubscription = null;
		let cancellationEffectiveDate = null;
		if (currentPhase === 'REGULAR') {
			const regularPhaseCyclesCompleted = regularPhase.cycles_completed;
			const trialPhaseTotalMonths =
				subscriptionType === 'UPFRONT'
					? billingCyclesConfig[0].frequency.interval_count
					: billingCyclesConfig[0].total_cycles;
			cancellationEffectiveDate = subDays(
				addMonths(
					new Date(subscription.start_time),
					trialPhaseTotalMonths + regularPhaseCyclesCompleted
				),
				1
			);
			canPaypalCancelSubscription = true;
		} else if (currentPhase === 'TRIAL') {
			if (subscriptionType === 'UPFRONT') {
				const trialPhaseTotalMonths = billingCyclesConfig[0].frequency.interval_count;
				cancellationEffectiveDate = subDays(
					// if a voucher Code was used, lastPayment will be null, so we use the subscription start time instead here
					addMonths(new Date(subscription.start_time), trialPhaseTotalMonths),
					1
				);
				canPaypalCancelSubscription = true;
			}
			if (subscriptionType === 'MONTHLY') {
				const trialPhaseCyclesCompleted = trialPhase.cycles_completed;
				const trialPhaseCyclesRemaining = trialPhase.cycles_remaining;
				cancellationEffectiveDate = subDays(
					addMonths(
						new Date(subscription.start_time),
						trialPhaseCyclesCompleted + trialPhaseCyclesRemaining
					),
					1
				);
				canPaypalCancelSubscription = false;
			}
		}

		console.log({
			subscription,
			cancellationEffectiveDate,
			canPaypalCancelSubscription,
			currentPhase,
			subscriptionType,
		});

		return {
			cancellationEffectiveDate,
			canPaypalCancelSubscription,
			success: typeof canPaypalCancelSubscription === 'boolean' && cancellationEffectiveDate,
		};
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (error) {
		return {
			cancellationEffectiveDate: null,
			canPaypalCancelSubscription: null,
			success: false,
		};
	}
};

export const unsubscribeFunction = async ({
	subscriptionDbId,
	userIdDb,
	brand,
}: {
	subscriptionDbId: InternalSubscriptionId;
	userIdDb: InternalUserId;
	brand: Brand;
}) => {
	const existingSubscription = await db.queryOne<{
		id: number;
		userId: number;
		canceled: boolean;
		subscriptionTierName: string;
		isAutoRenewal: string;
		paypalOrderId: string;
	}>(
		sql`
			SELECT 
			s.id, 
			s.user_id as "userId", 
			s.canceled,
			s.paypal_order_id as "paypalOrderId",
			st.is_auto_renewal as "isAutoRenewal",
			st.name as "subscriptionTierName"
		 	FROM priv.p_subscription s
			JOIN priv.p_subscription_tier st ON s.subscription_tier_id = st.id
			WHERE s.id = ${subscriptionDbId}
		`
	);
	if (!existingSubscription) {
		throw new ApolloError('SUBSCRIPTION_DOES_NOT_EXIST');
	}
	if (existingSubscription.userId !== userIdDb) {
		throw new ForbiddenError('UNSUBSCRIBING_ONLY_POSSIBLE_FOR_YOUR_OWN_SUBSCRIPTIONS');
	}
	if (!existingSubscription.isAutoRenewal) {
		throw new ApolloError('CANT_UNSUBSCRIBE_FROM_COMPANY_PROVIDED_SUBSCRIPTION');
	}
	if (existingSubscription.canceled) {
		throw new ApolloError('SUBSCRIPTION_ALREADY_CANCELED');
	}

	const subscriptionInfoRes = await getSubscription(existingSubscription.paypalOrderId);
	if (!subscriptionInfoRes.success) {
		throw new ApolloError(subscriptionInfoRes.errorMessage, 'PAYPAL_SUBSCRIPTION_INFO_FAILED');
	}

	const { cancellationEffectiveDate, canPaypalCancelSubscription, success } = await getCancelInfos(
		subscriptionInfoRes.data
	);

	if (!success) {
		throw new ApolloError('CANNOT_CANCEL_SUBSCRIPTION', 'CANNOT_CANCEL_SUBSCRIPTION');
	}

	if (canPaypalCancelSubscription) {
		const cancelSubscriptionRes = await cancelSubscription(existingSubscription.paypalOrderId);
		if (!cancelSubscriptionRes.success) {
			throw new ApolloError(
				cancelSubscriptionRes.errorMessage,
				'PAYPAL_SUBSCRIPTION_CANCELATION_FAILED'
			);
		}
	}

	await db.query(
		sql`
			UPDATE priv.p_subscription SET 
			canceled = TRUE,
			canceled_at = NOW(),
			updated_at = NOW(),
			cancellation_effective_at = ${cancellationEffectiveDate}, 
			paypal_status = ${canPaypalCancelSubscription ? 'CANCELLED' : subscriptionInfoRes.data.status}
			WHERE id = ${subscriptionDbId}`
	);

	const subscriptionTierName = existingSubscription.subscriptionTierName;

	const user = await db.queryOne(
		sql`SELECT email, first_name as "firstName", last_name as "lastName", username as "userName"  FROM priv.p_user WHERE id = ${userIdDb}`
	);
	const i18n = getFixedI18n(`de-${brand.toLowerCase()}` as Language);

	await sendEmail({
		to: user.email,
		from: `"${i18n.t('Global.appName')}" <${i18n.t('Global.transactionalOutboundMail')}>`,
		...renderEmailTemplate({
			template: 'CancelledSubscriptionInfoEmail',
			name: getUserName({
				firstName: user.firstName,
				lastName: user.lastName,
				userName: user.userName,
				brand: brand,
			}),
			brand: brand,
			subscriptionTierName,
		}),
	});
};

export const unsubscribe = async (
	_: unknown,
	{
		subscriptionId,
	}: {
		subscriptionId: PublicSubscriptionId;
	},
	context?: Context
) => {
	const userDbId = context?.viewer?.userIdDb;
	if (!userDbId) {
		return new ForbiddenError('UNSUBSCRIBING_REQUIRES_REGISTRATION');
	}

	await unsubscribeFunction({
		subscriptionDbId: Subscription.decodePublicId(subscriptionId),
		userIdDb: userDbId,
		brand: context.brand,
	});

	const userSubscriptionIds = (
		await db.query(
			sql`SELECT id FROM priv.p_subscription WHERE user_id = ${context.viewer.userIdDb}`
		)
	).rows.map(({ id }) => Subscription.encodeDbId(id));

	if (!userSubscriptionIds.length) {
		return [];
	}
	const res = await Subscription.genMult(userSubscriptionIds);
	return res;
};
