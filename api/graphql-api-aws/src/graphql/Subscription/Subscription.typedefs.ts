import { gql } from 'apollo-server-core';

export const typeDef = gql`
	extend type Mutation {
		subscribe(subscriptionTierId: ID, voucherCode: String): SubscribeResponse!
		reportApprovedSubscription(paypalApproveLink: String!): [Subscription!]!
			@deprecated(
				reason: "[1350/1351] completed subscriptions will be recognized via User.subscriptions"
			)
		unsubscribe(subscriptionId: ID!): [Subscription!]!
		createUserTokenForSubscriptionValidation: UserTokenResponse!
		logUserScreeningInterests(
			cinemaId: ID!
			screeningId: ID!
			linkedUserIds: [ID!]
		): [LogUserScreeningInterestResponse!]!
		reportScreeningError(
			voucherCode: String!
			userId: ID # this is actually not required anymore, but we need to keep it for backwards compatibility of old apps
			screeningId: ID!
		): ReportScreeningErrorResponse! @deprecated(reason: "[>1394] rely on prevalidation")
		validateVoucherCode(voucherCode: String!): ValidateVoucherCodeResponse!
	}

	extend type Query {
		validateSubscriptionUsers(screeningId: ID!): ValidateSubscriptionUsersResponse
	}

	type ValidateVoucherCodeResponse {
		title: String
		description: String
		company: Company
	}

	type ValidateSubscriptionUsersResponse {
		maxLinkedAccounts: Int!
		validatedUsers: [ValidatedUser!]!
	}

	type ValidatedUser {
		userId: ID!
		userName: String
		allowed: Boolean
		errorMessage: String
	}

	type ReportScreeningErrorResponse {
		nvc: String
	}

	type LogUserScreeningInterestResponse {
		userId: ID!
		vc: String
		userName: String
		errorMessage: String
	}

	type UserTokenResponse {
		userToken: String!
	}

	type SubscribeResponse {
		approveLink: String
		infoText: String
			@deprecated(
				reason: "not used anymore, but we need to keep it for backwards compatibility of old apps"
			)
	}

	enum BlockedReason {
		NO_SHOW
		PAYMENT_FAILED
		OTHER
	}
	type Subscription {
		id: ID!
		subscriptionTier: SubscriptionTier!
		validFrom: Date
		payedUntil: Date
		blocked: Boolean
		active: Boolean
		canceled: Boolean
		company: Company
		noShowWarningText: String
		blockedText: String
		blockedReason: BlockedReason
		cancellationEffectiveDate: Date
		paypalOrderId: String
		paypalStatus: String
	}
`;
