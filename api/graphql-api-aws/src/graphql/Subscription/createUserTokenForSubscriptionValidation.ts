import { AuthenticationError } from 'apollo-server';
import { Context } from '../../typescript-types';
import { db, sql } from '../../utils';
import crypto from 'crypto';
import { USER_TOKEN_PREFIX } from '../../../consts';

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const genUserToken = async () => {
	const userToken = crypto.randomBytes(7).toString('hex').toUpperCase();

	const userTokenWithPrefix = `${USER_TOKEN_PREFIX}${userToken}`;
	const exists =
		(
			await db.queryOne(
				sql`
                    SELECT COUNT (*) FROM priv.p_user_token 
                    WHERE token = ${userTokenWithPrefix}`
			)
		).count > 0;
	if (exists) {
		await delay(500);
		return genUserToken();
	} else {
		return userTokenWithPrefix;
	}
};

export const createUserTokenForSubscriptionValidation = async (
	_: unknown,
	__,
	context?: Context
) => {
	const userIdDb = context?.viewer?.userIdDb;
	if (!userIdDb) {
		return new AuthenticationError('Not authenticated');
	}

	let res, userToken;
	do {
		// create token until it is unique
		const userToken = await genUserToken();
		res = await db.queryOne(sql`
		INSERT INTO priv.p_user_token (user_id, token, created_at)
		VALUES (${userIdDb}, ${userToken}, NOW())
		ON CONFLICT (token) DO NOTHING
		RETURNING token,id
	`);
	} while (res.rows.length == 0);

	return { userToken };
};
