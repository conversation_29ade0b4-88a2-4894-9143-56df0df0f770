import { ApolloError, ForbiddenError } from 'apollo-server';
import { Context, InternalUserId } from '../../typescript-types';
import { db, sql } from '../../utils';
import { Company } from '../Company/Company';
import { DEFAULT_APP_BRAND, IsCinfinity } from '../../../consts';

export const validateVoucherCodeFunction = async ({
	voucherCode,
	userIdDb,
}: {
	voucherCode: string;
	userIdDb: InternalUserId;
}): Promise<{
	success: boolean;
	title?: string;
	description?: string;
	company_id?: string;
	subscription_tier_id?: string;
}> => {
	const res = (await db.queryOne(sql`
		SELECT 
		v.valid,
		v.user_id,
		v.company_id,
		vt.subscription_tier_id,
		vt.title, 
		vt.description 
		FROM priv.p_voucher v
		JOIN priv.p_voucher_types vt ON v.voucher_type_id = vt.id
		WHERE v.qr_code = ${voucherCode}
		AND vt.active IS TRUE
		`)) as null | {
		valid: boolean;
		title: string;
		description: string;
		company_id?: string;
		user_id?: InternalUserId;
		subscription_tier_id?: string;
	};

	if (!res || !res.valid || (res.user_id && res.user_id !== userIdDb)) {
		return { success: false };
	}

	// we assign the user that has first validated the voucher, so that the voucher can't be used by multiple users
	if (!res.user_id) {
		await db.query(sql`
			UPDATE priv.p_voucher
			SET user_id = ${userIdDb}
			WHERE qr_code = ${voucherCode}
		`);
	}

	return {
		success: true,
		title: res.title,
		company_id: res.company_id,
		description: res.description,
		subscription_tier_id: res.subscription_tier_id,
	};
};

export const validateVoucherCode = async (
	_: unknown,
	{
		voucherCode,
	}: {
		voucherCode: string;
	},
	context?: Context
) => {
	const userDbId = context?.viewer?.userIdDb;
	if (!userDbId) {
		return new ForbiddenError('Unauthenticated');
	}

	const { success, title, description, company_id } = await validateVoucherCodeFunction({
		voucherCode,
		userIdDb: userDbId,
	});

	if (!success) {
		throw new ApolloError('INVALID_VOUCHER_CODE', 'INVALID_VOUCHER_CODE');
	}

	return {
		title,
		description,
		company: () => {
			return IsCinfinity && company_id ? Company.gen(context.viewer, company_id, true) : null;
		},
	};
};
