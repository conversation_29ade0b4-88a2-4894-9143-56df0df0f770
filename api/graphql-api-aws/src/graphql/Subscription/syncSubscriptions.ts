/* eslint-disable no-await-in-loop */
/* eslint-disable no-catch-all/no-catch-all */

import { renderEmailTemplate } from '@cinuru/emails';
import { Brand } from '@cinuru/utils';

import { DEFAULT_APP_BRAND } from '../../../consts';
import {
	InternalSubscriptionId,
	InternalUserId,
	Language,
	SubscriptionStatus,
} from '../../typescript-types';
import { db, sendEmail, sendErrorMail, sql } from '../../utils';
import { getFixedI18n } from '../../utils/i18nHelpers';
import { getUserName } from '../../utils/nameHelpers';
import { getSubscription } from '../../utils/paypal/subscription';
import { getCancelInfos } from './unsubscribe';
import { addDays, addHours } from 'date-fns';

// what this function does:
// make sure to find subscriptions where a payment provider is involved that used to be incomplete and are now active -> send email, invalidate voucher code
// block subscription if subscription is active but payment is outstanding

// it gets called
// - on User.subscriptions: to make sure the subscription is up to date
// - on subscribe: to make sure the user cannot subscribe twice
// - on validateUserCanBuySubscriptionTicket: to make sure the user can only buy a ticket if the sub is still valid
// - when user enters the SubscriptionTierSelectView: to make sure we have the latest subscription status when the user comes back from paypal
// - on these webhook events: 'PAYMENT.SALE.COMPLETED'

export const syncSubscriptions = async ({ userId }: { userId: InternalUserId }) => {
	const userSubscriptons = (
		await db.query<{
			id: InternalSubscriptionId;
			valid_from: Date;
			created_at: Date;
			paypal_order_id: string;
			paypal_approve_link: string;
			user_id: InternalUserId;
			paypal_status: SubscriptionStatus;
			payed_until?: Date;
			voucher_code?: string;
			blocked?: boolean;
			blocked_reason?: string;
			canceled?: boolean;
			cancellation_effective_at?: Date;
		}>(sql`
            SELECT
            id,
            valid_from,
            created_at,
            paypal_order_id,
            paypal_approve_link,
            user_id,
            paypal_status,
            payed_until,
            voucher_code,
			blocked,
			blocked_reason,
			canceled,
			cancellation_effective_at
            FROM priv.p_subscription
            WHERE user_id = ${userId} AND paypal_order_id IS NOT NULL
      `)
	).rows;

	const subscriptionsToSync = userSubscriptons.filter(
		(s) =>
			!s.paypal_status || // could be active now
			s.paypal_status === 'APPROVAL_PENDING' || // could be active now
			s.paypal_status === 'APPROVED' || // could be active now
			s.paypal_status === 'ACTIVE' || // for these we need to update paidUntil, maybe they become blocked
			(s.paypal_status === 'CANCELLED' && s.cancellation_effective_at > new Date()) // these may become blocked
	);

	// if there are incomplete subscriptions, check the latest one for its status
	const subscriptionsToSyncInfos = await Promise.all(
		subscriptionsToSync.map(async (incompleteSubscriptionInfo) => ({
			paypal: await getSubscription(incompleteSubscriptionInfo.paypal_order_id),
			db: incompleteSubscriptionInfo,
		}))
	);

	const newActiveSubscriptions = subscriptionsToSyncInfos.filter(
		(s) =>
			s.paypal.success &&
			s.db.created_at > new Date('2025-02-07 11:00:00') &&
			s.paypal.data.status === 'ACTIVE' &&
			(s.db.paypal_status === 'APPROVAL_PENDING' ||
				s.db.paypal_status === 'APPROVED' ||
				!s.db.paypal_status)
	);

	if (newActiveSubscriptions.length > 0) {
		for (const subscription of newActiveSubscriptions) {
			if (subscription.db.voucher_code) {
				const voucherCodeRes = await db.queryOne(
					sql`SELECT valid FROM priv.p_voucher WHERE qr_code = ${subscription.db.voucher_code}`
				);
				if (!voucherCodeRes) {
					sendErrorMail(
						'voucher code not found',
						`voucher code ${subscription.db.voucher_code} not found in db`
					);
				} else if (!voucherCodeRes.valid) {
					sendErrorMail(
						'voucher code already redeemed',
						`about to set voucher code ${subscription.db.voucher_code} for subscription ${subscription.db.id} to redeemed, but the voucher code is already redeemed`
					);
				} else {
					await db.query(
						sql`
							UPDATE priv.p_voucher SET
							valid = FALSE,
							redeemed_datetime = NOW()
							WHERE qr_code = ${subscription.db.voucher_code}
						`
					);
				}
			}
			sendSubscriptionInfoEmail({
				userDbId: subscription.db.user_id,
				brand: DEFAULT_APP_BRAND,
				type: 'initial',
			});
		}
	}

	for (const subscription of subscriptionsToSyncInfos) {
		const error = !subscription.paypal.success;
		if (error) {
			const invalidResourceId = subscription.paypal.errorMessage?.includes('INVALID_RESOURCE_ID');
			if (invalidResourceId) {
				await db.query(
					sql`
						UPDATE priv.p_subscription SET
						paypal_status = 'INVALID_RESOURCE_ID',
						updated_at = NOW()
						WHERE id = ${subscription.db.id}
					`
				);
			}
			// if we got another error here, we assume it to be a network problem and dont sync
			continue;
		}

		if (subscription.paypal.data.status === 'CANCELLED' && !subscription.db.canceled) {
			// this is a cancelled subscription that was not synced to our database yet (can only happen if the user cancelled it via paypal dashboard), so we need to sync it now
			const { cancellationEffectiveDate, success } = await getCancelInfos(subscription.paypal.data);
			if (!success) {
				sendErrorMail(`cannot getCancelInfos`, `subscription ${subscription.db.id}`);
				continue;
			}

			await db.query(
				sql`
					UPDATE priv.p_subscription SET 
					canceled = TRUE,
					cancellation_effective_at = ${cancellationEffectiveDate}
					WHERE id = ${subscription.db.id}`
			);
		}

		const today = new Date();

		const validFrom = subscription.db.valid_from || subscription.paypal.data.start_time;

		const hasFailedPayments = subscription.paypal.data.billing_info?.failed_payments_count > 0;

		const paidUntil = !subscription.paypal.data.billing_info // if there is no billing info, the subscription is not payed
			? null
			: hasFailedPayments // if there are failed payments, we dont update the payed_until date: the next_billing_time on the paypal api will be the next interval irrespective of the failed payment
			? subscription.db.payed_until
			: subscription.paypal.data.billing_info.next_billing_time
			? addHours(new Date(subscription.paypal.data.billing_info.next_billing_time), 6)
			: subscription.db.payed_until;

		const paypalStatus = subscription.paypal.data.status;
		const isActiveSubscription =
			paypalStatus === 'ACTIVE' ||
			(paypalStatus === 'CANCELLED' && subscription.db.cancellation_effective_at > today);

		const useExtendedPaidUntil = userId === 31507 || userId === 38773;
		// for cancelled subscriptions, the payment can also be outstanding e.g. if the user cancels a monthly subscription in the trial phase via paypal dashboard
		const paymentOutstanding = hasFailedPayments
			? true
			: isActiveSubscription && today > (useExtendedPaidUntil ? addDays(paidUntil, 7) : paidUntil);

		const isBlockedForAnotherReason =
			subscription.db.blocked_reason && subscription.db.blocked_reason !== 'PAYMENT_FAILED';
		const isBlockedBecauseOfPaymentOutstanding =
			subscription.db.blocked_reason === 'PAYMENT_FAILED';
		const canBeUnblocked = isBlockedBecauseOfPaymentOutstanding && !paymentOutstanding;
		const isPaymentOutstandingBlocked = !isBlockedForAnotherReason && paymentOutstanding;

		const isBlocked =
			isBlockedForAnotherReason ||
			(isBlockedBecauseOfPaymentOutstanding && !canBeUnblocked) ||
			isPaymentOutstandingBlocked;

		const blockedReason = isBlockedForAnotherReason
			? subscription.db.blocked_reason
			: isBlockedBecauseOfPaymentOutstanding && !canBeUnblocked
			? 'PAYMENT_FAILED'
			: isPaymentOutstandingBlocked
			? 'PAYMENT_FAILED'
			: null;

		await db.query(
			sql`
				UPDATE priv.p_subscription SET
				valid_from = ${validFrom},
				payed_until = ${paidUntil},
				paypal_status = ${paypalStatus},
				updated_at = NOW(),
				blocked = ${isBlocked},
				blocked_reason = ${blockedReason}
				WHERE id = ${subscription.db.id}
			`
		);
	}

	const numberOfActiveSubscriptions = await db.queryOne(sql`
		SELECT COUNT(*) FROM priv.p_subscription 
		WHERE user_id = ${userId} 
		AND paypal_order_id IS NOT NULL AND (paypal_status = 'ACTIVE' OR paypal_status = 'CANCELLED' AND cancellation_effective_at > NOW())
	`);

	if (numberOfActiveSubscriptions.count > 1) {
		sendErrorMail(
			`Duplicate Subscription.`,
			`User ${userId} has more than one active subscription.`
		);
	}
};

export const sendSubscriptionInfoEmail = async ({
	userDbId,
	brand,
	type,
}: {
	userDbId: InternalUserId;
	brand: Brand;
	type: 'initial' | 'update';
}) => {
	const subscriptionTierName = 'Cinfinity';

	const user = await db.queryOne(
		sql`SELECT email, first_name as "firstName", last_name as "lastName", username as "userName" FROM priv.p_user WHERE id = ${userDbId}`
	);
	const i18n = getFixedI18n(`de-${brand.toLowerCase()}` as Language);

	const userName = getUserName({
		firstName: user.firstName,
		lastName: user.lastName,
		userName: user.userName,
		brand: brand,
	});

	const renderEmailOptions =
		type === 'update'
			? {
					name: userName,
					brand: brand,
					template: 'UpdatedSubscriptionInfoEmail' as const,
					subscriptionTierName,
			  }
			: {
					name: userName,
					brand: brand,
					template: 'CreatedSubscriptionInfoEmail' as const,
					subscriptionTierName,
			  };

	await sendEmail({
		to: user.email,
		from: `"${i18n.t('Global.appName')}" <${i18n.t('Global.transactionalOutboundMail')}>`,
		...renderEmailTemplate(renderEmailOptions),
	});
};
