import { gql } from 'apollo-server-core';

export const typeDef = gql`
	extend type Query {
		ticket(id: ID!): Ticket
	}

	extend type Mutation {
		cancelTicket(id: ID!): CancelTicketReturnType
		updateTicket(id: ID!, scanned: Boolean!): Ticket
		openTicket(id: ID!): Ticket
	}

	type Ticket {
		id: ID!
		screening: Screening!
		user: User!
		seats: [Seat!]!
		qrCode: String
		boughtAt: DateTime
		status: TicketStatus!
		refundedAt: DateTime
		refundable: Boolean!
		vouchers: [TicketVoucher!]
		onlineTicketingId: ID
		pkpass: String
		googlePayPass: String
		displayAllowedAfter: DateTime
		isFlatrateTicket: Boolean
		sponsoredBy: SponsoredBy
		auditoriumName: String
		cancelationLink: String
		provider: TicketProvider
		scanned: Boolean
		bluetoothRequired: Boolean
	}

	enum TicketProvider {
		CINETIXX
		KINOHELD
		MARS
		TICKET_INTERNATIONAL
		COMPESO
	}

	# <-- this can probably be replaced once the new Ticketing API is in place
	type Seat {
		id: ID!
		auditorium: Auditorium!
		rowName: String!
		seatName: String
		indexInRow: Int!
		x: Float!
		y: Float!
		width: Float!
		height: Float!
		type: SeatType!
		status: SeatStatus
		categoryId: ID!
		neighborLeftId: ID
		neighborRightId: ID
	}
	type Auditorium {
		id: ID!
		name: String
		cinema: Cinema!
		seats: [Seat!]!
		seatCategories: [SeatCategory!]!
		# screenings: [Screening!]!
	}
	type SeatCategory {
		id: ID!
		name: String
		prices: [Price!]!
	}
	type Price {
		id: ID!
		name: String
		description: String
		amount: Float
	}
	# --> this can probably be replaced once the new Ticketing API is in place

	type TicketVoucher {
		image: String
		qrCode: String!
	}
	enum TicketStatus {
		RESERVATION
		COMPLETE
		REFUNDED
		REFUND_FAILED
		REFUND_WAITING
		PREPARATION
		UNKNOWN
	}

	type CancelTicketReturnType {
		canceledTicket: Ticket
		currentUser: User
	}

	type SponsoredBy {
		name: String!
		logo: String!
	}
`;
