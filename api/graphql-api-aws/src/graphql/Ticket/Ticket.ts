import { differenceInMinutes } from 'date-fns';
import { Cinema, Movie, Screening, User } from '..';
import { CINEPLEX_BOOKING_SERVICE_URL, DEFAULT_APP_BRAND } from '../../../consts';
import {
	Context,
	InternalCinemaId,
	InternalMovieId,
	InternalScreeningId,
	InternalUserId,
	PublicCinemaId,
	TicketStatus,
	Viewer,
} from '../../typescript-types';
import {
	ApolloError,
	db,
	decodePublicId,
	encodeDbId,
	EntityNotFoundError,
	ForbiddenError,
	sql,
	UserInputError,
} from '../../utils';
import { cancelTicketInternationalBooking } from '../ConfirmTicketPurchase/ticketInternational';
import { forceSyncCineplexTickets } from '../User/cineplexCustomerService';
import * as cp from '../User/cineplexKrankikomCustomerService';

export interface PublicTicketId {
	type: 'PublicTicketId';
}

export interface InternalTicketId {
	type: 'InternalTicketId';
}

export class Ticket {
	id: PublicTicketId;
	_dbId: InternalTicketId;
	_userId: InternalUserId;
	_screeningId: InternalScreeningId;
	_cinemaId: InternalCinemaId;
	_movieId: InternalMovieId;
	auditoriumName?: string;
	_screeningDatetime?: Date;
	_movieTitle: string;
	seats: { rowName?: string; seatName?: string }[];
	qrCode?: string;
	boughtAt?: Date;
	refundedAt?: Date;
	vouchers?: { image: string; qrCode: string }[];
	onlineTicketingId?: string;
	refundable?: boolean;
	status: TicketStatus;
	pkpass?: string;
	googlePayPass?: string;
	isFlatrateTicket?: boolean;
	displayAllowedAfter?: Date;
	cancelationLink?: string;
	scanned?: boolean;
	bluetoothRequired?: boolean;

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	constructor(data: any) {
		Object.assign(this, data);
	}
	static encodeDbId(dbId: InternalTicketId): PublicTicketId {
		return (encodeDbId('Ticket', (dbId as unknown) as number) as unknown) as PublicTicketId;
	}
	static decodePublicId(id: PublicCinemaId): InternalTicketId {
		return (decodePublicId('Ticket', id as string) as unknown) as InternalTicketId;
	}
	static async gen(viewer: Viewer, id: PublicTicketId) {
		const ticket = (await Ticket.genMult(viewer, [id]))[0];
		if (!ticket) {
			return new EntityNotFoundError(`no ticket ${id}`);
		}
		return ticket;
	}
	static async genMult(viewer: Viewer, ids: PublicTicketId[]): Promise<Ticket[]> {
		const tickets = (
			await db.query(sql`
			SELECT 
				t.id as "_dbId",
				showtime_id as "_screeningId",
				user_id as "_userId",
				seats_info as "seats",
				attached_vouchers as "vouchers",
				qr_code as "qrCode",
				bought_at as "boughtAt",
				cp_booking_id as "onlineTicketingId",
				refundable,
				status,
				refunded_at as "refundedAt",
				movie_id as "_movieId",
				screening_datetime as "_screeningDatetime",
				auditorium_name as "auditoriumName",
				cinema_id as "_cinemaId",
				movie_title as "_movieTitle",
				google_pay_pass as "googlePayPass",
				is_subscription_ticket as "isFlatrateTicket",
				cf_booking_id as "cfBookingId",
				ticket_provider as "ticketProvider",
				reference_url as "referenceUrl",
				cancel_url as "cancelUrl",
				scanned
			FROM 
				priv.p_ticket t
			WHERE
				t.id = ANY(${ids.map(Ticket.decodePublicId)})
		`)
		).rows;

		const ticketDict = new Map(tickets.map((ticket) => [Ticket.encodeDbId(ticket._dbId), ticket]));
		return await Promise.all(
			ids
				.map((id) => ticketDict.get(id))
				.filter((id) => id)
				.map(async (row) => {
					if (
						((viewer.language === 'de-cineplex' || viewer.language === 'de-cinfinity') &&
							viewer.userIdDb !== row._userId) ||
						(viewer.language === 'de' && !viewer.isPrivileged)
					) {
						throw new ForbiddenError('Unauthorized');
					}
					const cinema = await Cinema.gen(viewer, Cinema.encodeDbId(row._cinemaId));
					const beacons = await cinema?.beacons();
					const ticket = new Ticket({
						_dbId: row._dbId,
						_screeningId: row._screeningId,
						_userId: row._userId,
						_movieId: row._movieId,
						_screeningDatetime: row._screeningDatetime,
						auditoriumName: row.auditoriumName,
						_cinemaId: row._cinemaId,
						_movieTitle: row._movieTitle,
						refundedAt: row.status === 'REFUNDED' ? row.refundedAt : null,
						...(viewer.language === 'de-cineplex' ||
						(viewer.language === 'de' && DEFAULT_APP_BRAND === 'CINEPLEX')
							? {
									seats:
										row.seats?.map(({ seatName, rowName }) => ({
											seatName: !seatName ? '-' : seatName,
											rowName: !rowName ? '-' : rowName,
										})) || [],
									// if a ticket has no pre assigned seats (=freie Platzwahl), seatName and rowName will be null, which is not-allowed by api
									vouchers: row.vouchers,
									qrCode: row.qrCode,
									boughtAt: row.boughtAt,
									onlineTicketingId: row.onlineTicketingId,
									id: Ticket.encodeDbId(row._dbId),
									refundable: Boolean(row.refundable),
									status: row.status,
									pkpass:
										CINEPLEX_BOOKING_SERVICE_URL +
										'/booking/pass.dpkpass?bookingProcessId=' +
										row.onlineTicketingId,
									googlePayPass: row.googlePayPass,
							  }
							: viewer.language === 'de-cinfinity' ||
							  (viewer.language === 'de' && DEFAULT_APP_BRAND === 'CINFINITY')
							? {
									seats:
										row.seats?.map(({ seatName, rowName }) => ({
											seatName: seatName || '?',
											rowName: rowName || '?',
										})) || [],
									qrCode: row.qrCode,
									boughtAt: row.boughtAt,
									id: Ticket.encodeDbId(row._dbId),
									refundable: Boolean(row.refundable),
									status: row.status,
									isFlatrateTicket: row.isFlatrateTicket,
									displayAllowedAfter: row._screeningDatetime
										? new Date(row._screeningDatetime.getTime() - 60 * 60 * 1000)
										: null,
									cancelationLink: row.refundable
										? row.ticketProvider === 'TICKET_INTERNATIONAL' // is cancellable via api
											? undefined
											: row.cancelUrl || row.referenceUrl // we either have a specific cancel url or just use confirmPage url
										: undefined,
									provider: row.ticketProvider,
									scanned: row.scanned,
									bluetoothRequired: row.ticketProvider === 'COMPESO' && Boolean(beacons?.length),
							  }
							: {}),
					});
					return ticket;
				})
		);
	}

	async user(_: unknown, { viewer }: { viewer: Viewer }) {
		return await User.gen(viewer, User.encodeDbId(this._userId));
	}

	async screening(_: unknown, { viewer }: { viewer: Viewer }) {
		const screening = this._screeningId
			? await Screening.gen(viewer, Screening.encodeDbId(this._screeningId))
			: undefined;
		if (screening) {
			return screening;
		} else {
			// The screening might not exist anymore because the cinema could have deleted it in the meantime
			// If the app in the future needs anything else then the datetime, this must be extended accordingly
			const screening = {
				id: `fakeScreeningTicket${this._dbId}`,
				movie: this._movieId
					? Movie.gen(null, Movie.encodeDbId(this._movieId))
					: {
							id: `fakeMovieTicket${this._dbId}`,
							title: this._movieTitle,
							poster: null, //TODO: Fallbacks for which properties needed?
							banner: null,
							ageRating: '',
							duration: 0,
							directedBy: '',
					  },
				datetime: this._screeningDatetime,
				auditoriumName: this.auditoriumName,
				cinema: Cinema.gen(viewer, Cinema.encodeDbId(this._cinemaId)),
			};
			console.log('screening', screening, this);
			return screening;
		}
	}

	static async cancelTicket(_: unknown, { id }: { id?: PublicTicketId }, context: Context) {
		const ticketIdDb = Ticket.decodePublicId(id);
		const ticket = await db.queryOne(sql`
			SELECT cp_booking_id, user_id FROM priv.p_ticket WHERE id = ${ticketIdDb}
		`);
		if (!ticket) {
			throw new UserInputError('BOOKING_NOT_FOUND');
		}
		if (!context.viewer.isPrivileged && ticket.user_id !== context.viewer.userIdDb) {
			throw new ForbiddenError('Unauthorized');
		}
		if (context.language === 'de-cineplex') {
			// CINEPLEX
			const { cp_access_token: cpAccessToken } = await db.queryOne(sql`
				SELECT cp_access_token FROM priv.p_user WHERE id=${context.viewer.userIdDb}
			`);
			await cp.cancelTicket({ cpAccessToken, bookingProcessId: ticket.cp_booking_id }, context);
			await forceSyncCineplexTickets(context.viewer.userIdDb);
		} else if (context.language === 'de-cinfinity' || context.language === 'de') {
			// CINFINITY
			const ticketInfo = await db.queryOne(sql`
				SELECT * FROM priv.p_ticket WHERE id=${ticketIdDb}
			`);
			if (!ticketInfo) {
				throw new UserInputError('TICKET_NOT_FOUND');
			}
			if (ticketInfo.ticket_provider === 'TICKET_INTERNATIONAL') {
				const { success, error } = await cancelTicketInternationalBooking({
					referenceId: ticketInfo.cf_booking_id,
					cinemaIdDb: ticketInfo.cinema_id,
				});
				if (!success) {
					throw new ApolloError('TICKET_INTERNATIONAL_CANCEL_TICKET_ERROR', error);
				} else {
					await db.query(sql`
						UPDATE priv.p_ticket SET status='REFUNDED', refunded_at = COALESCE(refunded_at,NOW()) WHERE id = ${ticketIdDb}
					`);
				}
			} else {
				throw new ForbiddenError('Not supported');
			}
		} else {
			await db.query(sql`
				UPDATE priv.p_ticket SET status='REFUNDED', refunded_at = COALESCE(refunded_at,NOW()) WHERE id = ${ticketIdDb}
			`);
		}
		return {
			canceledTicket: await Ticket.gen(context.viewer, id),
			currentUser: User.gen(context.viewer, context.viewer.userId),
		};
	}

	async sponsoredBy() {
		if (DEFAULT_APP_BRAND !== 'CINFINITY' || !this.isFlatrateTicket) {
			return null;
		}

		const company = await db.queryWithCache(sql`
				SELECT c.name,c.logo
				FROM priv.p_subscription s
				JOIN priv.p_company c
				ON s.company_id = c.id
				WHERE s.user_id = ${this._userId} 
					AND s.company_id IS NOT null 
					AND ((paypal_order_id IS NULL AND payed_until > NOW()) 
					OR s.paypal_status = 'ACTIVE' 
						OR (s.paypal_status = 'CANCELLED' 
							AND s.cancellation_effective_at > now()) )
			`);

		return company?.rows?.[0] || null;
	}

	static async updateTicket(
		_: unknown,
		{ id, scanned }: { id?: PublicTicketId; scanned },
		context: Context
	) {
		const viewer = context.viewer;
		if (DEFAULT_APP_BRAND !== 'CINFINITY' || !viewer.isPrivileged) {
			throw new ForbiddenError('Unauthorized');
		}
		const ticketIdDb = Ticket.decodePublicId(id);
		const ticket = await db.queryOne(sql`
			SELECT * FROM priv.p_ticket WHERE id = ${ticketIdDb}
		`);
		if (!ticket) {
			throw new UserInputError('TICKET_NOT_FOUND');
		}
		if (scanned === true || scanned === false) {
			await db.query(sql`
				UPDATE priv.p_ticket SET scanned=${scanned} WHERE id = ${ticketIdDb}
			`);
		}
		return await Ticket.gen(context.viewer, id);
	}

	// Temporary function, to be removed when all cinemas have beacons in place
	static async openTicket(_: unknown, { id }: { id?: PublicTicketId }, context: Context) {
		try {
			const viewer = context.viewer;
			const ticket = await Ticket.gen(viewer, id);

			if (
				DEFAULT_APP_BRAND !== 'CINFINITY' ||
				(!viewer.isPrivileged && ticket._userId !== viewer.userIdDb)
			) {
				throw new ForbiddenError('Unauthorized');
			}

			const now = new Date();
			const showTime = new Date(ticket._screeningDatetime);

			// Check if current time is within ±30 minutes of showTime
			const minutesDiff = differenceInMinutes(now, showTime);
			const isInWindow = Math.abs(minutesDiff) <= 30;

			if (!ticket.bluetoothRequired && isInWindow) {
				await db.query(sql`
					UPDATE priv.p_ticket SET scanned=true WHERE id = ${ticket._dbId}
				`);
			}
			return await Ticket.gen(context.viewer, id);
		} catch (err) {
			console.log(err);
			throw err;
		}
	}
}
