import { db, decodePublicId, encodeDbId, EntityNotFoundError, sql } from '../../utils';
import { CINURUFILE_BASE_URL } from '../../../consts';
import {
	InternalCastOrCrewMemberId,
	PublicCastOrCrewMemberId,
	Viewer,
} from '../../typescript-types';

export class CastOrCrewMember {
	id: string;
	image: string;
	fullName: string;
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	constructor(data: any) {
		Object.assign(this, data);
	}
	static encodeDbId(dbId: InternalCastOrCrewMemberId) {
		return encodeDbId('CastOrCrewMember', dbId as number) as PublicCastOrCrewMemberId;
	}
	static decodePublicId(id: PublicCastOrCrewMemberId) {
		return decodePublicId('CastOrCrewMember', id as string) as InternalCastOrCrewMemberId;
	}
	static async gen(viewer: Viewer, id: PublicCastOrCrewMemberId) {
		const dbId = this.decodePublicId(id);
		const res = await db.queryWithCache(sql`
		SELECT 
			id,
			full_name as "fullName", 
			image 
		FROM 
			priv.p_cast_or_crew_member 
		WHERE 
			id = ${dbId}
		`);
		if (res.rows.length === 0) {
			throw new EntityNotFoundError('No cast or Crew member with this id');
		}
		const d = res.rows[0];
		return new CastOrCrewMember(
			Object.assign(d, {
				id,
				image: d.image ? `${CINURUFILE_BASE_URL}${d.image}` : '',
			})
		);
	}
	static async genMult(ids?: PublicCastOrCrewMemberId[]): Promise<CastOrCrewMember[]> {
		//if ids not passed in it means that genMult is returning all CastOrCrewMembers
		const dbIds = ids
			?.filter((el, idx) => ids.indexOf(el) === idx)
			.map((id) => this.decodePublicId(id));
		let res;
		if (dbIds) {
			res = await db.queryWithCache(sql`
				SELECT 
					id,
					full_name as "fullName", 
					image 
				FROM 
					priv.p_cast_or_crew_member 
				WHERE 
					id = ANY(${dbIds})
			`);
			if (res.rows.length !== dbIds.length) {
				throw new EntityNotFoundError('No cast or Crew member with this id');
			}
		} else if (!dbIds || !dbIds?.length) {
			res = await db.queryWithCache(sql`
				SELECT 
					id,
					full_name as "fullName", 
					image 
				FROM 
					priv.p_cast_or_crew_member 
			`);
			//this should never happen
			if (res.rows.length === 0) {
				throw new EntityNotFoundError(
					'No cast or Crew member could be found. Something went terribly wrong'
				);
			}
		}

		return res.rows.map(
			(r) =>
				new CastOrCrewMember(
					Object.assign(r, {
						id: this.encodeDbId(r.id),
						image: r.image ? `${CINURUFILE_BASE_URL}${r.image}` : '',
					})
				)
		);
	}
}
