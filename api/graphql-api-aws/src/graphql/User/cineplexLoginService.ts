import fetch from 'node-fetch';
import AbortController from 'abort-controller';

import jwt from 'jsonwebtoken';
import uuid from 'uuid/v4';
import { CineplexServiceError, db, sendErrorMail, sql, printError, sendEmail } from '../../utils';
import {
	CINEPLEX_LOGIN_CLIENT_ID,
	CINEPLEX_LOGIN_PUBLIC_KEY,
	CINEPLEX_LOGIN_SERVICE_URL,
} from '../../../consts';
import { Context, InternalUserId } from '../../typescript-types';

type kkRequestLogObject = {
	initAt: number;
	requestPath: string;
	requestId: string;
	finishedAt?: number;
	error?: string;
	method?: string;
	params?: { [keys: string]: unknown };
};
const storeLogKK = async (log: kkRequestLogObject) => {
	const logObject = { ...log, timeElapsed: log.finishedAt - log.initAt };

	console.log('CP_CUSTOMER_SERVICE_REQUEST', logObject);
};

// docs: https://booking-uat.cineplex.de/TicketBoxXG3/docs/index.html
export const request = async ({
	baseUrl = CINEPLEX_LOGIN_SERVICE_URL,
	path,
	method,
	context,
	params,
	urlencoded,
	accessToken,
	apiKey,
	timeout,
}: {
	baseUrl?: string;
	path: string;
	method: 'POST' | 'PATCH' | 'GET' | 'DELETE';
	params?: { [param: string]: unknown };
	context?: { requestId?: string; clientVersion?: string; viewer?: { ipAddress?: string } };
	urlencoded?: boolean;
	accessToken?: string;
	apiKey?: string;
	timeout?: number;
}) => {
	const requestId = context && context.requestId ? context.requestId : uuid();
	const logging: kkRequestLogObject = {
		initAt: Date.now(),
		method,
		requestPath: path,
		params: path === '/customer' ? params : undefined, //We normally do not want to log params, but to debug Validation failed "BE-3" errors, we ned this for now
		requestId,
	};

	const controller = new AbortController();
	const timeoutF = setTimeout(() => {
		controller.abort();
	}, timeout || 20000);
	let status, content, success, response;
	try {
		response = await fetch(baseUrl + path, {
			method,
			headers: {
				'Accept': 'application/json',
				'Content-Type': `application/${urlencoded ? 'x-www-form-urlencoded' : 'json'}`,
				'RequestId': requestId,
				'Client-Type': context ? 'APP' : 'APP_SERVER',
				'Client-Version': context && context.clientVersion,
				'Original-Remote-Host': context && context.viewer.ipAddress,
				...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
				...(apiKey ? { 'X-AUTH-RESOURCE-ID': apiKey } : {}),
			},
			body:
				params && urlencoded
					? Object.entries(params)
							.map(
								([key, value]) => `${key}=${encodeURIComponent(value as string | number | boolean)}`
							)
							.join('&')
					: JSON.stringify(params),
			//@ts-ignore
			signal: controller.signal,
		});
		success = response.ok;
		status = response.status;
		content = await response.text();
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (e) {
		// 	await sendErrorMail(
		// 		'KK Timeout-Error',
		// 		`Time spent: ${Date.now() - logging.initAt}, Aborted at: ${Date.now()}, Started at:
		//  ${logging.initAt}
		//  Path: ${baseUrl + path}
		//  Method: ${method}.
		//  Headers: ${JSON.stringify(
		// 		{
		// 			'Accept': 'application/json',
		// 			'Content-Type': `application/${urlencoded ? 'x-www-form-urlencoded' : 'json'}`,
		// 			'RequestId': requestId,
		// 			'Client-Type': context ? 'APP' : 'APP_SERVER',
		// 			'Client-Version': context && context.clientVersion,
		// 			'Original-Remote-Host': context && context.viewer.ipAddress,
		// 		},
		// 		null,
		// 		2
		// 	)}Here is the error: ${printError(e)}${e}`
		// 	);

		status = 503;
	} finally {
		clearTimeout(timeoutF);
	}
	if (status === 503 || status === 501) {
		success = false;
		content = JSON.stringify({
			errorMessage: 'Krankikom: No Backend found!',
			errorUserMessage: 'Der Service ist nicht erreichbar. Bitte versuchen Sie es später erneut.',
			errorCode: 'KK-1',
			errorAction: 'retry-last',
			status,
			requestId,
		});
	}

	let data;
	try {
		data = content.length > 0 ? JSON.parse(content) : null;
	} catch (e) {
		let responseHeaders = '';
		for (let pair of response.headers.entries()) {
			responseHeaders += pair[0] + ': ' + pair[1];
		}

		sendErrorMail(
			'Could not parse cineplex login service response',
			`${method} ${path} got ${status} unparsable response ${content} for ${requestId}, responseHeaders: ${responseHeaders}`
		);
		logging.error = `${method} ${path} got unparsable response ${content} for ${requestId}. responseHeaders: ${responseHeaders}`;
		logging.finishedAt = Date.now();
		await storeLogKK(logging);
		throw new CineplexServiceError({
			requestId,
			status: 503,
			errorCode: 'KK-2',
			errorAction: 'retry-last',
			errorUserMessage: 'Der Service ist nicht erreichbar. Bitte versuchen Sie es später erneut.',
			errorMessage: `${method} ${path} got unparsable response for ${requestId}`,
		});
	}

	// console.log(method, baseUrl + path, JSON.stringify(params, null, 2), response.status, data);
	if (success) {
		logging.finishedAt = Date.now();
		await storeLogKK(logging);
		return { success, status, data, requestId };
	} else {
		const error = data || {};
		error.errorCode = error.errorCode?.replace('COMPESO', 'CP');
		error.status = status;
		logging.error = JSON.stringify(error);
		logging.finishedAt = Date.now();
		await storeLogKK(logging);
		if (status === 503 || status === 501) {
			throw new CineplexServiceError(error);
		}
		return { success, status, error, requestId };
	}
};

export type ErrorType = 'Input' | 'Forbidden' | 'Blocked' | 'Network';
type Response<D> = Promise<
	| {
			data?: undefined;
			errorType: ErrorType;
			errorMessage: string;
			errorProperties?: { [key: string]: unknown };
	  }
	| {
			data: D;
			errorType?: undefined;
			errorMessage?: undefined;
			errorProperties?: undefined;
	  }
>;
type CineplexJWT = {
	aud: string[];
	user_name: string;
	externalId: string;
	exp: number;
	jti: string;
	client_id: string;
};
const verifyCineplexToken = (token: string) => {
	jwt.verify(token, CINEPLEX_LOGIN_PUBLIC_KEY) as CineplexJWT;
};

export const register = async (
	{
		email,
		password,
		firstName,
		lastName,
	}: { email: string; password: string; firstName: string; lastName: string },
	context: Context
): Response<true> => {
	const { error } = await request({
		method: 'POST',
		path: '/users/custom/registration',
		params: {
			clientId: CINEPLEX_LOGIN_CLIENT_ID,
			onlyCheck: false,
			customer: {
				emailAddress: email,
				passwordPlain: password,
				firstName,
				lastName,
				consents: {
					TERMS_AND_CONDITIONS: true,
					DATA_USAGE_DECLARATION: true,
					AGE_16: true,
				},
			},
		},
		context,
	});
	if (error) {
		if (error.errorCode === 'AUTS-5') {
			return { errorType: 'Input', errorMessage: 'DUPLICATE_EMAIL' };
		}
		if (error.errorCode === 'AUTS-4') {
			return { errorType: 'Blocked', errorMessage: 'USER_EMAIL_UNCONFIRMED' };
		}
		if (error.errorCode === 'BE-3') {
			const errorFields = error.errorValidationList.map(({ field }) => field);
			let errorMessage: string;
			if (errorFields.includes('customer.emailAddress') || errorFields.includes('emailAddress')) {
				errorMessage = 'EMAIL_INVALID';
			} else if (
				errorFields.includes('customer.passwordPlain') ||
				errorFields.includes('passwordPlain')
			) {
				errorMessage = 'PASSWORD_INVALID';
			} else if (errorFields.includes('customer.lastName') || errorFields.includes('lastName')) {
				errorMessage = 'LAST_NAME_INVALID';
			} else if (errorFields.includes('customer.firstName') || errorFields.includes('firstName')) {
				errorMessage = 'FIRST_NAME_INVALID';
			} else {
				//-> can not be tested for now
				throw new CineplexServiceError(error);
			}
			//-> can not be tested for now
			return { errorType: 'Input', errorMessage };
		}
		//-> can not be tested for now
		throw new CineplexServiceError(error);
	}
	return { data: true };
};

export const verifyEmail = async (
	{ email, code, isChangeRequest }: { email?: string; code: string; isChangeRequest?: boolean },
	context: Context
): Response<{
	cpAccessToken: string;
	cpRefreshToken: string;
	cpWebsiteToken: string;
	cpId: string;
}> => {
	const { data, error } = await request({
		method: 'PATCH',
		path: `/users/custom/${isChangeRequest ? 'changeEmailAddress' : 'registration'}`,
		params: { emailAddress: email, code, clientId: CINEPLEX_LOGIN_CLIENT_ID },
		context,
	});
	if (error) {
		if (
			['AUTS-3', 'AUTS-6', 'AUTS-10', 'AUTS-30', 'AUTS-31', 'AUTS-32'].includes(error.errorCode)
		) {
			return { errorType: 'Input', errorMessage: 'WRONG_CODE' };
		}
		if (error.errorCode === 'AUTS-5' || error.errorCode === 'AUTS-9') {
			return { errorType: 'Input', errorMessage: 'EMAIL_ALREADY_CONFIRMED' };
		}
		if (error.errorCode === 'AUTS-12') {
			return { errorType: 'Input', errorMessage: 'WRONG_EMAIL_OR_ID' };
		}
		//-> can not be tested for now
		throw new CineplexServiceError(error);
	}
	verifyCineplexToken(data.access_token);
	verifyCineplexToken(data.refresh_token);
	return {
		data: {
			cpAccessToken: data.access_token,
			cpRefreshToken: data.refresh_token,
			cpWebsiteToken: JSON.stringify(data),
			cpId: data.externalId,
		},
	};
};

export const testing_getConfirmationCode = async (
	{ email, type }: { email: string; type: 'LOGIN_CREATION' | 'EMAIL_CHANGE' | 'PASSWORD_RESET' },
	context: Context
): Response<string> => {
	const { data, error } = await request({
		method: 'GET',
		path:
			{
				LOGIN_CREATION: '/userRegistrations/search/findByMandatorIdAndEmailAddress',
				EMAIL_CHANGE: '/userChangeEmailAddresses/search/findByMandatorIdAndEmailAddress',
				PASSWORD_RESET: '/userPasswordResets/search/findByMandatorIdAndEmailAddress',
			}[type] +
			`?emailAddress=${encodeURIComponent(email)}&mandatorId=746241be-8a37-44d8-8690-8ad67e674a2b`,
		context,
		apiKey: '4eb66bd3-c041-4bb3-bd54-ca6bf1d144bb',
	});

	if (error) {
		console.log('KK-Returned-Data', data, error);
		throw new CineplexServiceError(error);
	}
	return data.code;
};

export const updatePassword = async (
	{ email, code, password }: { email?: string; code: string; password?: string },
	context: Context
): Response<{
	cpAccessToken: string;
	cpRefreshToken: string;
	cpWebsiteToken: string;
	cpId: string;
}> => {
	const { data, error } = await request({
		method: 'PATCH',
		path: '/users/custom/passwordReset',
		params: {
			clientId: CINEPLEX_LOGIN_CLIENT_ID,
			emailAddress: email,
			code,
			passwordPlain: password,
		},
		context,
	});
	if (error) {
		if (['AUTS-6', 'AUTS-30', 'AUTS-31', 'AUTS-32'].includes(error.errorCode)) {
			return { errorType: 'Input', errorMessage: 'WRONG_CODE' };
		}
		if (error.errorCode === 'BE-3') {
			return { errorType: 'Input', errorMessage: 'PASSWORD_INVALID' };
		}
		//-> can not be tested for now
		throw new CineplexServiceError(error);
	}
	verifyCineplexToken(data.access_token);
	verifyCineplexToken(data.refresh_token);
	return {
		data: {
			cpAccessToken: data.access_token,
			cpRefreshToken: data.refresh_token,
			cpWebsiteToken: JSON.stringify(data),
			cpId: data.externalId,
		},
	};
};

export const login = async (
	{ email, password }: { email?: string; password: string },
	context: Context
): Response<{
	cpAccessToken: string;
	cpRefreshToken: string;
	cpWebsiteToken: string;
	cpId: string;
}> => {
	if (!email) return { errorType: 'Input', errorMessage: 'NEITHER_EMAIL_NOR_ID_PROVIDED' };
	const { data, error } = await request({
		method: 'POST',
		path: '/oauth/token',
		params: {
			client_id: CINEPLEX_LOGIN_CLIENT_ID,
			grant_type: 'password',
			username: email,
			password,
		},
		urlencoded: true,
		context,
	});
	if (error) {
		if (error.status === 503) {
			return { ...error, errorType: 'Network' };
		}
		const { errorType, errorMessage } = await resendActivationEmail({ email }, context);
		if (!errorMessage) {
			return { errorType: 'Blocked', errorMessage: 'USER_EMAIL_UNCONFIRMED' };
		}
		if (errorMessage === 'EMAIL_ALREADY_CONFIRMED') {
			return { errorType: 'Input', errorMessage: 'WRONG_PASSWORD' };
		}
		return { errorType, errorMessage };
	}
	verifyCineplexToken(data.access_token);
	verifyCineplexToken(data.refresh_token);
	return {
		data: {
			cpAccessToken: data.access_token,
			cpRefreshToken: data.refresh_token,
			cpWebsiteToken: JSON.stringify(data),
			cpId: data.externalId,
		},
	};
};

//-> can not be tested for now
export const socialLogin = async (
	{
		provider,
		token,
		email,
		firstName,
		lastName,
		socialLoginSession,
	}: {
		provider: 'APPLE' | 'GOOGLE' | 'FACEBOOK';
		token: string;
		email?: string;
		firstName?: string;
		lastName?: string;
		socialLoginSession?: string;
	},
	context: Context
): Response<{
	cpAccessToken: string;
	cpRefreshToken: string;
	cpWebsiteToken: string;
	cpId: string;
	socialAuthId: string;
}> => {
	let cpAccessToken: string;
	let cpRefreshToken: string;
	let cpWebsiteToken: string;
	let cpId: string;
	let socialAuthId: string;
	// if a social login process id is passed, register the user, otherwise try to log them in first
	if (!socialLoginSession) {
		const { data, error } = await request({
			method: 'GET',
			path: `/login/oauth2/token/746241be-8a37-44d8-8690-8ad67e674a2b_${provider.toLowerCase()}?token=${token}`,
			context,
		});
		if (error) {
			return { errorType: 'Input', errorMessage: 'SOCIAL_AUTH_FAILED' };
		}
		socialLoginSession = data.userExternalLoginProcessId;
		cpAccessToken = data.access_token;
		cpRefreshToken = data.refresh_token;
		cpWebsiteToken = JSON.stringify(data);
		cpId = data.externalId;
		socialAuthId = data.externalLoginId;
	}
	// this is not an else case, social login session might have been set in prevouous if block
	if (socialLoginSession) {
		// if an external process id was returned, additional information is needed to complete registration
		if (!context.viewer.hasUserId) {
			// registration in only allowed if an anoymous user already extist
			return { errorType: 'Input', errorMessage: 'USER_DOES_NOT_EXIST' };
		}
		if (email && firstName && lastName) {
			// if all additional data is provided, register user with socialLoginSession
			const { error, data, requestId } = await request({
				method: 'POST',
				path: '/users/custom/registration',
				params: {
					clientId: CINEPLEX_LOGIN_CLIENT_ID,
					onlyCheck: false,
					userExternalLoginProcessId: socialLoginSession,
					customer: {
						emailAddress: email,
						firstName,
						lastName,
						consents: {
							TERMS_AND_CONDITIONS: true,
							DATA_USAGE_DECLARATION: true,
							AGE_16: true,
						},
					},
				},
				context,
			});
			if (error) {
				if (error.errorCode === 'AUTS-5') {
					return { errorType: 'Input', errorMessage: 'DUPLICATE_EMAIL' };
				}
				if (error.errorCode === 'AUTS-4') {
					return { errorType: 'Blocked', errorMessage: 'USER_EMAIL_UNCONFIRMED' };
				}
				if (error.errorCode === 'BE-3') {
					const errorFields = error.errorValidationList.map(({ field }) => field);
					let errorMessage: string;
					if (
						errorFields.includes('customer.emailAddress') ||
						errorFields.includes('emailAddress')
					) {
						errorMessage = 'EMAIL_INVALID';
					} else if (
						errorFields.includes('customer.passwordPlain') ||
						errorFields.includes('passwordPlain')
					) {
						errorMessage = 'PASSWORD_INVALID';
					} else if (
						errorFields.includes('customer.lastName') ||
						errorFields.includes('lastName')
					) {
						errorMessage = 'LAST_NAME_INVALID';
					} else if (
						errorFields.includes('customer.firstName') ||
						errorFields.includes('firstName')
					) {
						errorMessage = 'FIRST_NAME_INVALID';
					} else {
						throw new Error(`unexpected error ${JSON.stringify(error)} for request ${requestId}`);
					}
					return { errorType: 'Input', errorMessage };
				}
				throw new Error(`unexpected error ${JSON.stringify(error)} for request ${requestId}`);
			}
			if (data && data.access_token) {
				verifyCineplexToken(data.access_token);
				verifyCineplexToken(data.refresh_token);
				return {
					data: {
						cpAccessToken: data.access_token,
						cpRefreshToken: data.refresh_token,
						cpWebsiteToken: JSON.stringify(data),
						cpId: data.externalId,
						socialAuthId: data.externalLoginId,
					},
				};
			} else {
				return { errorType: 'Blocked', errorMessage: 'USER_EMAIL_UNCONFIRMED' };
			}
		} else {
			// return error, frontend will request missing data and call this endpoint again with complete data
			return {
				errorType: 'Input',
				errorMessage: 'INCOMPLETE_USER_PROFILE',
				errorProperties: { socialLoginSession },
			};
		}
	}
	verifyCineplexToken(cpAccessToken);
	verifyCineplexToken(cpRefreshToken);
	return { data: { cpAccessToken, cpRefreshToken, cpWebsiteToken, cpId, socialAuthId } };
};

export const refresh = async (
	{ cpRefreshToken }: { cpRefreshToken: string },
	context?: {
		requestId?: string;
		clientVersion?: string;
		clientSession?: string;
		viewer?: { ipAddress?: string; userIdDb: InternalUserId };
	}
): Response<{
	cpAccessToken: string;
	cpRefreshToken: string;
	cpWebsiteToken: string;
	cpId: string;
}> => {
	const { error, data } = await request({
		method: 'POST',
		path: '/oauth/token',
		params: {
			client_id: CINEPLEX_LOGIN_CLIENT_ID,
			grant_type: 'refresh_token',
			refresh_token: cpRefreshToken,
		},
		context,
		urlencoded: true,
	});
	if (error) {
		let errorMessage = 'OUTDATED_REFRESH_TOKEN';
		console.log('Error in refresh Token', error, JSON.stringify(error));
		if (!error.error_description || !error.error_description.includes('expired')) {
			errorMessage = 'INVALID_REFRESH_TOKEN';
			if (
				!error ||
				!error.error_description ||
				!error.error_description.includes("Can't find user by name")
			) {
				// The "Can't find user by name" error is KK's fault and will disapear at some point. Do not write error mails for it
				sendErrorMail(
					'Invalid Refresh Token',
					`Here is the error: ${printError(error)}, ${error.error_description}. RequestId: ${
						context?.requestId
					}
					Session: ${context?.clientSession} cinuruUserId:${context?.viewer?.userIdDb}
				
					Token: ${cpRefreshToken},
					`
				);
			}
		}
		return {
			errorType: 'Input',
			errorMessage,
		};
	}
	return {
		data: {
			cpAccessToken: data.access_token,
			cpRefreshToken: data.refresh_token,
			cpWebsiteToken: JSON.stringify(data),
			cpId: data.externalId,
		},
	};
};

export const resendActivationEmail = async (
	{ email }: { email: string },
	context: Context
): Response<boolean> => {
	const { error } = await request({
		method: 'POST',
		path: '/users/custom/registration',
		params: {
			onlyCheck: true,
			clientId: CINEPLEX_LOGIN_CLIENT_ID,
			customer: { emailAddress: email },
		},
		context,
	});
	if (!error) {
		return { errorType: 'Input', errorMessage: 'WRONG_EMAIL_OR_ID' };
	} else if (error.errorCode === 'AUTS-5') {
		return { errorType: 'Input', errorMessage: 'EMAIL_ALREADY_CONFIRMED' };
	} else if (error.errorCode === 'AUTS-4') {
		return { data: true };
	} else {
		//-> can not be tested for now
		throw new CineplexServiceError(error);
	}
};

export const resendChangeEmailConfirmationEmail = async (
	{ email, cpAccessToken }: { email: string; cpAccessToken: string },
	context: Context
): Response<boolean> => {
	const { success, error } = await request({
		method: 'POST',
		path: '/users/custom/changeEmailAddress',
		params: { clientId: CINEPLEX_LOGIN_CLIENT_ID, newEmailAddress: email, onlyCheck: false },
		accessToken: cpAccessToken,
		context,
	});
	if (success || error.errorCode === 'AUTS-9') {
		// email change request was sent or already existed and was resent
		return { data: true };
	}
	if (error.errorCode === 'AUTS-24' || error.errorCode === 'BE-3') {
		return { errorType: 'Input', errorMessage: 'WRONG_EMAIL_OR_ID' };
	}
	if (error.errorCode === 'BE-5') {
		return { errorType: 'Forbidden', errorMessage: 'MISSING_CINEPLEX_RIGHTS' };
	}
	throw new CineplexServiceError(error);
};

export const resetPassword = async (
	{ email }: { email: string },
	context: Context
): Response<boolean> => {
	const { success, error } = await request({
		method: 'POST',
		path: '/users/custom/passwordReset',
		params: { clientId: CINEPLEX_LOGIN_CLIENT_ID, emailAddress: email },
		context,
	});
	if (success || error.errorCode === 'AUTS-8') {
		// password change request already exists and email was resent, no need to error
		return { data: true };
	} else if (error.errorCode === 'BE-3' || error.errorCode === 'AUTS-12') {
		return { errorType: 'Input', errorMessage: 'WRONG_EMAIL_OR_ID' };
	} else {
		//-> can not be tested for now
		throw new CineplexServiceError(error);
	}
};

export const changeEmail = async (
	{ email, cpAccessToken }: { email: string; cpAccessToken: string },
	context: Context
): Response<true> => {
	const { success, error } = await request({
		method: 'POST',
		path: '/users/custom/changeEmailAddress',
		params: {
			clientId: CINEPLEX_LOGIN_CLIENT_ID,
			newEmailAddress: email,
			onlyCheck: false,
		},
		accessToken: cpAccessToken,
		context,
	});
	if (success) {
		return { data: true };
	}
	if (error.errorCode === 'AUTS-9') {
		// email change request already existed and was resent
		return { data: true };
	}
	if (error.errorCode === 'AUTS-24') {
		return { errorType: 'Input', errorMessage: 'DUPLICATE_EMAIL' };
	}
	if (error.errorCode === 'BE-3') {
		return { errorType: 'Input', errorMessage: 'UNREACHABLE_EMAIL_ADDRESS' };
	}
	//-> can not be tested for now
	if (error.errorCode === 'BE-5') {
		return { errorType: 'Forbidden', errorMessage: 'MISSING_CINEPLEX_RIGHTS' };
	}
	//-> can not be tested for now
	throw new CineplexServiceError(error);
};
