import { OAuth2Client as Google } from 'google-auth-library';
import { Facebook } from 'fb';
import { padStart, random } from 'lodash';
import jwt from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';
import CSRFTokens from 'csrf';
import { decode as atob } from 'base-64';

import cryptoRandomString from 'crypto-random-string';
import { renderEmailTemplate } from '@cinuru/emails';
import { Cinema, BonusProgram, User } from '..';
import {
	db,
	sql,
	sendErrorMail,
	createJWT,
	UserInputError,
	ForbiddenError,
	RateLimitError,
	SocialAuthError,
	UserBlockedError,
	ExpectedError,
	InternalServerError,
	encodeDbId,
	isAnonymousUser,
	sendEmail,
} from '../../utils';
import { createFileServerJWT, createViewerFromJWT } from '../../utils/authHelpers';
import {
	Context,
	InternalUserId,
	Language,
	InternalCinemaId,
	InternalBonusProgramId,
	PublicBonusProgramId,
	Gender,
} from '../../typescript-types';
import * as cp from './cineplexLoginService';
import { cineplexCustomerAuthChanged } from './cineplexCustomerService';
import {
	GOOGLE_WEB_CLIENT_ID_BY_LANGUAGE,
	APPLE_SIGNIN_CLIENT_IDS,
	RATE_LIMIT,
	ENABLE_MOCK_APIS,
} from '../../../consts';
import { combineEmailRecipientAndAppUser } from '../../utils/combineEmailRecipientAndAppUser';
import { getUserName } from '../../utils/nameHelpers';

export const createAnonymousUser = async (
	_: unknown,
	{ appId, bonusProgramId }: { appId?: string; bonusProgramId?: PublicBonusProgramId },
	context: Context
) => {
	const { viewer, language, brand } = context;
	await checkRateLimit(viewer.ipAddress);
	const { id: userDbId } = await db.queryOne(sql`
		INSERT INTO priv.p_user (registered_with_language, registered_datetime) 
		VALUES (${language}, now()) 
		RETURNING id;
	`);
	// update the user to set the qr_code, which is based on the id
	await db.query(sql`
		UPDATE priv.p_user 
		SET qr_code=(id::numeric*10000+(floor(random()*(9999-1000+1))+1000))*10+1 
		WHERE id=${userDbId}
	`);
	// if the user is created for a brand, the corresponding bonusProgramId will be passed and a cinema will be auto selected in case there only is one
	if (bonusProgramId && brand !== 'CINEPLEX') {
		const bonusProgram = await BonusProgram.gen(viewer, bonusProgramId);
		const cinemas = await bonusProgram.participatingCinemas(null, { viewer, language });
		if (cinemas.length === 1) {
			// we cannot use selectCinema since that requires authentication
			await db.query(sql`
				INSERT INTO priv.p_user_cinema(user_id, cinema_id, selected_datetime) 
				VALUES (${userDbId}, ${Cinema.decodePublicId(cinemas[0].id)}, now()) 
				ON CONFLICT DO NOTHING;
			`);
		}
	}
	// login new anonymous user
	return await executeLogin(userDbId, { appId, anonymous: true }, context);
};

export const requestLoginCreation = async (
	_: unknown,
	{
		email,
		password,
		name,
		firstName,
		lastName,
		gender,
		birthDate,
		telephone,
		street,
		houseNumber,
		zipCode,
		city,
		country,
	}: {
		email: string;
		password: string;
		name?: string;
		firstName?: string;
		lastName?: string;
		gender?: Gender;
		telephone?: string;
		birthDate?: string;
		street?: string;
		houseNumber?: string;
		zipCode?: string;
		city?: string;
		country?: string;
	},
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);
	email = email.toLowerCase();
	const { viewer, i18n, language, brand, appLink } = context;
	if (!viewer.hasUserId || !viewer.userId) {
		// user hasn't been created yet, abort
		return new UserInputError('USER_DOES_NOT_EXIST');
	}
	const userBlockedReason = await viewer.isBlocked();
	if (userBlockedReason && userBlockedReason !== 'MISSING_EMAIL_VERIFICATION') {
		// cannot add login to blocked user
		return await makeAndRecordLoginError({
			ipAddress: viewer.ipAddress,
			email,
			errorType: 'Input',
			errorMessage: 'USER_INVALID_OR_BLOCKED',
			errorProperties: { userBlockedReason },
		});
	}
	if (!(await viewer.isAnonymous())) {
		// not an anonymou user, abort
		return await makeAndRecordLoginError({
			ipAddress: viewer.ipAddress,
			email,
			errorType: 'Input',
			errorMessage: 'USER_ALREADY_REGISTERED',
		});
	}

	if (brand === 'CINEPLEX') {
		if (!firstName) {
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				email,
				errorType: 'Input',
				errorMessage: 'FIRST_NAME_MISSING',
			});
		}
		if (!lastName) {
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				email,
				errorType: 'Input',
				errorMessage: 'LAST_NAME_MISSING',
			});
		}
		const { data: success, errorType, errorMessage, errorProperties } = await cp.register(
			{ email, password, firstName, lastName },
			context
		);
		if (!success) {
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				email,
				errorType,
				errorMessage,
				errorProperties,
			});
		}
		// ensure orphaned users won't lead to issues
		await db.query(sql`
			DELETE FROM priv.p_user WHERE email=${(
				email || ''
			).toLowerCase()} AND registered_with_language='de-cineplex';
		`);
		// user doesn't exist yet and confirmation email has been sent, upgrade to registered user
		const user = await db.queryOne(sql`
			UPDATE priv.p_user SET 
				active=false,
				user_blocked_reason='MISSING_EMAIL_VERIFICATION',
				email=${(email || '').toLowerCase()},
				registered_with_language=${language},
				email_confirmed=false,
				first_name=${firstName},
				last_name=${lastName},
				username=${firstName + ' ' + lastName},
				telephone=${telephone}
			WHERE id=${viewer.userIdDb} 
			RETURNING id
		`);
		if (user?.id !== viewer.userIdDb) {
			throw new Error(`something went terribly wrong`);
		}
	} else {
		if (brand === 'CINFINITY') {
			const errorCode = !firstName
				? 'FIRST_NAME_MISSING'
				: !lastName
				? 'LAST_NAME_MISSING'
				: !gender
				? 'GENDER_MISSING'
				: !birthDate
				? 'BIRTH_DATE_MISSING'
				: !street
				? 'STREET_MISSING'
				: !zipCode
				? 'ZIP_CODE_MISSING'
				: !city
				? 'CITY_MISSING'
				: !country
				? 'COUNTRY_MISSING'
				: // : !telephone
				  // ? 'PHONE_NUMBER_MISSING'
				  null;

			if (errorCode) {
				return await makeAndRecordLoginError({
					ipAddress: viewer.ipAddress,
					email,
					errorType: 'Input',
					errorMessage: errorCode,
				});
			}
		} else {
			if (!name) {
				return await makeAndRecordLoginError({
					ipAddress: viewer.ipAddress,
					email,
					errorType: 'Input',
					errorMessage: 'NAME_MISSING',
				});
			}
		}

		if (password.length < 8) {
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				email,
				errorType: 'Input',
				errorMessage: 'PASSWORD_INVALID',
			});
		}
		const appUserWithSameEmail = await db.queryOne(sql`
			SELECT id FROM priv.p_user
			WHERE email=${(
				email || ''
			).toLowerCase()} AND registered_with_language=${language} AND is_app_user=true;
		`);
		if (appUserWithSameEmail) {
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				email,
				errorType: 'Input',
				errorMessage: 'DUPLICATE_EMAIL',
			});
		}
		const newsLetterUserWithSameEmail = await db.queryOne(sql`
			SELECT id FROM priv.p_user
			WHERE email=${(
				email || ''
			).toLowerCase()} AND registered_with_language=${language} AND is_app_user=false;
		`);

		if (newsLetterUserWithSameEmail?.id) {
			await combineEmailRecipientAndAppUser(viewer.userIdDb, newsLetterUserWithSameEmail.id);
		}

		const code = padStart(String(random(1, 999999)), 6, '0');
		const href = `${appLink!}://profile/create?confirmationCode=${code}`;
		const { success } = await sendEmail({
			to: email,
			from: `"${i18n.t('Global.appName')}" <${i18n.t('Global.transactionalOutboundMail')}>`,
			...renderEmailTemplate({
				template: 'ConfirmRegistration',
				name: getUserName({
					firstName,
					lastName,
					userName: name,
					brand,
				}),
				code,
				href,
				brand,
			}),
		});
		if (!success) {
			// couldn't sent email confirmation, abort
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				email,
				errorType: 'Input',
				errorMessage: 'UNREACHABLE_EMAIL_ADDRESS',
			});
		}
		// user doesn't exist yet and confirmation email has been sent, upgrade to registered user
		const user = await db.queryOne(sql`
			UPDATE priv.p_user SET 
				active=false,
				user_blocked_reason='MISSING_EMAIL_VERIFICATION',
				email=${(email || '').toLowerCase()},
				registered_with_language=${language},
				email_confirmed=false,
				email_reminder_sent=true,
				welcome_email_sent=true,
				confirmation_code=${'EMAIL:' + code},
				email_changed_datetime=now(),
				password_hash=crypt(${password}, gen_salt('bf')),
				username=${name},
				first_name=${firstName},
				last_name=${lastName},
				gender=${gender},
				birth_date=${birthDate},
				street=${street},
				house_number=${houseNumber},
				zip_code=${zipCode},
				city=${city},
				country=${country},
				telephone=${telephone}
			WHERE id=${viewer.userIdDb} 
			RETURNING id
		`);
		if (user?.id !== viewer.userIdDb) {
			throw new Error(`something went terribly wrong`);
		}
	}
	return {
		user: User.gen(viewer, viewer.userId),
	};
};

const getUserByEmail = async (
	email: string,
	language: Language,
	password?: string
): Promise<null | {
	id: InternalUserId;
	language: Language;
	confirmationCode: null | string;
	correctPassword: boolean;
	name?: string;
	firstName?: string;
	lastName?: string;
	testingStatus?: string;
}> => {
	const res = await db.query(sql`
		SELECT
			id,
			username as "name",
			first_name as "firstName",
			last_name as "lastName",
			confirmation_code as "confirmationCode", 
			registered_with_language as "language",
			password_hash=crypt(${password}, password_hash) as "correctPassword",
			testing_status as "testingStatus"
		FROM priv.p_user 
		WHERE 
			email=${(email || '').toLowerCase()} AND 
			(registered_with_language=${language} 
			OR registered_with_language='de-cinuru' -- cinuru users are allowed to log in to multiple brands (probably for filmnerd testing?)
			OR ${language}='de' -- dashboard users can login (dashboard language NONE maps to language de)
		)
	`);
	if (res.rows.length > 2) {
		throw new Error(`found more than 2 accounts for ${email} with ${language}`);
	} else if (res.rows.length === 2) {
		return res.rows.find((user) => user.language === language);
	} else if (res.rows.length === 1) {
		const user = res.rows[0];
		return user;
	} else {
		return null;
	}
};

export const requestEmailChange = async (
	_: unknown,
	{ email }: { email: string },
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);
	const { viewer, i18n, language, brand, appLink } = context;
	if (!viewer.userIdDb) return new UserInputError('USER_DOES_NOT_EXIST');
	if (await viewer.isAnonymous()) return new ForbiddenError('REGISTRATION_REQUIRED');
	if (brand === 'CINEPLEX') {
		const { cp_access_token: cpAccessToken, cp_id: cpId } = await db.queryOne(sql`
				SELECT cp_access_token, cp_id FROM priv.p_user WHERE id=${viewer.userIdDb}
		`);
		const { data: success, errorType, errorMessage } = await cp.changeEmail(
			{ email, cpAccessToken },
			context
		);
		if (errorType === 'Input') return new UserInputError(errorMessage);
		if (errorType === 'Forbidden') return new ForbiddenError(errorMessage);
		if (success) {
			await db.query(sql`UPDATE priv.p_user SET cp_last_sync=NULL WHERE cp_id=${cpId}`);
			return { user: User.gen(viewer, viewer.userId) };
		}
	} else {
		const emailAlreadyTaken = await db.query(sql`
			SELECT id FROM priv.p_user WHERE email=${(
				email || ''
			).toLowerCase()} AND registered_with_language=${language} AND is_app_user=true
		`);
		if (emailAlreadyTaken.rows.length > 0) return new UserInputError('DUPLICATE_EMAIL');
		// email isn't taken yet, send confirmation
		const { name, firstName, lastName } = await db.queryOne(sql`
			SELECT username as "name", first_name as "firstName", last_name as "lastName" FROM priv.p_user WHERE id=${viewer.userIdDb}
		`);
		const code = padStart(String(random(1, 999999)), 6, '0');
		const encodedEmail = encodeURIComponent((email || '').toLowerCase());
		const href = `${appLink!}://profile/edit?confirmationCode=${code}&newEmail=${encodedEmail}`;
		const { success } = await sendEmail({
			to: email,
			from: `"${i18n.t('Global.appName')}" <${i18n.t('Global.transactionalOutboundMail')}>`,
			...renderEmailTemplate({
				template: 'ConfirmEmailChange',
				name: getUserName({
					firstName,
					lastName,
					userName: name,
					brand,
				}),
				code,
				href,
				brand,
			}),
		});
		if (!success) {
			return new UserInputError('UNREACHABLE_EMAIL_ADDRESS');
		}
		await db.queryOne(sql`
			UPDATE priv.p_user SET confirmation_code=${'EMAIL:' + code} WHERE id=${viewer.userIdDb}
		`);
		return { user: User.gen(viewer, viewer.userId) };
	}
};

export const resendEmailConfirmation = async (
	_: unknown,
	{ email, isChangeRequest }: { email: string; isChangeRequest?: boolean },
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);
	const { viewer, i18n, language, brand, appLink } = context;

	if (brand === 'CINEPLEX') {
		let res: { data?: boolean; errorType?: string; errorMessage?: string };
		if (isChangeRequest) {
			const user = await db.queryOne(sql`
				SELECT cp_access_token as "cpAccessToken" FROM priv.p_user WHERE id=${viewer.userIdDb}
			`);
			if (!user || !user.cpAccessToken) {
				return new ForbiddenError('EMAIL_UPDATE_REQUIRES_REGISTRATION');
			}
			res = await cp.resendChangeEmailConfirmationEmail(
				{ email, cpAccessToken: user.cpAccessToken },
				context
			);
		} else {
			res = await cp.resendActivationEmail({ email }, context);
		}
		if (res.data) return { success: true, sentToEmail: email };
		if (res.errorType === 'Forbidden') return new ForbiddenError(res.errorMessage);
		else if (res.errorType === 'Input') return new UserInputError(res.errorMessage);
	} else {
		email = email.toLowerCase();
		const user = isChangeRequest
			? await db.queryOne(sql`
					SELECT id, username as "name", first_name as "firstName", last_name as "lastName" FROM priv.p_user WHERE id=${viewer.userIdDb}
				`)
			: await getUserByEmail(email, language);
		if (!user) {
			return new UserInputError('WRONG_EMAIL_OR_ID');
		}
		const code = padStart(String(random(1, 999999)), 6, '0');
		const href = `${appLink!}://verify-email?code=${code}`;
		const template = isChangeRequest ? 'ConfirmEmailChange' : 'ConfirmRegistration';
		const { success } = await sendEmail({
			to: email,
			from: `"${i18n.t('Global.appName')}" <${i18n.t('Global.transactionalOutboundMail')}>`,
			...renderEmailTemplate({
				template,
				name: getUserName({
					firstName: user.firstName,
					lastName: user.lastName,
					userName: user.name,
					brand,
				}),
				code,
				href,
				brand,
			}),
		});
		if (!success) {
			return new UserInputError('UNREACHABLE_EMAIL_ADDRESS');
		}
		await db.queryOne(sql`
			UPDATE priv.p_user SET confirmation_code=${'EMAIL:' + code} WHERE id=${user.id}
		`);
		return { success: true, sentToEmail: email };
	}
};

export const confirmEmail = async (
	_: unknown,
	{
		email,
		code,
		appId,
		isChangeRequest,
	}: { email: string; code: string; appId?: string; isChangeRequest?: boolean },
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);
	const { viewer, language, brand } = context;
	if (isChangeRequest) {
		// changing the email requires authentication
		if (!viewer.userIdDb) return new UserInputError('USER_DOES_NOT_EXIST');
	}
	// get the user from the db by email if confirming login creation or by id if changing email
	let user = isChangeRequest
		? await db.queryOne(sql`
				SELECT 
					id,
					email,
					confirmation_code as "confirmationCode", 
					registered_with_language as "language"
				FROM priv.p_user WHERE id=${viewer.userIdDb}
			`)
		: await getUserByEmail(email, language);

	if (brand === 'CINEPLEX') {
		// check if email is already in database
		const { data, errorType, errorMessage } = await cp.verifyEmail(
			{ email: isChangeRequest ? user.email : email, code, isChangeRequest },
			context
		);
		if (!data) {
			return makeAndRecordLoginError({
				userId: user?.id,
				ipAddress: context.viewer.ipAddress,
				errorType,
				errorMessage,
			});
		} else {
			if (!user) {
				// the user doesn't exist in the database yet, create him
				user = await db.queryOne(sql`
					INSERT INTO priv.p_user(registered_with_language) 
					VALUES (${context.language}) RETURNING id
				`);
			}
			user = await db.queryOne(sql`
				UPDATE priv.p_user 
				SET
					active=true,
					user_blocked_reason=null,
					email=${(email || '').toLowerCase()},
					email_confirmed=true,
					email_changed_datetime=now(),
					cp_access_token=${data.cpAccessToken},
					cp_refresh_token=${data.cpRefreshToken},
					cp_website_token=${data.cpWebsiteToken},
					cp_id=${data.cpId},
					cp_last_sync=null
				WHERE id = ${user.id}
				RETURNING id
			`);
			cineplexCustomerAuthChanged(user.id);
		}
	} else {
		if (isChangeRequest) {
			// another user could have requested a change to the same email and confirmed it first
			const emailAlreadyTaken = await db.query(sql`
				SELECT id FROM priv.p_user WHERE email=${(
					email || ''
				).toLowerCase()} AND registered_with_language=${language} AND is_app_user=true
			`);
			if (emailAlreadyTaken.rows.length > 0) {
				if (emailAlreadyTaken.rows[0].id !== viewer.userIdDb) {
					return new UserInputError('DUPLICATE_EMAIL');
				} else {
					return new UserInputError('EMAIL_ALREADY_CONFIRMED');
				}
			}
		}
		if (!user) {
			return await makeAndRecordLoginError({
				email,
				ipAddress: viewer.ipAddress,
				errorType: 'Input',
				errorMessage: 'USER_DOES_NOT_EXIST',
			});
		}
		if (
			!user.confirmationCode ||
			user.confirmationCode.split(':')[0] !== 'EMAIL' ||
			user.confirmationCode.split(':')[1] !== code ||
			user.language !== language
		) {
			return await makeAndRecordLoginError({
				userId: user?.id,
				ipAddress: viewer.ipAddress,
				errorType: 'Input',
				errorMessage: 'WRONG_CODE',
			});
		}
		// code is correct, confirm user
		user = await db.queryOne(sql`
			UPDATE priv.p_user SET 
				email=${(email || '').toLowerCase()},
				email_confirmed=true,
				email_changed_datetime=now(),
				confirmation_code=null,
				active=active OR (user_blocked_reason = 'MISSING_EMAIL_VERIFICATION'),
				user_blocked_reason=CASE
					WHEN user_blocked_reason = 'MISSING_EMAIL_VERIFICATION' THEN null
					ELSE user_blocked_reason
				END
			WHERE id=${user.id} 
			RETURNING id
		`);
	}
	return await executeLogin(user.id, { appId }, context);
};

export const requestPasswordReset = async (
	_: unknown,
	{ email }: { email: string },
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);
	const { language, brand, i18n } = context;
	if (brand === 'CINEPLEX') {
		const { data: success, errorType, errorMessage } = await cp.resetPassword({ email }, context);
		if (errorType === 'Input') return new UserInputError(errorMessage);
		if (success) return { success: true };
	} else {
		const user = await getUserByEmail(email, language);
		if (!user) return new UserInputError('WRONG_EMAIL_OR_ID');
		if (await isAnonymousUser(user.id)) return new ForbiddenError('REGISTRATION_REQUIRED');
		// send password reset code
		const code = padStart(String(random(1, 999999)), 6, '0');
		const href = `cinuru://reset-password?code=${code}`;
		const { success } = await sendEmail({
			to: email,
			from: `"${i18n.t('Global.appName')}" <${i18n.t('Global.transactionalOutboundMail')}>`,
			...renderEmailTemplate({
				template: 'ResetPassword',
				name: getUserName({
					firstName: user.firstName,
					lastName: user.lastName,
					userName: user.name,
					brand,
				}),
				code,
				href,
				brand,
			}),
		});
		if (!success) {
			// this shouldn't happen as we have verified the email
			return new UserInputError('UNREACHABLE_EMAIL_ADDRESS');
		}
		await db.queryOne(sql`
			UPDATE priv.p_user SET confirmation_code=${'PASSWORD:' + code} WHERE id=${user.id}
		`);
		return { success: true };
	}
};
export const changePassword = async (
	_: unknown,
	{
		email,
		password,
		code,
		appId,
	}: { email: string; password: string; code: string; appId?: string },
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);
	const { language, viewer } = context;
	if (language === 'de-cineplex') {
		const { data, errorType, errorMessage } = await cp.updatePassword(
			{ email, password, code },
			context
		);
		if (!data) {
			return makeAndRecordLoginError({
				email,
				ipAddress: context.viewer.ipAddress,
				errorType,
				errorMessage,
			});
		}
		const user = await db.queryOne(sql`
			UPDATE priv.p_user SET 
				cp_access_token=${data.cpAccessToken},
				cp_refresh_token=${data.cpRefreshToken}, 
				cp_website_token=${data.cpWebsiteToken}
			WHERE email=${(email || '').toLowerCase()} AND registered_with_language=${language}
			RETURNING id;
		`);
		if (user) {
			cineplexCustomerAuthChanged(user.id);
			return await executeLogin(user.id, { appId }, context);
		}
		// the user might have been created on the website and not yet exist in the cinuru database
		const newUser = await db.queryOne(sql`
			INSERT INTO priv.p_user(
				email, 
				email_confirmed,
				cp_access_token,
				cp_refresh_token,
				cp_website_token,
				cp_id,
				registered_with_language
			) VALUES (
				${email},
				TRUE,
				${data.cpAccessToken},
				${data.cpRefreshToken},
				${data.cpWebsiteToken},
				${data.cpId},
				${language}
			) RETURNING id;
		`);
		return await executeLogin(newUser.id, { appId }, context);
	} else {
		const user = await getUserByEmail(email, language);
		if (!user) return new UserInputError('WRONG_EMAIL_OR_ID');
		if (await isAnonymousUser(user.id)) return new ForbiddenError('REGISTRATION_REQUIRED');
		if (
			!user.confirmationCode ||
			user.confirmationCode.split(':')[0] !== 'PASSWORD' ||
			user.confirmationCode.split(':')[1] !== code ||
			user.language !== language
		) {
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				userId: user.id,
				errorType: 'Input',
				errorMessage: 'WRONG_CODE',
			});
		}
		// code is correct, change password
		await db.queryOne(sql`
			UPDATE priv.p_user SET 
				confirmation_code=null, 
				password_hash=crypt(${password}, gen_salt('bf')) 
			WHERE id=${user.id}
		`);
		return await executeLogin(user.id, { appId }, context);
	}
};

export const testing_getConfirmationCode = async (
	_: unknown,
	{ email, type }: { email: string; type: 'LOGIN_CREATION' | 'EMAIL_CHANGE' | 'PASSWORD_RESET' },
	context: Context
) => {
	// for email change the old email must be provided
	if (!ENABLE_MOCK_APIS) {
		throw new ForbiddenError('only available in testing environments');
	}
	if (context.language === 'de-cineplex') {
		return await cp.testing_getConfirmationCode({ email, type }, context);
	} else {
		const user = await getUserByEmail(email, context.language);
		if (!user?.confirmationCode) return null;
		const [codeType, code] = user.confirmationCode.split(':');
		switch (type) {
			case 'LOGIN_CREATION':
			case 'EMAIL_CHANGE':
				return codeType === 'EMAIL' ? code : null;
			case 'PASSWORD_RESET':
				return codeType === 'PASSWORD' ? code : null;
			default:
				return null;
		}
	}
};

/**
 * @deprecated use requestLoginCreation instead
 */
export const addLogin = async (
	_: unknown,
	{
		email,
		password,
		name,
		appId,
	}: {
		email: string;
		password: string;
		name?: string;
		appId?: string;
	},
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);

	email = email.toLowerCase();
	const { viewer, i18n } = context;

	if (!viewer.hasUserId || !viewer.userId) {
		// user hasn't been created yet, abort
		return await makeAndRecordLoginError({
			ipAddress: viewer.ipAddress,
			email,
			errorType: 'Input',
			errorMessage: 'USER_DOES_NOT_EXIST',
		});
	}

	if (!(await viewer.isAnonymous())) {
		// not an anonymou user, abort
		return await makeAndRecordLoginError({
			ipAddress: viewer.ipAddress,
			email,
			errorType: 'Input',
			errorMessage: 'USER_ALREADY_REGISTERED',
		});
	}

	if (context.language === 'de-cineplex') {
		throw new Error('endpoint not supported for cineplex, you must have an old app version');
	} else {
		if (!name) {
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				email,
				errorType: 'Input',
				errorMessage: 'NAME_MISSING',
			});
		}

		if (password.length < 8) {
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				email,
				errorType: 'Input',
				errorMessage: 'PASSWORD_INVALID',
			});
		}

		const emailAlreadyTaken = await db.query(
			`SELECT DISTINCT language
			FROM priv.p_user u JOIN priv.p_app a ON a.user_id = u.id
			WHERE email=$1 AND is_app_user=true`,
			[(email || '').toLowerCase()]
		);

		if (emailAlreadyTaken.rows.length > 0) {
			// email already exists, abort and let user know in which brand email exists
			const accountBrands = emailAlreadyTaken.rows.map((u: { language?: string }) => {
				if (!u.language) return 'CINURU';
				const [, brand] = u.language.split('-');
				return brand ? brand.toUpperCase() : 'CINURU';
			});
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				email,
				errorType: 'Input',
				errorMessage: 'DUPLICATE_EMAIL',
				errorProperties: { accountBrands },
			});
		}
		const emailConfirmToken = (await db.query('SELECT random_text_md5(70)', [])).rows[0]
			.random_text_md5;
		const href = `https://cinuru.com/verify-email?token=${emailConfirmToken}`;
		const { success } = await sendEmail({
			to: email,
			from: `"${i18n.t('Global.appName')}" <${i18n.t('Global.transactionalOutboundMail')}>`,
			...renderEmailTemplate({ template: 'ConfirmRegistration', name, href }),
		});
		if (!success) {
			// couldn't sent email confirmation, abort
			//sending emails is deactivated for testing - so it is not possible to test this error
			return await makeAndRecordLoginError({
				ipAddress: viewer.ipAddress,
				email,
				errorType: 'Input',
				errorMessage: 'UNREACHABLE_EMAIL_ADDRESS',
			});
		}

		// user doesn't exist yet and confirmation email has been sent, upgrade to registered user
		const res = await db.query(
			`UPDATE priv.p_user SET 
				email=$2,
				email_confirmed=false,
				email_reminder_sent=false,
				welcome_email_sent=true,
				email_confirm_token=$3,
				email_changed_datetime=now(), 
				password_hash=crypt($4, gen_salt('bf')),
				username=$5, 
				is_app_user=true
			WHERE id=$1 RETURNING id`,
			[viewer.userIdDb, (email || '').toLowerCase(), emailConfirmToken, password, name]
		);
		if (res.rows.length === 1) {
			return await executeLogin(res.rows[0].id, { appId }, context);
		}
	}
};

/**
 * @deprecated use confirmEmail instead
 */
export const verifyEmail = async (
	_: unknown,
	{ appId, token }: { appId?: string; token: string },
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);
	let user;
	if (context.language === 'de-cineplex') {
		throw new Error('endpoint not supported for cineplex, you must have an old app version');
	} else {
		user = await db.queryOne(sql`
			SELECT id, email_confirmed, user_blocked_reason 
			FROM priv.p_user 
			WHERE email_confirm_token = ${token};
		`);
		if (!user) {
			return new UserInputError('INVALID_TOKEN');
		}
		if (user.email_confirmed) {
			return new UserInputError('EMAIL_ALREADY_CONFIRMED');
		}
		await db.queryOne(sql`
			UPDATE priv.p_user 
			SET email_confirmed=TRUE 
			WHERE id = ${user.id};
		`);
	}
	if (user.user_blocked_reason === 'MISSING_EMAIL_VERIFICATION') {
		// The user was blocked because the email was not confirmed in time. Unblock them.
		await db.queryOne(sql`
			UPDATE priv.p_user 
			SET active=TRUE, user_blocked_reason=NULL
			WHERE id = ${user.id};
		`);
	}
	return await executeLogin(user.id, { appId }, context);
};

/**
 * @deprecated use changePassword instead
 */
export const updatePassword = async (
	_: unknown,
	{
		oldPassword,
		token,
		email,
		password,
		appId,
	}: { oldPassword?: string; token?: string; password: string; appId?: string; email?: string },
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);
	let user;
	if (context.language === 'de-cineplex') {
		throw new Error('endpoint not supported for cineplex, you must have an old app version');
	} else {
		if (token) {
			user = await db.queryOne(sql`
				SELECT id 
				FROM priv.p_user
				WHERE 
					password_reset_token = ${token} AND 
					password_reset_request_datetime > now() - interval '1 day'
			`);
			if (!user) {
				return await makeAndRecordLoginError({
					email,
					ipAddress: context.viewer.ipAddress,
					errorType: 'Input',
					errorMessage: 'INVALID_TOKEN',
				});
			}
		} else if (oldPassword) {
			user = await db.queryOne(sql`
				SELECT id 
				FROM priv.p_user 
				WHERE 
					id=${context.viewer.userIdDb} AND
					(password_hash IS NULL OR password_hash = crypt(${oldPassword}, password_hash))
			`);
			if (!user) {
				return await makeAndRecordLoginError({
					email,
					ipAddress: context.viewer.ipAddress,
					errorType: 'Input',
					errorMessage: 'WRONG_PASSWORD',
				});
			}
		} else {
			return new UserInputError('NEITHER_PASSWORD_NOR_TOKEN_PROVIDED');
		}
		if (password.length <= 8) {
			return new UserInputError('PASSWORD_INVALID');
		}
		await db.query(sql`
			UPDATE priv.p_user SET password_hash = crypt(${password}, gen_salt('bf')) WHERE id=${user.id}
		`);
	}
	return await executeLogin(user.id, { appId }, context);
};

export const login = async (
	_: unknown,
	{
		email,
		password,
		privileged,
		appId,
		logoutFromOtherApps,
	}: {
		email: string;
		password: string;
		privileged?: boolean;
		appId?: string;
		logoutFromOtherApps?: boolean; // This is used to ensure cinfinity users are only logged in into one app at a time
	},
	context: Context
) => {
	console.log('in login', email, privileged, appId, logoutFromOtherApps, context.language);
	await checkRateLimit(context.viewer.ipAddress);
	email = email && email.toLowerCase();

	if (context.language === 'de-cineplex') {
		const { data, errorType, errorMessage, errorProperties } = await cp.login(
			{ email, password },
			context
		);
		if (data) {
			// login with cineplex worked, so upsert user in database and log him in
			const { cpAccessToken, cpRefreshToken, cpWebsiteToken, cpId } = data;
			// the user might not have a cp id yet, because he only gets one after confirming his email, but we need to ensure the anonymous user get's properly updated, so we might have already recorded his email thus we cannot use on conflict but need to check on both unique constraints
			let res = await db.query(
				`SELECT id from priv.p_user 
				WHERE email=$1 OR cp_id=$2`,
				[(email || '').toLowerCase(), cpId]
			);
			if (res.rows.length > 0) {
				if (res.rows.length > 1) {
					sendErrorMail(
						'Login matched more than one account',
						`email: ${email}, cp_id: ${cpId}, accessToken ${cpAccessToken} `
					);
				}
				res = await db.query(sql`
					UPDATE priv.p_user SET 
						email=${(email || '').toLowerCase()},
						email_confirmed=TRUE,
						active=TRUE,
						user_blocked_reason=NULL,
						cp_access_token=${cpAccessToken},
						cp_refresh_token=${cpRefreshToken},
						cp_website_token=${cpWebsiteToken},
						cp_id=${cpId},
						cp_last_sync=NULL
					WHERE 
						id=${res.rows[0].id} 
					RETURNING id;
				`);
				cineplexCustomerAuthChanged(res.rows[0].id);
			} else {
				res = await db.query(sql`
					INSERT INTO priv.p_user(
						email, 
						email_confirmed,
						cp_access_token,
						cp_refresh_token,
						cp_website_token,
						cp_id,
						registered_with_language
					) VALUES (
						${email},
						TRUE,
						${cpAccessToken},
						${cpRefreshToken},
						${cpWebsiteToken},
						${cpId},
						${context.language}
					) RETURNING id;
				`);
			}
			return executeLogin(res.rows[0].id, { appId, privileged }, context);
		} else {
			return makeAndRecordLoginError({
				email,
				ipAddress: context.viewer.ipAddress,
				errorType,
				errorMessage,
				errorProperties,
			});
		}
	} else {
		const user = await getUserByEmail(email, context.language, password);
		if (!user) {
			return await makeAndRecordLoginError({
				ipAddress: context.viewer.ipAddress,
				email,
				privileged,
				errorType: 'Input',
				errorMessage: 'WRONG_EMAIL_OR_ID',
			});
		}
		if (!user.correctPassword) {
			return await makeAndRecordLoginError({
				ipAddress: context.viewer.ipAddress,
				email,
				privileged,
				errorType: 'Input',
				errorMessage: 'WRONG_PASSWORD',
			});
		}
		if (context.language === 'de-cinfinity' && !privileged) {
			// Find out wether this is a login on a new app
			const [currentUniqueDeviceId] = atob(appId).split(':');
			const lastAppId = (
				await db.query(sql`
					SELECT app_id FROM priv.p_app 
					WHERE user_id=${user.id} 
					ORDER BY updatet DESC
					LIMIT 1
				;
			`)
			).rows[0]?.app_id;

			// the appId is a base64 encoded string: 'uniqueDeviceId:UUID'
			const [lastUniqueDeviceId] = lastAppId ? atob(lastAppId)?.split(':') : [null];
			const isFirstLogin = !lastUniqueDeviceId;
			const isLoginWithDifferentDeviceId =
				lastUniqueDeviceId !== '[object Promise]' && lastUniqueDeviceId !== currentUniqueDeviceId;
			const isOldAppVersionWithDifferentAppId =
				lastUniqueDeviceId === '[object Promise]' &&
				currentUniqueDeviceId === '[object Promise]' &&
				lastAppId !== appId;
			const isLoginToNewApp =
				isFirstLogin || isOldAppVersionWithDifferentAppId || isLoginWithDifferentDeviceId;
			if (isLoginToNewApp && !user.testingStatus) {
				// if this a login to a new app and the user is blocked, throw an error
				const blockedUntilRes = await db.queryOne(
					sql`SELECT block_app_change_until FROM priv.p_user WHERE id = ${user.id};`
				);
				if (
					blockedUntilRes.block_app_change_until &&
					blockedUntilRes.block_app_change_until > new Date()
				) {
					// The user is not allowed to change the app
					return await makeAndRecordLoginError({
						ipAddress: context.viewer.ipAddress,
						email,
						privileged,
						errorType: 'Expected',
						errorMessage: 'APP_CHANGE_BLOCKED',
					});
				}
			}
			if (isLoginToNewApp && !isFirstLogin && !logoutFromOtherApps) {
				// if this a login from a new app, and the user is not blocked and it is not the first login and he did not explicitly told to logoutFromOtherApps make sure to warn the user
				// that he will be locked to the new app for 90 days
				return await makeAndRecordLoginError({
					ipAddress: context.viewer.ipAddress,
					email,
					privileged,
					errorType: 'Expected',
					errorMessage: 'ALREADY_LOGGED_IN_OTHER_APP',
				});
			}
			if (logoutFromOtherApps) {
				console.log('logoutFromOtherApps');
				//Logout the user from all other apps
				await db.query(
					sql`UPDATE priv.p_app SET refresh_token = NULL WHERE user_id = ${user.id} AND app_id !=${appId};`
				);
			}
			if (isLoginToNewApp) {
				//Set the block app change period to now + 90 days
				// We do this here in the and, so that the blocked period only gets set if no error ocurred
				await db.query(
					sql`UPDATE priv.p_user SET block_app_change_until = now() + interval '90 days' WHERE id=${user.id};`
				);
			}
		}

		return await executeLogin(user.id, { privileged, appId }, context);
	}
};

const fb = new Facebook();
const verifyFacebookToken = async (token: string): Promise<{ id: string; email?: string }> => {
	return new Promise((resolve) =>
		fb.api('/me', { fields: 'id,name,email', access_token: token }, resolve)
	);
};

const google = new Google();
const verifyGoogleToken = async (token: string, webClientId: string) => {
	const ticket = await google.verifyIdToken({ idToken: token, audience: webClientId });
	const { sub: id, email } = ticket.getPayload();
	return { id, email };
};

const verifyAppleToken = async (token: string) => {
	const { sub: id, email } = await new Promise<{ sub: string; email: string }>((resolve, reject) =>
		jwt.verify(
			token,
			(header, onError) => {
				jwksClient({ jwksUri: 'https://appleid.apple.com/auth/keys' }).getSigningKey(
					header.kid,
					// @ts-ignore
					(err, key) => onError(err, key.publicKey || key.rsaPublicKey)
				);
			},
			{
				issuer: 'https://appleid.apple.com',
				audience: APPLE_SIGNIN_CLIENT_IDS,
			},
			(err, decoded) => (err ? reject(err) : resolve(decoded as { sub: string; email: string }))
		)
	);
	return { id, email };
};

export const socialLogin = async (
	_: unknown,
	{
		provider,
		accessToken,
		idToken,
		appId,
		email,
		name,
		firstName,
		lastName,
		socialLoginSession,
	}: {
		provider: 'APPLE' | 'GOOGLE' | 'FACEBOOK';
		accessToken?: string;
		idToken?: string;
		appId?: string;
		email?: string; // this is only for cineplex, we use the email from the token instead
		name?: string;
		firstName?: string;
		lastName?: string;
		socialLoginSession?: string;
	},
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);

	const viewer = context.viewer;
	const { token, dbIdName } = {
		FACEBOOK: { dbIdName: 'fb_id', token: accessToken },
		GOOGLE: { dbIdName: 'google_id', token: idToken },
		APPLE: { dbIdName: 'apple_id', token: idToken },
	}[provider] as { token: string; dbIdName: 'fb_id' | 'google_id' | 'apple_id' };
	if (!token) {
		return await makeAndRecordLoginError({
			email,
			ipAddress: context.viewer.ipAddress,
			privileged: false,
			errorType: 'Input',
			errorMessage: 'MISSING_SOCIAL_ACCESS_TOKEN',
		});
	}

	if (context.language === 'de-cineplex') {
		const { data, errorType, errorMessage, errorProperties } = await cp.socialLogin(
			{ provider, token, email, firstName, lastName, socialLoginSession },
			context
		);
		if (data) {
			// social login with cineplex worked, so upsert user in database and log him in
			const { cpAccessToken, cpRefreshToken, cpWebsiteToken, cpId, socialAuthId } = data;
			const fbId = dbIdName === 'fb_id' ? socialAuthId : null;
			const googleId = dbIdName === 'google_id' ? socialAuthId : null;
			const appleId = dbIdName === 'apple_id' ? socialAuthId : null;
			// try updating anonymous or registered user if he can be found
			let res = await db.query(sql`
				SELECT id FROM priv.p_user WHERE email=${(email || '').toLowerCase()} OR cp_id=${cpId}
			`);
			if (res.rows.length > 1) {
				sendErrorMail(
					'social login duplicates',
					`social login for cineplex with email=${(
						email || ''
					).toLowerCase()} was somehow already in the database under cp_id=${cpId}`
				);
				return await makeAndRecordLoginError({
					email,
					ipAddress: context.viewer.ipAddress,
					socialAuthToken: token,
					socialAuthProvider: provider,
					privileged: false,
					errorType: 'SocialAuth',
					errorMessage: 'SOCIAL_AUTH_FAILED',
				});
			}
			if (res.rows.length === 1 || viewer.hasUserId) {
				res = await db.query(sql`
					UPDATE priv.p_user SET 
						email_confirmed=TRUE,
						active=TRUE,
						user_blocked_reason=NULL,
						email=${(email || '').toLowerCase()},
						cp_access_token=${cpAccessToken},
						cp_refresh_token=${cpRefreshToken},
						cp_website_token=${cpWebsiteToken},
						cp_id=${cpId},
						fb_id=COALESCE(${fbId}, fb_id),
						google_id=COALESCE(${googleId}, google_id),
						apple_id=COALESCE(${appleId}, apple_id),
						cp_last_sync=NULL
					WHERE id=${res.rows.length === 1 ? res.rows[0].id : viewer.userIdDb}
					RETURNING id;
				`);
				cineplexCustomerAuthChanged(res.rows[0].id);
			} else {
				res = await db.query(sql`
					INSERT INTO priv.p_user(
						email,
						email_confirmed,
						cp_access_token,
						cp_refresh_token,
						cp_website_token,
						cp_id,
						fb_id,
 						google_id,
 						apple_id,
						registered_with_language
					) VALUES (
						${email}, 
						TRUE,
						${cpAccessToken},
						${cpRefreshToken},
						${cpWebsiteToken},
						${cpId},
						${fbId},
						${googleId},
						${appleId},
						${context.language}
					) RETURNING id;
				`);
			}
			// the user may have been created or updated on the website, so we need to sync him
			return executeLogin(res.rows[0].id, { appId }, context);
		} else {
			if (errorMessage === 'USER_EMAIL_UNCONFIRMED') {
				// to ensure the anonymous user is not overwritten when login is called after the email confirmation, we must write the email to the database here
				await db.query(
					`UPDATE priv.p_user SET 
						email=$2,
						email_confirmed=FALSE,
						active=FALSE,
						user_blocked_reason='MISSING_EMAIL_VERIFICATION',
						email_changed_datetime=now()
					WHERE id=$1 RETURNING id`,
					[context.viewer.userIdDb, (email || '').toLowerCase()]
				);
			}
			return await makeAndRecordLoginError({
				email,
				ipAddress: context.viewer.ipAddress,
				errorType,
				errorMessage,
				errorProperties,
			});
		}
	}

	let auth: { id: string; email?: string; name?: string };
	try {
		auth = await {
			FACEBOOK: () => verifyFacebookToken(token),
			GOOGLE: () => verifyGoogleToken(token, GOOGLE_WEB_CLIENT_ID_BY_LANGUAGE[context.language]),
			APPLE: () => verifyAppleToken(token),
		}[provider]();
		if (auth.email && email && auth.email !== email) {
			// this should never happen
			throw new Error(
				`Email missmatch between query param '${email}' and auth token '${auth.email}'`
			);
		}
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (e) {
		sendErrorMail(
			'Social login error',
			JSON.stringify({ provider, token, appId, email, name, error: e.toString() })
		);
		return await makeAndRecordLoginError({
			email,
			ipAddress: context.viewer.ipAddress,
			socialAuthToken: token,
			socialAuthProvider: provider,
			privileged: false,
			errorType: 'SocialAuth',
			errorMessage: 'SOCIAL_AUTH_FAILED',
		});
	}
	auth.email = auth.email && auth.email.toLowerCase();

	const existingUser = await db.query(
		/* sql */ `
			SELECT id 
			FROM priv.p_user 
			WHERE 
				${dbIdName}=$1 AND 
				(registered_with_language=$2 OR registered_with_language='de-cinuru')
		`,
		[auth.id, context.language]
	);
	if (existingUser.rows.length > 2) {
		throw new Error(`found more than 2 accounts for ${email} with ${context.language}`);
	} else if (existingUser.rows.length === 2) {
		return await executeLogin(
			existingUser.rows.find((user) => user.language === context.language).id,
			{ appId },
			context
		);
	} else if (existingUser.rows.length === 1) {
		// only cinuru users are allowed to log in to multiple brands
		if (
			existingUser.rows[0].language === context.language ||
			existingUser.rows[0].language === 'de-cinuru'
		) {
			return await executeLogin(existingUser.rows[0].id, { appId }, context);
		}
	}

	if (auth.email) {
		const res = await db.query(
			/* sql */ `
				UPDATE priv.p_user SET 
					${dbIdName}=$1,
					username=COALESCE(username, $3),
					email_confirmed=true,
					confirmation_code=null,
					registered_with_language=$4,
					active=active OR (user_blocked_reason = 'MISSING_EMAIL_VERIFICATION'),
					user_blocked_reason=CASE
						WHEN user_blocked_reason = 'MISSING_EMAIL_VERIFICATION' THEN null
						ELSE user_blocked_reason
					END
				WHERE email=$2
				RETURNING id
			`,
			[auth.id, (auth.email || '').toLowerCase(), name || auth.name, context.language]
		);
		if (res.rows.length === 1) {
			// We found a user with that email, updated them and now log them in.
			return await executeLogin(res.rows[0].id, { appId }, context);
		}
	}

	if (viewer.hasUserId) {
		// A user is registering an account with a social login
		if (!(await viewer.isAnonymous())) {
			// adding a social login to a user who already has an account, where the social email and account
			// email don't match, is identical to changing the password and should require the oldPassword
			// in case we need this in the future, add a case here that requires passing the current password
			return await makeAndRecordLoginError({
				email,
				ipAddress: viewer.ipAddress,
				socialAuthToken: token,
				privileged: false,
				errorType: 'Forbidden',
				errorMessage: 'CANNOT_ADD_SOCIAL_LOGIN_WITH_DIFFERENT_EMAIL',
			});
		}
		// add social login to anonymous user
		const res = await db.query(
			`UPDATE priv.p_user SET ${dbIdName}=$2, username=$3, email=$4, email_confirmed=$5
			WHERE id=$1 RETURNING id`,
			[viewer.userIdDb, auth.id, name, (auth.email || '').toLowerCase(), Boolean(auth.email)]
		);
		return await executeLogin(res.rows[0].id, { appId }, context);
	}

	// We cannot find the user by email or social id in our database and the user is not logged in.
	return await makeAndRecordLoginError({
		email,
		ipAddress: viewer.ipAddress,
		socialAuthToken: token,
		privileged: false,
		errorType: 'Input',
		errorMessage: 'USER_DOES_NOT_EXIST',
	});
};

export const loginPOS = async (
	_: unknown,
	{ authToken }: { authToken: string },
	context: Context
) => {
	await checkRateLimit(context.viewer.ipAddress);

	const res = await db.query(sql`
		SELECT id, currency_id as "bonusProgramId", cinema_id as "cinemaId" 
		FROM priv.p_pos_device 
		WHERE auth_token=${authToken} AND active=true
	`);
	if (res.rows.length === 1) {
		const pos = res.rows[0];
		const posApp = {
			id: encodeDbId('PosApp', pos.id),
			deviceId: pos.id,
			cinemaId: Cinema.encodeDbId(pos.cinemaId),
			bonusProgramId: BonusProgram.encodeDbId(pos.bonusProgramId),
		};
		return executeLogin(null, { posApp }, context);
	}
	return await makeAndRecordLoginError({
		ipAddress: context.viewer.ipAddress,
		posAuthToken: authToken,
		errorType: 'Input',
		errorMessage: 'INVALID_AUTHTOKEN',
	});
};

export const refreshLogin = async (
	_: unknown,
	{ refreshToken, privileged }: { refreshToken: string; privileged: boolean },
	context: Context
) => {
	const startDate = Date.now();
	await checkRateLimit(context.viewer.ipAddress);

	const res = await db.query(sql`
		SELECT 
			a.user_id,
			a.refresh_token_valid_until > now() AS refresh_token_valid,
			u.cp_refresh_token
		FROM priv.p_app a
		JOIN priv.p_user u ON a.user_id = u.id 
		WHERE refresh_token=${refreshToken}
	`);

	if (res.rows.length === 1) {
		const userId = res.rows[0].user_id;

		if (privileged) {
			const allPivileges = await db.query(
				sql`SELECT privilege FROM priv.p_user_privilege WHERE user_id=${userId}`
			);
			if (!allPivileges.rows.length) {
				return await makeAndRecordLoginError({
					ipAddress: context.viewer.ipAddress,
					userId,
					email: null,
					privileged,
					errorType: 'Input',
					errorMessage: 'NO_PRIVILEGES',
				});
			}
		}

		if (!res.rows[0].refresh_token_valid) {
			return await makeAndRecordLoginError({
				ipAddress: context.viewer.ipAddress,
				userId,
				email: null,
				privileged: false,
				errorType: 'Input',
				errorMessage: 'OUTDATED_REFRESH_TOKEN',
			});
		}

		// set refresh tolen validity
		if (await isAnonymousUser(userId)) {
			await db.query(sql`
				UPDATE priv.p_app SET 
					refresh_token_valid_until=now() + interval '1095days'
				WHERE refresh_token=${refreshToken}
			`);
		} else {
			await db.query(sql`
				UPDATE priv.p_app SET 
					refresh_token_valid_until=now() + interval '180days'
				WHERE refresh_token=${refreshToken}
			`);
		}

		const token = await executeLogin(userId, { privileged }, context);
		return token;
	}
	if (res.rows.length > 1) {
		// The likelihood of this ever happening is probably lower than the likelihood of the moon crashing into earth but just in case...
		sendErrorMail(
			'Duplicated refresh token, please fix',
			`Here is the refresh token: ${refreshToken}`
		);
	}
	return await makeAndRecordLoginError({
		ipAddress: context.viewer.ipAddress,
		userId: null,
		email: null,
		privileged: false,
		errorType: 'Input',
		errorMessage: 'INVALID_REFRESH_TOKEN',
	});
};

export const logout = async (_: unknown, { appId }: { appId: string }, context: Context) => {
	// anonymous users cannot log back in, so we mark orphaned anonymous accounts as inactive
	if (context.viewer.hasUserId && (await context.viewer.isAnonymous())) {
		await db.query(
			`UPDATE priv.p_user SET active=false, user_blocked_reason='ANONYMOUS_USER_LOGGED_OUT' WHERE id=$1;`,
			[context.viewer.userIdDb]
		);
	}
	if (appId) {
		// If an app id is provided, we want to delete the apps refresh token
		await db.query(sql`
			UPDATE priv.p_app 
			SET refresh_token=NULL, refresh_token_valid_until=NULL
			WHERE app_id=${appId} AND user_id=${context.viewer.userIdDb};
		`);
	}

	if (context && context.httpReq) {
		context.httpReq.session = null;
	}
	return { success: true };
};

const csrf = new CSRFTokens();
export const executeLogin = async (
	userIdDb: InternalUserId,
	privileges: {
		posApp?: {
			id: string;
			deviceId: number;
			cinemaId: InternalCinemaId;
			bonusProgramId: InternalBonusProgramId;
		};
		privileged?: boolean;
		appId?: string;
		anonymous?: boolean;
	},
	context: Context
) => {
	// This function is a helper method that does the actual work of loging in a user and is
	// an abstraction on top of the several login methods (login, socialLogin, loginPOS), but is also
	// used in notifications to login the notification sending user
	//
	// This function creates and returns the LoginReturnType (for graphql), but also sets the
	// auth cookies on the session
	const { posApp, privileged, appId, anonymous } = privileges;

	let roles: string[], expiresIn: string, adminForCinemas, adminForBonusPrograms;
	if (userIdDb) {
		// ensure user isn't blocked
		const res = await db.query(sql`
			SELECT active, user_blocked_reason, email FROM priv.p_user WHERE id=${userIdDb}
		`);
		const { active, email, user_blocked_reason } = res.rows[0];
		if (!active && user_blocked_reason !== 'MISSING_EMAIL_VERIFICATION') {
			return await makeAndRecordLoginError({
				userId: userIdDb,
				ipAddress: context.viewer.ipAddress,
				privileged: false,
				errorType: 'Blocked',
				errorMessage: 'USER_INVALID_OR_BLOCKED',
				errorProperties: { userBlockedReason: user_blocked_reason },
			});
		}
		// record the login
		await db.query(`UPDATE priv.p_user SET last_login=now() WHERE id=$1;`, [userIdDb]);
		await db.query(`INSERT INTO priv.p_user_login_logs(user_id, datetime) VALUES ($1, now());`, [
			userIdDb,
		]);
		if (!anonymous) {
			// if this is not an anonymous user creation, but an anonymous user logging in with an existing account, mark the orphaned anonynous user
			if (
				context.viewer.hasUserId &&
				context.viewer.userIdDb !== userIdDb &&
				(await context.viewer.isAnonymous())
			) {
				db.query(
					`UPDATE priv.p_user SET active = false, user_blocked_reason = 'OTHER_ACCOUNT_EXISTED' WHERE id=$1;`,
					[context.viewer.userIdDb]
				);
			}
		}
		if (privileged) {
			expiresIn = '1h';
			const allPrivileges = await db.query(sql`
				SELECT privilege FROM priv.p_user_privilege WHERE user_id = ${userIdDb}
			`);
			if (!allPrivileges.rows.length) {
				return await makeAndRecordLoginError({
					ipAddress: context.viewer.ipAddress,
					userId: userIdDb,
					email,
					privileged,
					errorType: 'Forbidden',
					errorMessage: 'NO_PRIVILEGES',
				});
			}

			roles = ['USER'];
			let rootRole = false;
			let adminRole = false;
			let supportRole = false;
			let cinemaAdminRole = false;
			allPrivileges.rows.forEach((el) => {
				if (el.privilege === 'ROOT') {
					rootRole = true;
					adminRole = true;
				}
				if (el.privilege === 'ADMIN') {
					adminRole = true;
				}
				if (el.privilege === 'SUPPORT') {
					supportRole = true;
				}
				if (el.privilege === 'CINEMA_ADMIN') {
					cinemaAdminRole = true;
				}
			});
			if (rootRole) {
				roles.push('ROOT');
			}
			if (adminRole) {
				roles.push('ADMIN');
			}
			if (supportRole) {
				roles.push('SUPPORT');
			}
			if (rootRole || adminRole || supportRole || cinemaAdminRole) {
				roles.push('CINEMA_ADMIN');
			}
		} else {
			expiresIn = '24h';
			roles = ['USER'];
		}
	}
	if (posApp) {
		expiresIn = '72h';
		roles = ['POS_APP'];
	}
	let refreshToken = null;

	if (appId) {
		refreshToken = cryptoRandomString({ length: 30 });
		// anonymous users don't have a password to log back in and should thus stay active for 3 years, until they should be deleted
		const res = await db.query(
			`INSERT INTO priv.p_app(
				app_id, user_id, refresh_token, refresh_token_valid_until, updatet
			) VALUES ($1, $2, $3, now() + interval '${anonymous ? 1095 : 180}days', now())
			ON CONFLICT (app_id) DO UPDATE SET 
				user_id=$2, refresh_token=$3, 
				refresh_token_valid_until=now() + interval '${anonymous ? 1095 : 180}days', updatet=now()
			RETURNING app_id;`,
			[appId, userIdDb, refreshToken]
		);
		if (res.rows.length !== 1) refreshToken = null;
	}
	const jwtData = {
		userId: userIdDb ? User.encodeDbId(userIdDb) : null,
		roles,
		adminForCinemas,
		adminForBonusPrograms,
		posApp,
	};
	const currentJWT = createJWT(jwtData, expiresIn);
	const fileServerToken = createFileServerJWT(
		{ userId: userIdDb ? User.encodeDbId(userIdDb) : null },
		expiresIn
	);

	const csfrSecret = csrf.secretSync();
	let ipAddress, userAgent, requestId, clientVersion;
	if (context && context.httpReq) {
		const req = context.httpReq;
		// If this is called in the server context (login mutations), then setup session
		// Do not setup session if called internally (e.g. notifications)
		// @ts-ignore
		req.session = req.session || {};
		// @ts-ignore
		req.session.secret = csfrSecret;
		// @ts-ignore
		req.session.expiresIn = expiresIn;
		// @ts-ignore
		req.session.jwt = currentJWT;

		if (req.header) {
			ipAddress = req.header('x-forwarded-for') || (req.connection && req.connection.remoteAddress);
			userAgent = req.header('user-agent') || '';
			requestId = req.header('requestId');
			clientVersion = req.header('version');
		}
	}
	const viewer = createViewerFromJWT(
		currentJWT,
		context.language,
		null,
		ipAddress,
		userAgent,
		requestId,
		clientVersion
	);
	return {
		jwt: currentJWT,
		fileServerToken,
		csrf: csrf.create(csfrSecret),
		user: userIdDb && (await User.gen(viewer, User.encodeDbId(userIdDb))),
		posApp,
		roles,
		refreshToken,
	};
};

export const makeAndRecordLoginError = async ({
	ipAddress,
	userId,
	email,
	socialAuthToken,
	socialAuthProvider,
	posAuthToken,
	privileged,
	errorMessage,
	errorType,
	errorProperties,
}: {
	userId?: InternalUserId;
	email?: string;
	ipAddress: string;
	socialAuthToken?: string;
	socialAuthProvider?: 'FACEBOOK' | 'APPLE' | 'GOOGLE';
	posAuthToken?: string;
	privileged?: boolean;
	errorMessage: string;
	errorType: cp.ErrorType | 'Expected' | 'SocialAuth';
	errorProperties?: { [key: string]: unknown };
}): Promise<
	| InternalServerError
	| ExpectedError
	| UserInputError
	| ForbiddenError
	| UserBlockedError
	| SocialAuthError
> => {
	await db.query(sql`
		INSERT INTO priv.p_failed_logins(
			user_id,
			email,
			social_auth_token,
			social_auth_provider,
			pos_auth_token,
			ip,
			privileged,
			timestamp,
			logs
		) VALUES (
			${userId},
			${email},
			${socialAuthToken},
			${socialAuthProvider},
			${posAuthToken}, 
			${ipAddress}, 
			${privileged},
			now(),
			${errorMessage}
		);
	`);
	let err;
	switch (errorType) {
		case 'Expected':
			return new ExpectedError(errorMessage, errorProperties);
		case 'Input':
			err = new UserInputError(errorMessage, errorProperties);
			// This is a temporary workaround. Formerly apollo server supplied the values on error.extensions.exception. Old App Versions rely on this. Fixed in app on 23.11.2022. Can be removed later
			err.extensions.exception = errorProperties;
			return err;
		case 'Forbidden':
			return new ForbiddenError(errorMessage);
		case 'Blocked':
			err = new UserBlockedError(errorMessage, errorProperties);
			// This is a temporary workaround. Formerly apollo server supplied the values on error.extensions.exception. Old App Versions rely on this. Fixed in app on 23.11.2022. Can be removed later
			err.extensions.exception = errorProperties;
		case 'SocialAuth':
			err = new SocialAuthError(errorMessage, socialAuthProvider);
			// This is a temporary workaround. Formerly apollo server supplied the values on error.extensions.exception. Old App Versions rely on this. Fixed in app on 23.11.2022. Can be removed later
			err.extensions.exception = errorProperties;
			return err;
		default:
			return new InternalServerError(errorMessage);
	}
};

/**
 * Used to check if the user has too many failed login attempts first to prevent leaking info through error codes like WRONG_CODE or WRONG_PASSWORD
 *
 * @param ipAddress - ipAddress from context.viewer.ipAddress
 */
export const checkRateLimit = async (ipAddress: string, rateLimit = RATE_LIMIT) => {
	const { count } = await db.queryOne(sql`
		SELECT count(*) FROM priv.p_failed_logins 
		WHERE ip=${ipAddress} AND "timestamp" > now()-interval '10 seconds'
	`);
	if (count > rateLimit) {
		throw new RateLimitError('Too many login attempts');
	}
};
