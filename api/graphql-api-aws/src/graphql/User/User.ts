import {
	adminForBonusPrograms,
	adminForCinemas,
	AuthenticationError,
	belongsToCinemaOperatingCompanies,
	db,
	decodePublicId,
	encodeDbId,
	encodeDbIdString,
	EntityNotFoundError,
	FeatureAccessLevel,
	ForbiddenError,
	getIsRootOrAdmin,
	getPrivileges,
	IdError,
	isAnonymousUser,
	sql,
	UserInputError,
} from '../../utils';

import {
	CinemaOperatingCompany,
	BonusProgram,
	BonusProgramMembership,
	Cinema,
	InterestRating,
	MovieList,
	Notification,
	NotificationPreference,
	ReviewRating,
	Subscription,
	VoucherInstance,
} from '..';
import { DEFAULT_APP_BRAND, ONLINE_TICKETING_LANGUAGES } from '../../../consts';
import {
	Context,
	I18N,
	InternalBonusProgramId,
	InternalUserId,
	InternalVoucherInstanceId,
	Language,
	PublicBonusProgramId,
	PublicCinemaId,
	PublicUserId,
	UserPrivileges,
	Viewer,
} from '../../typescript-types';
import { isAppVersionGreater } from '../../utils/authHelpers';
import { syncAllTickets } from '../ConfirmTicketPurchase/syncTicket';
import { askForAppFeedback } from '../ContentSectionList/AppFeedbackSection';
import { externalNewsletterPreferences } from '../ExternalNewsletterPreference/externalNewsletterPreference';
import { Invoice } from '../Invoice/Invoice';
import { getLinkedAccounts, linkedAccounts } from '../LinkedAccount/linkedAccounts';
import { InternalOrderId, Order } from '../Order/Order';
import { InternalTicketId, Ticket } from '../Ticket/Ticket';
import { forceSyncCineplexTickets, syncIfCineplexCustomer } from './cineplexCustomerService';
import { syncSubscriptions } from '../Subscription/syncSubscriptions';

const userQuery = `SELECT
id,
username AS name,
first_name || ' ' || last_name AS "fullName",
active,
email,
fb_id,
testing_status,
user_blocked_reason,
cp_website_token,
(data_usage_allowed IS NOT NULL) AS "dataUsageAllowed",
(marketing_emails_opt_in IS NOT NULL) AS "marketingEmailsAllowed",
skipped_registration as "skippedRegistration",
skipped_bonusprogram as "skippedBonusProgram",
zip_code AS "zipCode",
gender,
birth_date::text AS "birthDate",
first_name AS "firstName",
last_name AS "lastName",
street,
house_number AS "houseNumber",
city,
country,
telephone,
cp_last_sync AS "_cpLastSync",
cp_id AS "_cpId",
user_blocked_text as "blockedText",
block_app_change_until,
app_tracking_allowed as "appTrackingAllowed"
FROM priv.p_user
WHERE id = $1`;

export class User {
	id: PublicUserId;
	testingStatus: FeatureAccessLevel;
	_dbId: InternalUserId;
	_skippedBonusProgram: boolean;
	_skippedRegistration: boolean;
	_cpLastSync?: Date;
	_cpId?: string;
	anonymous: boolean;
	name?: string;
	fullName?: string;
	active?: boolean;
	email?: string;
	blocked?: boolean;
	blockedReason?: string;
	blockedText?: string;
	dataUsageAllowed?: boolean;
	marketingEmailsAllowed?: boolean;
	zipCode?: string;
	gender?: string;
	birthDate?: string;
	firstName?: string;
	lastName?: string;
	street?: string;
	houseNumber?: string;
	city?: string;
	country?: string;
	telephone?: string;
	onlineTicketingToken?: string;
	blockAppChangeUntil?: Date;
	appTrackingAllowed?: boolean;

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	constructor(data: any) {
		Object.assign(this, data);
	}
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	static encodeDbId(dbId: any) {
		return <PublicUserId>encodeDbId('User', <number>dbId);
	}
	static decodePublicId(id: PublicUserId) {
		return <InternalUserId>decodePublicId('User', <string>id);
	}
	static async gen(
		viewer: {
			hasUserId?: boolean;
			userId?: PublicUserId;
			isRoot?: boolean;
			isAdmin?: boolean;
			isCinemaAdmin?: boolean;
			isSupport?: boolean;
			isPrivileged?: boolean;
			authenticatedBonusProgramForPOS?: { bonusProgramIdDb?: InternalBonusProgramId };
			requestId?: string;
			clientVersion?: string;
		},
		id: PublicUserId
	) {
		const dbId = this.decodePublicId(id);
		if (!dbId) {
			throw new IdError('INVALID_USER_ID', 'User');
		}

		let res = await db.query(userQuery, [dbId]);
		if (res.rows.length === 0) {
			if (viewer.hasUserId && viewer.userId === id) {
				// this happens if the user was deletd via an integration with an external account service
				throw new AuthenticationError('USER_NOT_FOUND');
			} else {
				throw new EntityNotFoundError(`Unknown userid ${id}`, 'User');
			}
		}
		const dbUser = res.rows[0];
		if (dbUser._cpId) {
			if (
				viewer.clientVersion &&
				isAppVersionGreater(8, 0, 0, viewer) &&
				dbUser.cp_last_sync !== null
			) {
				console.log(
					'Not waiting',
					viewer.clientVersion,
					isAppVersionGreater(8, 0, 0, viewer),
					dbUser.cp_last_sync !== null
				);
				//On new app versions we do not wait to start the app until the data has loaded from tuffi & KK. The app will refetch the data in the background
				syncIfCineplexCustomer(dbId, false, viewer.requestId && (viewer as Viewer));
			} else {
				console.log(
					'Waiting',
					viewer.clientVersion,
					isAppVersionGreater(8, 0, 0, viewer),
					dbUser.cp_last_sync !== null
				);
				await syncIfCineplexCustomer(dbId, false, viewer.requestId && (viewer as Viewer));
				res = await db.query(userQuery, [dbId]);
			}
		}

		//Check authorization
		const userIsHimself = viewer.hasUserId && viewer.hasUserId && id === viewer.userId;
		// These are users controled by Cinuru / Cinfinity / Cineplex directly. Thus not the cinema admins

		let linkedUserIds = [];
		if (viewer.hasUserId) {
			// We must make suhre that we do not query this for service users (like POS users). Otherwise it will crash
			const { otherUserIds } = await getLinkedAccounts({
				userIdDb: User.decodePublicId(viewer.userId),
				acceptedOnly: true,
			});
			linkedUserIds = otherUserIds;
		}

		let authorized = userIsHimself || viewer.isPrivileged || linkedUserIds?.includes(id);

		if (!authorized) {
			//We do this if(!authorized) to prevent unnecessary queries here, as most often,
			// the user is already authorized at this point

			//Check if POS App + authorized
			const bpIdDb =
				viewer.authenticatedBonusProgramForPOS &&
				viewer.authenticatedBonusProgramForPOS.bonusProgramIdDb;
			if (bpIdDb) {
				if (
					(
						await db.query(
							`SELECT count(*)> 0 AS is_member
						FROM  priv.p_bonus_program_membership  
						WHERE user_id=$1 and bonus_program_id=$2`,
							[dbId, bpIdDb]
						)
					).rows[0].is_member
				) {
					authorized = true;
				}
			}
		}
		if (!authorized) {
			//TODO: Refactor this (in app, etc.) to ForbiddenError
			throw new ForbiddenError('NOT_AUTHORIZED');
		}
		const u = res.rows[0];
		const testingStatus: FeatureAccessLevel = u.testing_status
			? u.testing_status.toUpperCase()
			: 'PRODUCTION';

		const anonymous = await isAnonymousUser(u.id);

		if (userIsHimself || viewer.isPrivileged) {
			return new User({
				_dbId: u.id,
				id: id,
				name: u.name || undefined,
				active: u.active,
				email: u.email || undefined,
				testingStatus,
				blocked: !u.active,
				blockedReason: u.user_blocked_reason,
				blockedText: u.blockedText,
				dataUsageAllowed: u.dataUsageAllowed,
				marketingEmailsAllowed: u.marketingEmailsAllowed,
				zipCode: u.zipCode || undefined,
				gender: u.gender || undefined,
				birthDate: u.birthDate || undefined,
				firstName: u.firstName || undefined,
				lastName: u.lastName || undefined,
				fullName: u.fullName || undefined,
				street: u.street || undefined,
				houseNumber: u.houseNumber || undefined,
				city: u.city || undefined,
				country: u.country || undefined,
				telephone: u.telephone || undefined,
				onlineTicketingToken: u.cp_website_token,
				anonymous,
				_skippedRegistration: Boolean(u.skippedRegistration),
				_skippedBonusProgram: Boolean(u.skippedBonusProgram),
				_cpLastSync: u._cpLastSync,
				_cpId: u._cpId,
				blockAppChangeUntil: u.block_app_change_until,
				appTrackingAllowed: u.appTrackingAllowed,
			});
		} else {
			// is linked account
			return new User({
				_dbId: u.id,
				id: id,
				name: u.name || undefined,
				active: u.active,
				email: u.email || undefined,
				firstName: u.firstName || undefined,
				lastName: u.lastName || undefined,
				fullName: u.fullName || undefined,
				blocked: !u.active,
				blockedReason: u.user_blocked_reason,
				blockedText: u.blockedText,
			});
		}
	}

	async askToRegister(_: unknown, { language }: { language: Language }) {
		return (
			(language === 'de-cineplex' || language === 'de-cinfinity') &&
			this.anonymous &&
			!this._skippedRegistration
		);
	}
	async lastSync(_: unknown, { viewer }: { viewer: Viewer }) {
		if (viewer.language !== 'de-cineplex') {
			return new Date().toISOString();
		}
		if (this._cpLastSync) {
			return this._cpLastSync.toISOString();
		}
		if (!this._cpId) {
			return new Date().toISOString();
		}
		return new Date('2020-01-01').toISOString();
	}

	async askToJoinBonusProgram(_: unknown, { language }: { language: Language }) {
		if (language !== 'de-cineplex' || this.anonymous || this._skippedBonusProgram) {
			return false;
		}
		const res = await db.query(sql`
			SELECT bonus_program_id 
			FROM priv.p_bonus_program_membership 
			WHERE user_id=${this._dbId} AND active = TRUE 
		`);
		return res.rows.length === 0;
	}

	async selectedCinemas(_: unknown, { viewer, language }: { viewer: Viewer; language: Language }) {
		if (viewer.userId !== this.id) {
			return new ForbiddenError('Unauthorized');
		}

		const res = await db.query(
			sql`SELECT
					cinema_id
					FROM
					priv.p_user_cinema
					WHERE
					user_id = ${User.decodePublicId(this.id)}
					AND deselected_datetime IS NULL
					AND cinema_id IN(
						SELECT
							cinema_id FROM priv.p_cinema_app_brand
						WHERE
							"language" = ${language} OR ${language === null} IS TRUE)`
		);

		const ids = res.rows.map((r) => Cinema.encodeDbId(r.cinema_id));
		const cinemas = await Cinema.allCinemas(viewer, ids, language);
		return cinemas;
	}

	async cinemaCustomerships(
		args: { currentlySelectedOnly?: boolean },
		{ viewer, language }: { viewer: Viewer; language: Language }
	) {
		if (viewer.userId !== this.id) {
			return new ForbiddenError('Unauthorized');
		}

		let query =
			'SELECT cinema_id FROM priv.p_user_cinema WHERE user_id = $1 AND deselected_datetime IS NULL';
		if (args && args.currentlySelectedOnly === false) {
			// usually only return the currentlySelected cinemaCustomerships
			query = 'SELECT cinema_id FROM priv.p_user_cinema WHERE user_id = $1';
		}
		const res = await db.query(query, [User.decodePublicId(this.id)]);
		const ids = res.rows.map((r) => Cinema.encodeDbId(r.cinema_id));
		const cinemas = await Cinema.allCinemas(viewer, ids, language);
		return cinemas.map((cinema) => {
			return {
				id: encodeDbIdString(
					'CinemaCustomership',
					User.decodePublicId(this.id) + 'c' + Cinema.decodePublicId(cinema.id)
				),
				cinema: cinema,
				user: this,
			};
		});
	}
	async bonusProgramMemberships(
		_: unknown,
		{ viewer, language }: { viewer: Viewer; language: Language }
	) {
		return await BonusProgramMembership.allMembershipsByUserAndOrBonusProgram(
			viewer,
			this.id,
			null,
			language
		);
	}
	async bonusProgramMembership(
		{
			bonusProgramId,
			cinemaId,
		}: { bonusProgramId?: PublicBonusProgramId; cinemaId?: PublicCinemaId },
		{ viewer, language }: { viewer: Viewer; language: Language }
	) {
		let bpIdDb: InternalBonusProgramId;
		if (bonusProgramId) {
			bpIdDb = BonusProgram.decodePublicId(bonusProgramId);
		} else {
			if (!cinemaId) {
				if (viewer.hasPosApp && viewer.posApp.cinemaId) {
					//If no cinemaId is provided try to use the one from auth token
					cinemaId = viewer.posApp.cinemaId;
				} else {
					return new UserInputError('Please specify either bonusProgramId or cinemaId');
				}
			}
			const cinemaIdDb = Cinema.decodePublicId(cinemaId);

			const cRes = await db.query('SELECT bonus_currency_id FROM priv.p_cinema WHERE id = $1', [
				cinemaIdDb,
			]);
			if (!cRes.rows.length || !cRes.rows[0].bonus_currency_id) {
				return new EntityNotFoundError('Cinema does not have a Bonusprogram');
			}
			bpIdDb = cRes.rows[0].bonus_currency_id;
		}
		return (
			await BonusProgramMembership.allMembershipsByUserAndOrBonusProgram(
				viewer,
				this.id,
				BonusProgram.encodeDbId(bpIdDb),
				language
			)
		)[0];
	}
	async interestRatings(
		{ removeReviewRatedMovies = true }: { removeReviewRatedMovies: boolean },
		{ viewer }: { viewer: Viewer }
	) {
		let query =
			'SELECT movie_id FROM priv.p_movie_interest WHERE user_id = $1 AND valid = true ORDER BY datetime DESC';
		if (removeReviewRatedMovies) {
			query =
				'SELECT movie_id FROM priv.p_movie_interest WHERE user_id = $1 AND valid = true AND movie_id NOT IN (SELECT movie_id FROM priv.p_rating_movie WHERE user_id = $1 AND valid = true) ORDER BY datetime DESC';
		}

		const res = await db.query(query, [User.decodePublicId(this.id)]);
		const ids = res.rows.map((row) =>
			InterestRating.encodeDbId(User.decodePublicId(this.id), row.movie_id)
		);
		return InterestRating.genMult(viewer, ids);
	}

	async watchlist(
		{ cinemaIds, lastChance }: { cinemaIds?: PublicCinemaId[]; lastChance?: boolean },
		{ viewer }: { viewer: Viewer },
		{ language }: { language: Language }
	) {
		if (viewer.userId !== this.id) {
			return new ForbiddenError('Unauthorized');
		}
		if (cinemaIds) {
			if (lastChance) {
				// cinema ids and last chance are set, return watchlistlastchance
				return await MovieList.gen(
					viewer,
					MovieList.encodeDbId(16, cinemaIds.map(Cinema.decodePublicId)),
					language
				);
			} else {
				// cinema ids are set, return watchlistincinema
				return await MovieList.gen(
					viewer,
					MovieList.encodeDbId(15, cinemaIds.map(Cinema.decodePublicId)),
					language
				);
			}
		}
		// no cinema ids are set, return full watchlist
		return await MovieList.gen(viewer, MovieList.encodeDbId(1, []), language);
	}
	async ratedlist(
		_: unknown,
		{ viewer }: { viewer: Viewer },
		{ language }: { language: Language }
	) {
		if (viewer.userId !== this.id) {
			return new ForbiddenError('Unauthorized');
		}
		return await MovieList.gen(viewer, MovieList.encodeDbId(2, []), language);
	}
	async seenInCinemaList(_, { viewer }: { viewer: Viewer }, { language }: { language: Language }) {
		return await MovieList.gen(viewer, MovieList.encodeDbId(3, []), language);
	}

	async termsStatus(_: unknown, { i18n, language }: { i18n: I18N; language: Language }) {
		const currentVersion = i18n.t('Global.termsVersion');

		const query =
			'SELECT max(terms_version) as "acceptedVersion" FROM priv.p_privacy_terms_accepted_by_user WHERE user_id = $1 AND language = $2';
		const res = await db.query(query, [User.decodePublicId(this.id), language]);
		const acceptedVersion = res.rows[0].acceptedVersion;

		if (acceptedVersion === null || currentVersion > acceptedVersion) {
			return {
				latestTermsAgreed: false,
				alertTitle: i18n.t('TermsAgreementView.title'),
				alertDescription: i18n.t('TermsAgreementView.description', {
					// if terms were accepted once, we must be here because newer terms were not accepted, therefore show the update version of the alertDescription
					context: acceptedVersion ? 'update' : null,
				}),
				alertButtonLabel: i18n.t('TermsAgreementView.agreementButtonLabel'),
			};
		}
		return {
			latestTermsAgreed: true,
		};
	}
	async reviewRatings(_: unknown, { viewer }: { viewer: Viewer }) {
		if (viewer.userId !== this.id) {
			return new ForbiddenError('Unauthorized');
		}
		const res = await db.query(
			'SELECT movie_id FROM priv.p_rating_movie WHERE user_id = $1 AND valid = true ORDER BY datetime DESC',
			[User.decodePublicId(this.id)]
		);
		const ids = res.rows.map((row) =>
			ReviewRating.encodeDbId(User.decodePublicId(this.id), row.movie_id)
		);
		return ReviewRating.genMult(viewer, ids);
	}
	async notificationPreferences(_: unknown, { viewer, i18n }: { viewer: Viewer; i18n: I18N }) {
		if (viewer.userId !== this.id) {
			return new ForbiddenError('Unauthorized');
		}
		const prefs = await NotificationPreference.genMult(undefined, viewer, i18n);
		return prefs.filter((p) => p);
	}
	externalNewsletterPreferences = externalNewsletterPreferences;
	async privileges(
		_: unknown,
		{ viewer, language }: { viewer: Viewer; language: Language }
	): Promise<UserPrivileges> {
		const _belongsToCinemaOperatingCompanies = async () => {
			let ids = [];
			if (viewer.userId === this.id) {
				// For the current user (the standard case, it is much faster to rely on the viewer token than to regenrate everything)
				ids = await viewer.belongsToCinemaOperatingCompanies();
			} else {
				ids = await belongsToCinemaOperatingCompanies(this.id);
			}
			return await CinemaOperatingCompany.genMult(viewer, ids, true);
		};
		const _adminForCinemas = async () => {
			let ids = [];
			if (viewer.userId === this.id) {
				// For the current user (the standard case, it is much faster to rely on the viewer token than to regenrate everything)
				ids = await viewer.adminForCinemas();
			} else {
				ids = await adminForCinemas(this.id);
			}
			return await Cinema.allCinemas(viewer, ids, language);
		};
		const _adminForBonusPrograms = async () => {
			let ids = [];
			if (viewer.userId === this.id) {
				// For the current user (the standard case, it is much faster to rely on the viewer token than to regenrate everything)
				ids = await viewer.adminForBonusPrograms();
			} else {
				ids = await adminForBonusPrograms(this.id);
			}
			return await Promise.all(ids.map((id) => BonusProgram.gen(viewer, id)));
		};

		let isRoot = false,
			isAdmin = false,
			isSupport = false;

		if (viewer.userId === this.id) {
			// For the current user (the standard case, it is much faster to rely on the viewer token than to regenrate everything)
			isRoot = viewer.isRoot;
			isAdmin = viewer.isAdmin;
			isSupport = viewer.isSupport;
		} else {
			const privileges = await getIsRootOrAdmin(this.id);
			isRoot = privileges.root;
			isAdmin = privileges.admin;
			isSupport = privileges.support;
		}
		let privileges;
		if (viewer.userId === this.id) {
			// For the current user (the standard case, it is much faster to rely on the viewer token than to regenrate everything)
			privileges = await viewer.privileges();
		} else {
			privileges = await getPrivileges(this.id, isRoot, isAdmin);
		}
		return {
			belongsToCinemaOperatingCompanies: _belongsToCinemaOperatingCompanies,
			adminForCinemas: _adminForCinemas,
			adminForBonusPrograms: _adminForBonusPrograms,
			...privileges,
			adminRole: isAdmin,
			rootRole: isRoot,
			supportRole: isSupport,
		};
	}
	async notifications(_: unknown, { viewer, language }: { viewer: Viewer; language: Language }) {
		if (viewer.userId !== this.id) {
			return new ForbiddenError('Unauthorized');
		}
		return await Notification.genByUser(viewer, this.id, language);
	}

	async askForAppFeedback(_: unknown, { viewer }: { viewer: Viewer }) {
		if (viewer.userId !== this.id) {
			return new ForbiddenError('Unauthorized');
		}
		//Deprecated, replaced by AppRatingSection

		return await askForAppFeedback(viewer.userIdDb);
	}

	async tickets(
		{
			cinemaIds,
			openOnly,
			sync,
		}: { cinemaIds?: PublicCinemaId[]; openOnly?: boolean; sync?: boolean },
		{ viewer }: { viewer: Viewer }
	) {
		if (
			DEFAULT_APP_BRAND !== 'CINFINITY' &&
			DEFAULT_APP_BRAND !== 'CINEPLEX' &&
			viewer.language !== 'de-lumos'
		) {
			return [];
		}

		if (viewer.userId !== this.id && !viewer.isPrivileged) {
			return new ForbiddenError('Unauthorized');
		}

		if (viewer.language === 'de-cineplex') {
			if (sync) {
				await forceSyncCineplexTickets(viewer.userIdDb);
			} else {
				forceSyncCineplexTickets(viewer.userIdDb);
			}
		} else if (
			viewer.language === 'de-cinfinity' &&
			ONLINE_TICKETING_LANGUAGES.includes(viewer.language)
		) {
			await syncAllTickets({
				userIdDb: viewer.userIdDb,
			});
		}

		const res = await db.query(sql`
			SELECT 
				t.id
			FROM 
				priv.p_ticket t
			WHERE 
				user_id=${this._dbId}
				AND (${!cinemaIds} OR cinema_id = ANY(${(cinemaIds || []).map(Cinema.decodePublicId)}))
				AND (${!openOnly} OR screening_datetime > now() - interval '90 minutes')
				AND (${!openOnly} OR t.status != 'REFUNDED')
			ORDER BY screening_datetime DESC
		`);

		return await Ticket.genMult(
			viewer,
			res.rows.map((r) => Ticket.encodeDbId(r.id as InternalTicketId))
		);
	}
	async orders(
		{
			cinemaIds,
			openOnly,
			sync,
		}: { cinemaIds?: PublicCinemaId[]; openOnly?: boolean; sync?: boolean },
		{ viewer }: { viewer: Viewer }
	) {
		if (
			DEFAULT_APP_BRAND !== 'CINFINITY' &&
			DEFAULT_APP_BRAND !== 'CINEPLEX' &&
			viewer.language !== 'de-lumos'
		) {
			return [];
		}

		if (viewer.userId !== this.id && !viewer.isPrivileged) {
			return new ForbiddenError('Unauthorized');
		}
		if (viewer.language === 'de-cineplex') {
			if (sync) {
				await forceSyncCineplexTickets(viewer.userIdDb);
			} else {
				forceSyncCineplexTickets(viewer.userIdDb);
			}
		}

		const res = await db.query(sql`
			SELECT 
				id
			FROM 
				priv.p_order
			WHERE 
				user_id=${this._dbId}
				AND (${!cinemaIds} OR cinema_id = ANY(${(cinemaIds || []).map(Cinema.decodePublicId)}))
				AND (${!openOnly} OR datetime > now() - interval '90 minutes')
				AND (${!openOnly} OR status != 'REFUNDED')
			ORDER BY datetime DESC
		`);

		return await Order.genMult(
			viewer,
			res.rows.map((r) => Order.encodeDbId(r.id as InternalOrderId))
		);
	}
	async subscriptions(_: unknown, { viewer }: { viewer: Viewer }) {
		if (DEFAULT_APP_BRAND !== 'CINFINITY') {
			return [];
		}

		if (viewer.userId !== this.id && !viewer.isPrivileged) {
			return new ForbiddenError('Unauthorized');
		}
		await syncSubscriptions({ userId: this._dbId });
		const ids = (
			await db.query(sql`SELECT id FROM priv.p_subscription WHERE user_id = ${this._dbId}`)
		).rows.map(({ id }) => Subscription.encodeDbId(id));
		if (!ids.length) {
			return [];
		}
		return await Subscription.genMult(ids);
	}
	async invoices(_: unknown, { viewer }: { viewer: Viewer }) {
		if (DEFAULT_APP_BRAND !== 'CINFINITY') {
			return [];
		}

		if (viewer.userId !== this.id && !viewer.isPrivileged) {
			return new ForbiddenError('Unauthorized');
		}

		const invoiceIds = (
			await db.query(
				sql`
				SELECT id FROM priv.p_invoice
				WHERE user_id = ${this._dbId} 
				AND pdf_data IS NOT NULL 
			`
			)
		).rows.map(({ id }) => Invoice.encodeDbId(id));

		const result = await Invoice.genMult(invoiceIds, viewer);
		return result;
	}

	async linkedAccounts(_: unknown, context: Context) {
		return await linkedAccounts(this._dbId, context);
	}

	async vouchers(
		{ onlyRedeemed = true }: { onlyRedeemed?: boolean },
		{ viewer }: { viewer: Viewer }
	) {
		if (viewer.userId !== this.id && !(viewer.isAdmin || viewer.isRoot)) {
			return new ForbiddenError('Unauthorized');
		}
		const res = await db.query(
			onlyRedeemed
				? sql`
					SELECT 
						id
					FROM 
						priv.p_voucher
					WHERE 
						user_id=${this._dbId}
					AND 
						redeemed_datetime IS NOT NULL
					ORDER 
						BY redeemed_datetime DESC
				`
				: sql`
					SELECT 
						id
					FROM 
						priv.p_voucher
					WHERE 
						user_id=${this._dbId}
					ORDER BY 
						redeemed_datetime IS NULL ASC,
						redeemed_datetime DESC   
				`
		);

		return await VoucherInstance.genMult(
			viewer,
			res.rows.map((r) => VoucherInstance.encodeDbId(r.id as InternalVoucherInstanceId)),
			viewer.language
		);
	}
}
