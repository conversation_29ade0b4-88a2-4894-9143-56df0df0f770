import { db, sendEmail, UserInputError, ForbiddenError, sql } from '../../utils';
import { isSameDay } from 'date-fns';
import { CinemaOperatingCompany, Cinema, Subscription, User } from '..';
import {
	I18N,
	Context,
	PublicUserId,
	Gender,
	PublicSubscriptionId,
	PublicCinemaOperatingCompanyId,
} from '../../typescript-types';
import * as cp from './cineplexKrankikomCustomerService';
import { renderEmailTemplate } from '@cinuru/emails';
import lodash from 'lodash';

export const logUserEdit = async ({
	editorUserId,
	userId,
	relation,
	changedColumn,
	oldValue,
	newValue,
}: {
	editorUserId: PublicUserId;
	userId: PublicUserId;
	relation: string;
	changedColumn: string;
	oldValue: string;
	newValue: string;
}) => {
	const userDbId = User.decodePublicId(userId);
	const editorUserDbId = User.decodePublicId(editorUserId);

	await db.query(sql`
		INSERT INTO priv.p_user_edit
		(editor_user_id, user_id, relation, changed_column, old_value, new_value, datetime)
		VALUES
		(${editorUserDbId}, ${userDbId}, ${relation}, ${changedColumn}, ${oldValue}, ${newValue}, now())
	`);
};

/*
This mutation is used by admin users to update user data. 
For users to update their own info there is a mutation called updateUserProfile.
*/

export const updateUser = async (
	_: unknown,
	{
		userId,
		blocked,
		blockedText,
		firstname,
		lastname,
		street,
		houseNumber,
		zipCode,
		city,
		email,
		adminCinemaOperatingCompanyIds,
		resetAppChangeBlockedUntil,
	}: {
		userId: PublicUserId;
		blocked?: boolean;
		blockedText?: string;
		firstname?: string;
		lastname?: string;
		street?: string;
		houseNumber?: string;
		zipCode?: string;
		city?: string;
		email?: string;
		adminCinemaOperatingCompanyIds?: PublicCinemaOperatingCompanyId[];
		resetAppChangeBlockedUntil?: boolean;
	},
	context: Context
) => {
	if (!context.viewer.isPrivileged) {
		throw new ForbiddenError('Only Admins and Supporters are allowed to call this');
	}

	if (blocked === true && !blockedText) {
		throw new UserInputError('Blocked text is required');
	}

	if (blocked === false && blockedText) {
		throw new UserInputError('Blocked text is not allowed');
	}

	const userBefore = await User.gen(context.viewer, userId);

	if (!userBefore) {
		throw new UserInputError('User not found');
	}

	if (blocked === true && userBefore.blocked) {
		throw new UserInputError('User is already blocked');
	}

	if (blocked === false) {
		if (!userBefore.blocked) {
			throw new UserInputError('User is already unblocked');
		}
		if (userBefore.blockedReason !== 'OTHER') {
			throw new UserInputError(
				'User is already blocked by another reason and cannot be unblocked by this endpoint'
			);
		}
	}

	const updatedBlockedReason =
		blocked !== undefined ? (blocked === true ? 'OTHER' : null) : userBefore.blockedReason;
	const updatedBlockedText =
		blocked !== undefined ? (blocked === true ? blockedText : null) : userBefore.blockedText;
	const updatedFirstname = firstname || userBefore.firstName;
	const updatedLastname = lastname || userBefore.lastName;
	const updatedActive = blocked !== undefined ? !blocked : userBefore.active;
	const updatedStreet = street || userBefore.street;
	const updatedHouseNumber = houseNumber || userBefore.houseNumber;
	const updatedZipCode = zipCode || userBefore.zipCode;
	const updatedCity = city || userBefore.city;
	const updatedEmail = email || userBefore.email;
	const appChangeBlockedUntil = resetAppChangeBlockedUntil ? null : userBefore.blockAppChangeUntil;

	const changedColumns = [
		{
			column: 'user_blocked_reason',
			oldValue: `${userBefore.blockedReason}`,
			newValue: `${updatedBlockedReason}`,
		},
		{
			column: 'user_blocked_text',
			oldValue: `${userBefore.blockedText}`,
			newValue: `${updatedBlockedText}`,
		},
		{
			column: 'first_name',
			oldValue: `${userBefore.firstName}`,
			newValue: `${updatedFirstname}`,
		},
		{
			column: 'last_name',
			oldValue: `${userBefore.lastName}`,
			newValue: `${updatedLastname}`,
		},
		{
			column: 'active',
			oldValue: `${userBefore.active}`,
			newValue: `${updatedActive}`,
		},
		{
			column: 'street',
			oldValue: `${userBefore.street}`,
			newValue: `${updatedStreet}`,
		},
		{
			column: 'house_number',
			oldValue: `${userBefore.houseNumber}`,
			newValue: `${updatedHouseNumber}`,
		},
		{
			column: 'zip_code',
			oldValue: `${userBefore.zipCode}`,
			newValue: `${updatedZipCode}`,
		},
		{
			column: 'city',
			oldValue: `${userBefore.city}`,
			newValue: `${updatedCity}`,
		},
		{
			column: 'email',
			oldValue: `${userBefore.email}`,
			newValue: `${updatedEmail}`,
		},
		{
			column: 'app_change_blocked_until',
			oldValue: `${userBefore.blockAppChangeUntil}`,
			newValue: `${appChangeBlockedUntil}`,
		},
	].filter(({ oldValue, newValue }) => oldValue !== newValue);

	await Promise.all(
		changedColumns.map(({ column, oldValue, newValue }) =>
			logUserEdit({
				editorUserId: context.viewer.userId,
				userId,
				relation: 'user',
				changedColumn: column,
				oldValue,
				newValue,
			})
		)
	);

	await db.queryOne(sql`
			UPDATE priv.p_user SET 
			user_blocked_reason = ${updatedBlockedReason},
			user_blocked_text = ${updatedBlockedText},
			active = ${updatedActive},
			last_name = ${updatedLastname},
			first_name = ${updatedFirstname},
			street = ${updatedStreet},
			house_number = ${updatedHouseNumber},
			zip_code = ${updatedZipCode},
			city = ${updatedCity},
			email = ${updatedEmail},
			block_app_change_until = ${appChangeBlockedUntil}
			WHERE id = ${User.decodePublicId(userId)}
		`);

	if (adminCinemaOperatingCompanyIds) {
		if (!context.viewer.isRoot && !context.viewer.isAdmin) {
			throw new ForbiddenError(
				'Only Root or Admin users are allowed to set cinema operating companies'
			);
		}
		const privilegesBefore = await userBefore.privileges(_, {
			viewer: context.viewer,
			language: context.language,
		});

		const userIdDb = User.decodePublicId(userId);
		const cinemaOperatingCompanyIdsBefore = (
			await privilegesBefore.belongsToCinemaOperatingCompanies()
		).map((c) => c.id);
		const adminForCinemaOperatingCompanysChanged = !lodash.isEqual(
			cinemaOperatingCompanyIdsBefore,
			adminCinemaOperatingCompanyIds
		);

		if (adminForCinemaOperatingCompanysChanged) {
			const removedCinemaOperatingCompanys = lodash.difference(
				cinemaOperatingCompanyIdsBefore,
				adminCinemaOperatingCompanyIds
			);
			const addedCinemaOperatingCompanys = lodash.difference(
				adminCinemaOperatingCompanyIds,
				cinemaOperatingCompanyIdsBefore
			);

			await logUserEdit({
				editorUserId: context.viewer.userId,
				userId,
				relation: 'user_privilege',
				changedColumn: 'multiple',
				oldValue: JSON.stringify(cinemaOperatingCompanyIdsBefore.sort()),
				newValue: JSON.stringify(adminCinemaOperatingCompanyIds.sort()),
			});

			if (removedCinemaOperatingCompanys.length) {
				await db.query(
					sql`DELETE FROM priv.p_user_privilege WHERE user_id = ${userIdDb} AND cinema_operating_company_id = ANY(${removedCinemaOperatingCompanys.map(
						(c) => CinemaOperatingCompany.decodePublicId(c)
					)})`
				);
			}
			if (addedCinemaOperatingCompanys.length) {
				await db.query(
					`
						INSERT INTO priv.p_user_privilege (user_id, privilege, cinema_operating_company_id )
						VALUES 
						${addedCinemaOperatingCompanys.map((_, index) => `($1, $2, $${index + 3})`)}
					`,
					[
						userIdDb,
						'CINEMA_ADMIN',
						...addedCinemaOperatingCompanys.map((c) => CinemaOperatingCompany.decodePublicId(c)),
					]
				);
			}
		}
	}

	return await User.gen(context.viewer, userId);
};

export const updateUserSubscription = async (
	_: unknown,
	{
		id,
		blocked,
		blockedText,
	}: {
		id: PublicSubscriptionId;
		blocked?: boolean;
		blockedText?: string;
	},
	context: Context
) => {
	if (!context.viewer.isPrivileged) {
		throw new ForbiddenError('Only Cinuru or Cinfinity Admins are allowed to call this');
	}

	if (blocked === true && !blockedText) {
		throw new UserInputError('Blocked text is required');
	}

	if (blocked === false && blockedText) {
		throw new UserInputError('Blocked text is not allowed');
	}

	const subscriptionBefore = await Subscription.gen(id);

	if (!subscriptionBefore) {
		throw new UserInputError('Subscription not found');
	}

	if (subscriptionBefore.blocked && blocked === true) {
		throw new UserInputError('Subscription is already blocked');
	}

	if (blocked === false) {
		if (!subscriptionBefore.blocked) {
			throw new UserInputError('Subscription is already unblocked');
		}
		if (subscriptionBefore.blockedReason !== 'OTHER') {
			throw new UserInputError(
				'Subscription is already blocked by another reason and cannot be unblocked by this endpoint'
			);
		}
	}

	await db.queryOne(sql`
			UPDATE priv.p_subscription SET 
			blocked = ${blocked || null},
			blocked_reason = ${blocked ? 'OTHER' : null},
			blocked_text = ${blocked ? blockedText : null}, 
			updated_at = now()
			WHERE id = ${Subscription.decodePublicId(id)}
		`);

	const updatedSubscription = await Subscription.gen(id);

	const changedColumns = [
		{
			column: 'blocked',
			oldValue: `${subscriptionBefore.blocked}`,
			newValue: `${updatedSubscription.blocked}`,
		},
		{
			column: 'blocked_reason',
			oldValue: `${subscriptionBefore.blockedReason}`,
			newValue: `${updatedSubscription.blockedReason}`,
		},
		{
			column: 'blocked_text',
			oldValue: `${await subscriptionBefore.blockedText(_, context)}`,
			newValue: `${await updatedSubscription.blockedText(_, context)}`,
		},
	].filter(({ oldValue, newValue }) => oldValue !== newValue);

	await Promise.all(
		changedColumns.map(({ column, oldValue, newValue }) =>
			logUserEdit({
				editorUserId: context.viewer.userId,
				userId: User.encodeDbId(subscriptionBefore._userIdDb),
				relation: 'subscription',
				changedColumn: column,
				oldValue,
				newValue,
			})
		)
	);

	return updatedSubscription;
};

export const updateUserProfile = async (
	_: unknown,
	{
		name,
		firstName,
		lastName,
		street,
		houseNumber,
		zipCode,
		city,
		country,
		gender,
		telephone,
		birthDate,
	}: {
		name?: string;
		firstName?: string;
		lastName?: string;
		street?: string;
		houseNumber?: string;
		zipCode?: string;
		city?: string;
		country?: string;
		gender?: Gender;
		telephone?: string;
		birthDate?: Date;
	},
	context: Context
) => {
	if (await context.viewer.isAnonymous()) {
		return new ForbiddenError('PROFILE_UPDATE_REQUIRES_REGISTRATION');
	}

	if (context.language === 'de-cineplex' || context.language === 'de-cinfinity') {
		if (firstName === '') return new UserInputError('FIRST_NAME_MISSING');
		if (lastName === '') return new UserInputError('LAST_NAME_MISSING');
	} else {
		if (!name) {
			return new UserInputError('NAME_MISSING');
		}
	}

	//synchronize with cineplex api
	if (context.language === 'de-cineplex') {
		const {
			cp_access_token: cpAccessToken,
			birth_date: currBirthDateString,
		} = await db.queryOne(sql`
			SELECT cp_access_token, birth_date::text FROM priv.p_user WHERE id=${context.viewer.userIdDb}
		`);
		if (!cpAccessToken) {
			return new ForbiddenError('PROFILE_UPDATE_REQUIRES_REGISTRATION');
		}
		// because of the birthday voucher the user is not allowed to change his birthday
		// because birthDate is parsed wrongly with new Date, we must not use dateFns.parse here
		if (currBirthDateString && !isSameDay(birthDate, new Date(currBirthDateString))) {
			return new UserInputError('BIRTH_DATE_CANNOT_BE_CHANGED');
		}
		await cp.updateProfile(
			{
				cpAccessToken,
				firstName,
				lastName,
				street,
				houseNumber,
				zipCode,
				city,
				country,
				gender: gender as Gender,
				telephone,
				birthDate,
			},
			context
		);
		await db.query(sql`
			UPDATE priv.p_user SET cp_last_sync=NULL WHERE id=${context.viewer.userIdDb}
		`);
	}

	/* TODO remove CP_COUNTRY_VALUES_REVERSE mapping after 01.01.2026 - Cineplex changed from GERMANY to DE */
	const countryMapped =
		context.language !== 'de-cineplex' ? country : cp.CP_COUNTRY_VALUES_REVERSE[country] || country;

	const args = {
		username: name,
		first_name: firstName,
		last_name: lastName,
		street: street,
		house_number: houseNumber,
		zip_code: zipCode,
		city: city,
		country: countryMapped,
		gender: gender,
		telephone: telephone,
		birth_date: birthDate && birthDate.toISOString(),
	};

	await Promise.all(
		Object.keys(args).map(async (key) => {
			const value = args[key];
			if (value === null) {
				await db.query(`UPDATE priv.p_user SET ${key} = NULL WHERE id=$1`, [
					context.viewer.userIdDb,
				]);
			}
			if (value) {
				await db.query(`UPDATE priv.p_user SET ${key} = $2 WHERE id=$1`, [
					context.viewer.userIdDb,
					args[key],
				]);
			}
		})
	);

	return { user: async () => await User.gen(context.viewer, context.viewer.userId) };
};

export const updateEmail = async (_: unknown, { email }: { email: string }, context: Context) => {
	email = email.toLocaleLowerCase();
	if (await context.viewer.isAnonymous()) {
		return new ForbiddenError('EMAIL_UPDATE_REQUIRES_REGISTRATION');
	}
	if (context.language === 'de-cineplex') {
		throw new Error('endpoint not supported for cineplex, you must have an old app version');
	}

	const emailAlreadyTaken = await db.query(
		`SELECT DISTINCT language
			FROM priv.p_user u JOIN priv.p_app a ON a.user_id = u.id
		WHERE email=$1`,
		[(email || '').toLowerCase()]
	);
	if (emailAlreadyTaken.rows.length > 0) {
		// email already exists, abort and let user know in which brand email exists
		const accountBrands = emailAlreadyTaken.rows.map((u: { language?: string }) => {
			if (!u.language) return 'CINURU';
			const [_, brand] = u.language.split('-');
			return brand ? brand.toUpperCase() : 'CINURU';
		});
		return new UserInputError('DUPLICATE_EMAIL', { accountBrands });
	}

	const { username } = await db.queryOne(sql`
		SELECT username FROM priv.p_user WHERE id=${context.viewer.userIdDb}
	`);
	const emailConfirmToken = (await db.query('SELECT random_text_md5(70)', [])).rows[0]
		.random_text_md5;
	const ableToSendEmailConfirmation = await sendEmailConfirmation({
		username,
		email,
		isUpdate: true,
		token: emailConfirmToken,
		i18n: context.i18n,
	});
	if (!ableToSendEmailConfirmation) {
		// couldn't sent email confirmation, abort
		//sending emails is deactivated for testing - so it is not possible to test this error
		return new UserInputError('UNREACHABLE_EMAIL_ADDRESS');
	}

	// user is registered and confirmation email has been sent, upgrade email
	const res2 = await db.query(
		`UPDATE priv.p_user SET 
			email=$2,
			email_confirmed=false,
			email_reminder_sent=false,
			welcome_email_sent=true,
			email_confirm_token=$3,
			email_changed_datetime=now()
		WHERE id=$1 RETURNING id`,
		[context.viewer.userIdDb, (email || '').toLowerCase(), emailConfirmToken]
	);
	if (res2.rows.length === 1) {
		return { user: async () => await User.gen(context.viewer, context.viewer.userId) };
	}
};

/**
 * @deprecated use requestPasswordReset instead
 */
export const sendResetPasswordEmail = async (
	_: unknown,
	{ email }: { email: string },
	context: Context
) => {
	if (context.language === 'de-cineplex') {
		throw new Error('endpoint not supported for cineplex, you must have an old app version');
	}

	const i18n = context.i18n;
	const result = await db.query(
		//Remove password_reset_email_sent=true once old account endpoint is removed
		`Update priv.p_user SET
			password_reset_token = (SELECT random_text_md5(40)),
			password_reset_request_datetime = now(),
			password_reset_email_sent = true
		WHERE email =$1
		RETURNING id, username, email, password_reset_token`,
		[email.toLowerCase()]
	);
	if (!result.rows.length) {
		return new UserInputError('WRONG_EMAIL_OR_ID');
	}

	const user = result.rows[0];

	const userId = User.encodeDbId(user.id);
	const token = user.password_reset_token;
	let name = user.username;
	if (!name) {
		name = i18n.t('Global.defaultUserName');
	}
	const mailOptions = {
		from: `"${i18n.t('Global.appName')}" <${i18n.t('Global.transactionalOutboundMail')}>`,
		to: user.email,
		...renderEmailTemplate({
			template: 'ResetPassword',
			name,
			href: `https://cinuru.com/set-new-password?token=${token}`,
		}),
	};

	try {
		await sendEmail(mailOptions);
		return { success: true, user: await User.gen({ userId, hasUserId: true }, userId) };
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (e) {
		return { success: false };
	}
};

/**
 * @deprecated use requestEmailChange instead
 */
const sendEmailConfirmation = async ({
	username,
	email,
	token,
	isUpdate,
	i18n,
}: {
	username: string;
	email: string;
	token?: string;
	code?: string;
	isUpdate?: boolean;
	i18n: I18N;
}) => {
	const name = username || i18n.t('Global.defaultUserName');
	const mailOptions = {
		from: `"${i18n.t('Global.appName')}" <${i18n.t('Global.transactionalOutboundMail')}>`,
		to: email,
		...renderEmailTemplate({
			template: isUpdate ? 'ConfirmEmailChange' : 'ConfirmRegistration',
			name,
			href: `https://cinuru.com/verify-email?token=${token}`,
		}),
	};
	const res = await sendEmail(mailOptions);
	if (!res.success) {
		return false;
	}
	return true;
};

export const resendActivationEmail = async (
	_: unknown,
	{ email, userId, updating }: { email?: string; userId?: PublicUserId; updating?: boolean },
	context: Context
) => {
	// this can eitherbe used during the initial verification of the email during registration or when an email change was requested (updating=true)
	const { i18n, language } = context;
	if (!email && !userId) {
		return new UserInputError('NEITHER_EMAIL_NOR_ID_PROVIDED');
	}
	if (email && userId) {
		return new UserInputError('ONLY_EMAIL_OR_USER_ID_ALLOWED');
	}
	email = email ? email.toLowerCase() : null;
	let userIdDb = userId ? User.decodePublicId(userId) : null;
	if (updating) {
		userIdDb = context.viewer.userIdDb;
	}
	const user = await db.queryOne(sql`
		SELECT username, email, email_confirm_token as token, email_confirmed, cp_access_token as "cpAccessToken"
		FROM priv.p_user 
		WHERE 
			(id=${userIdDb} OR ${Boolean(userIdDb)} = FALSE) AND
			(email=${(email || '').toLowerCase()} OR ${!updating && Boolean(email)} = FALSE)
	`);

	if (language === 'de-cineplex') {
		throw new Error('endpoint not supported for cineplex, you must have an old app version');
	} else {
		if (!user) {
			return new UserInputError('WRONG_EMAIL_OR_ID');
		}
		if (user.email_confirmed) {
			return new UserInputError('EMAIL_ALREADY_CONFIRMED');
		}
		const emailSendingSuccess = await sendEmailConfirmation({ ...user, i18n, isUpdate: updating });
		return { success: emailSendingSuccess, sentToEmail: emailSendingSuccess ? user.email : null };
	}
};

export const setInitialRatedMovies = async (
	_: unknown,
	__: unknown,
	context: Context
): Promise<{
	success: true;
}> => {
	await db.query(sql`
	UPDATE 
		priv.p_user
	SET 
		initial_rated_movies=true 
	WHERE 
		id=${context.viewer.userIdDb};
	`);

	return {
		success: true,
	};
};
