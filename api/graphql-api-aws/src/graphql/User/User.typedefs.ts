import { gql } from 'apollo-server-core';

export const typeDef = gql`
	extend type Query {
		currentUser: User
		currentPOS: PosApp
		userByQr(qrCode: String!): User
		userById(id: ID!): User
		adminUsers: [User!]!
		searchUsers(
			queryText: String
			filterActiveSubscriptions: Boolean
			limit: Int
			skip: Int
		): [User!]!
		searchUsersTotal(queryText: String, filterActiveSubscriptions: Boolean): Int!
		testing_getConfirmationCode(email: String!, type: ConfirmationCodeType!): String
		testing_forceDeleteUser(email: String!): DeleteUserReturnType
	}
	extend type Mutation {
		acceptAppTerms: acceptAppTermsReturnType
		allowDataUsage: acceptAppTermsReturnType
		allowAppTracking(allowed: Boolean!): acceptAppTermsReturnType
		optInToMarketingEmails(rejected: Boolean, token: String): acceptAppTermsReturnType
		login(
			email: String!
			password: String!
			privileged: Boolean
			appId: ID
			logoutFromOtherApps: Boolean
		): LoginReturnType
		socialLogin(
			provider: SocialAuthProvider!
			accessToken: String
			idToken: String
			appId: ID
			email: String
			name: String
			firstName: String
			lastName: String
			socialLoginSession: String
		): LoginReturnType
		loginPOS(authToken: String!): LoginReturnType
		logout(appId: ID): LogoutReturnType
		refreshLogin(refreshToken: String!, privileged: Boolean): LoginReturnType
		createAnonymousUser(appId: ID, bonusProgramId: ID): LoginReturnType
		requestLoginCreation(
			email: String!
			password: String!
			name: String
			firstName: String
			lastName: String
			gender: Gender
			birthDate: Date
			telephone: String
			street: String
			houseNumber: String
			zipCode: String
			city: String
			country: String
		): RequestCodeReturnType
		requestEmailChange(email: String!): RequestCodeReturnType
		resendEmailConfirmation(
			email: String!
			isChangeRequest: Boolean
		): resendActivationEmailReturnType
		confirmEmail(
			email: String!
			code: String!
			appId: ID
			isChangeRequest: Boolean
		): LoginReturnType
		requestPasswordReset(email: String!): RequestPasswordResetReturnType
		changePassword(email: String!, password: String!, code: String!, appId: ID): LoginReturnType
		updateUserProfile(
			name: String
			firstName: String
			lastName: String
			street: String
			houseNumber: String
			zipCode: String
			city: String
			country: String
			gender: Gender
			telephone: String
			birthDate: Date
		): createOrUpdateUserReturnType
		selectCinemas(cinemaIds: [ID!]!): selectCinemasReturnType
		selectCinema(cinemaId: ID!): selectCinemaReturnType
		deselectCinema(cinemaId: ID!): deselectCinemaReturnType
		increaseUserTestingStatus(testingStatus: TestingStatus!): increaseUserTestingStatusReturnType
		updateUserAdminStatus(
			userId: ID!
			cinemaOperatingCompanyId: ID!
			setAdminPrivilege: Boolean
		): updateUserAdminStatusReturnType!
		deleteUser: DeleteUserReturnType!
		deleteCineplexUser(id: String!, apiKey: String!): deleteCineplexUserReturnType!
		leaveBonusProgram(bonusProgramId: ID!): LeaveBonusProgramReturnType!
		rememberSkippedOnboardingStep(step: OnboardingStep!): rememberSkippedOnboardingStepReturnType!
		updateUser(
			userId: ID!
			blocked: Boolean
			blockedText: String
			firstname: String
			lastname: String
			street: String
			houseNumber: String
			zipCode: String
			city: String
			email: String
			adminCinemaOperatingCompanyIds: [ID!]
			resetAppChangeBlockedUntil: Boolean
		): User!
		updateUserSubscription(id: ID!, blocked: Boolean!, blockedText: String): Subscription!

		addLogin(
			email: String!
			password: String!
			appId: ID
			name: String
			firstName: String
			lastName: String
		): LoginReturnType @deprecated(reason: "[5.2.3] use requestLoginCreation instead")
		verifyEmail(email: String, appId: ID, token: String!, updating: Boolean): LoginReturnType
			@deprecated(reason: "[5.2.3] use confirmEmail instead")
		updateEmail(email: String!): createOrUpdateUserReturnType
			@deprecated(reason: "[5.2.3] use requestEmailChange instead")
		updatePassword(
			oldPassword: String
			appId: ID
			token: String
			password: String!
			email: String
		): LoginReturnType @deprecated(reason: "[5.2.3] use requestPasswordReset instead")
		sendResetPasswordEmail(email: String!): RequestPasswordResetReturnType!
			@deprecated(reason: "[5.0.12] use requestPasswordReset instead")
		resendActivationEmail(
			email: String
			userId: ID
			updating: Boolean
		): resendActivationEmailReturnType!
			@deprecated(reason: "[5.0.12] use resendConfirmationEmail instead")
		acceptCinuruTerms: Boolean @deprecated(reason: "[5.0.12] use acceptAppTerms instead")
		setInitialRatedMovies: SetInitialRatedMoviesReturnType
	}
	type SetInitialRatedMoviesReturnType {
		success: Boolean!
	}
	type RequestCodeReturnType {
		user: User
	}
	type DeleteUserReturnType {
		success: Boolean!
	}
	type deleteCineplexUserReturnType {
		success: Boolean!
	}
	type LeaveBonusProgramReturnType {
		success: Boolean!
	}
	type rememberSkippedOnboardingStepReturnType {
		user: User
	}
	type TestUserTokenReturnType {
		emailToken: String
		passwordToken: String
	}
	enum Gender {
		MALE
		FEMALE
		DIVERSE
	}
	enum SocialAuthProvider {
		FACEBOOK
		APPLE
		GOOGLE
	}
	enum UserBlockedReason {
		MISSING_EMAIL_VERIFICATION
		WRONG_EMAIL
		OTHER_ACCOUNT_EXISTED
		ANONYMOUS_USER_LOGGED_OUT
		OTHER
	}
	enum ConfirmationCodeType {
		LOGIN_CREATION
		EMAIL_CHANGE
		PASSWORD_RESET
	}
	enum LoginError {
		WRONG_PASSWORD
		WRONG_EMAIL_OR_ID
		NEITHER_EMAIL_NOR_ID_PROVIDED
	}
	enum LoginRole {
		USER
		POS_APP
		CINEMA_ADMIN
		ROOT
	}
	enum TestingStatus {
		TESTING
		PRODUCTION
		STAGING
		DEVELOPMENT
		CINEMA_EMPLOYEE
	}
	enum OnboardingStep {
		REGISTRATION
		JOIN_BONUS_PROGRAM
	}
	type User {
		id: ID!
		name: String
		fullName: String
		active: Boolean @deprecated(reason: "[3.3.1] use blocked instead")
		blocked: Boolean!
		blockedReason: UserBlockedReason
		blockedText: String
		email: String
		selectedCinemas: [Cinema!] @deprecated(reason: "[4.0.15] use cinemaCustomerships instead")
		cinemaCustomerships(currentlySelectedOnly: Boolean): [CinemaCustomership!]!
		watchlist(cinemaIds: [ID!], lastChance: Boolean): MovieList!
		ratedlist: MovieList!
		seenInCinemaList: MovieList!
		reviewRatings: [ReviewRating!]! @deprecated(reason: "[5.2.3] use ratedlist instead")
		interestRatings(removeReviewRatedMovies: Boolean): [InterestRating!]!
			@deprecated(reason: "[5.2.3] use watchlist instead")
		bonusProgramMembership(bonusProgramId: ID, cinemaId: ID): BonusProgramMembership
		tickets(cinemaIds: [ID!], openOnly: Boolean, sync: Boolean): [Ticket!]!
		orders(cinemaIds: [ID!], openOnly: Boolean, sync: Boolean): [Order!]!
		bonusProgramMemberships: [BonusProgramMembership!]!
		onlineTicketingToken: String
		testingStatus: TestingStatus
		termsStatus: AppTermsStatus
		dataUsageAllowed: Boolean
		marketingEmailsAllowed: Boolean
		notificationPreferences: [NotificationPreference!]!
		privileges: UserPrivileges!
		notifications: [Notification!]
		anonymous: Boolean!
		askForAppFeedback: Boolean!
		askToRegister: Boolean!
		askToJoinBonusProgram: Boolean!
		zipCode: String
		gender: Gender
		birthDate: Date
		firstName: String
		lastName: String
		street: String
		houseNumber: String
		city: String
		country: String
		lastSync: DateTime
		telephone: String
		subscriptions: [Subscription!]!
		invoices: [Invoice!]!
		vouchers(onlyRedeemed: Boolean): [VoucherInstance!]!
		appTrackingAllowed: Boolean
	}
	type UserPrivileges {
		belongsToCinemaOperatingCompanies: [CinemaOperatingCompany!]!
		adminForCinemas: [Cinema!]!
		adminForBonusPrograms: [BonusProgram!]!
		accessRightDashboard: Boolean!
		accessRightFilmStatistics: Boolean!
		accessRightBonusProgram: Boolean!
		accessRightCampaigns: Boolean!
		adminRole: Boolean!
		rootRole: Boolean!
		supportRole: Boolean!
	}
	type PosApp {
		id: ID
		cinemaId: ID
		bonusProgramId: ID
	}
	type CinemaCustomership {
		id: ID!
		cinema: Cinema
		user: User
	}
	type AppTermsStatus {
		latestTermsAgreed: Boolean
		alertTitle: String
		alertDescription: String
		alertButtonLabel: String
	}
	type selectCinemaReturnType {
		cinemaCustomership: CinemaCustomership!
	}
	type deselectCinemaReturnType {
		success: Boolean
	}

	type updateUserAdminStatusReturnType {
		success: Boolean!
		user: User
	}
	type RequestPasswordResetReturnType {
		success: Boolean!
	}
	type resendActivationEmailReturnType {
		success: Boolean!
		sentToEmail: String
	}
	type increaseUserTestingStatusReturnType {
		user: User
	}
	type LoginReturnType {
		user: User
		posApp: PosApp
		fileServerToken: String
		jwt: String
		csrf: String
		roles: [LoginRole!]
		refreshToken: String
	}
	type LoginStatusType {
		success: Boolean
		errors: [LoginError!]
	}
	type LogoutReturnType {
		success: Boolean
	}
	type selectCinemasReturnType {
		cinemas: [Cinema!]!
		user: User!
	}
	type createOrUpdateUserReturnType {
		user: User
	}
	type acceptAppTermsReturnType {
		user: User
	}
	type Invoice {
		id: ID!
		url: String!
		date: Date!
		priceInCents: Int!
	}
`;
