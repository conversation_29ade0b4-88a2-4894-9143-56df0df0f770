/* eslint-disable no-await-in-loop, no-catch-all/no-catch-all, promise/prefer-await-to-callbacks */

import fetch from 'node-fetch';

import AbortController from 'abort-controller';
import uuid from 'uuid/v4';
import {
	db,
	UserBlockedError,
	sql,
	sendErrorMail,
	InternalServerError,
	AuthenticationError,
	CineplexServiceError,
	printError,
} from '../../utils';
import { getFixedI18n } from '../../utils/i18nHelpers';
import { CINEPLEX_CUSTOMER_DB_API_URL, ENV, SYNC_USER_FUNCTION_NAME } from '../../../consts';
import {
	InternalUserId,
	InternalCinemaId,
	InternalMovieId,
	InternalVoucherClassId,
	Viewer,
	Context,
} from '../../typescript-types';
import * as loginService from './cineplexLoginService';
import {
	getTickets,
	getWatchlist,
	addOr<PERSON><PERSON>oveFromWatchlist,
	CP_COUNTRY_VALUES_REVERSE,
} from './cineplexKrankikomCustomerService';

import type { GetTicketsData, GetWatchlistData } from './cineplexKrankikomCustomerService';

import { stripIndent } from 'common-tags';
import { createWelcomeAchievement } from '../../pos/pos-adapter-cineplex/createWelcomeAchievement';
import { ForbiddenError } from 'apollo-server';
import { optInToMarketingEmails } from './acceptAppTerms';
import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda';
import { POSAdapterCineplex } from '../../pos/pos-adapter-cineplex';
import { BonusProgramMembership } from '../BonusProgramMembership/BonusProgramMembership';

const lambdaClient = new LambdaClient({
	region: 'eu-central-1',
});

interface CustomerConsent {
	key: string;
	text: string;
	version: number;
	accepted: boolean;
}

interface CustomerCardAccount {
	accountNo: number;
	accountName: string;
	accountType: string;
	accountUnit: string;
	visibleForEnduser: boolean;
	balanceAmount: number;
	balanceValue: number;
}
interface CustomerCard {
	customerCardNumber: string;
	customerId: string;
	customerCardId: string;
	cardStatusId: number;
	cardStatusName: string;
	cardStatusIsBlocked: boolean;
	accounts: CustomerCardAccount[];
}
interface Customer {
	customerId: string;
	loginId: number;
	title: number;
	firstname: string;
	lastname: string;
	dateOfBirth: string; // YYYY-MM-DD
	email: string;
	emailNew: string;
	phoneNumber: string;
	streetName: string;
	houseNumber: string;
	postCode: string;
	cityName: string;
	country: string;
	dateCreated: string; // YYYY-MM-DD HH:MM:SS
	dateLastUpdated: string; // YYYY-MM-DD HH:MM:SS
	dateAnonymized: string; // YYYY-MM-DD HH:MM:SS
	consents: CustomerConsent[];
	customerCards: CustomerCard[];
}
export interface AccountPosting {
	id: number;
	accountNo: number;
	isCancellation: boolean;
	bookingDate: string;
	amount: number;
	currencyCode: string;
	postingType: number;
	postingText: string;
	remoteCenterNo: number;
	remoteWorkstationId: string;
	filmId: string;
	rentrakNo: string;
	value: number;
	performanceId: string;
}
interface AccountPostingsResponse {
	accountNo: number;
	accountName: string;
	accountType: 'PK' | 'TK' | 'WK' | 'GK' | 'RK' | 'EK' | 'CK' | 'SK';
	accountUnit: string;
	visibleForEnduser: boolean;
	balanceAmount: number;
	balanceValue: number;
	postings: AccountPosting[];
}

// we will only sync with CP every 5 minutes
const SYNC_INTERVAL = 5 * 60 * 1000;

const formatCineplexDate = (date: Date): string /*yyyy-MM-dd HH:mm:ss in UTC*/ => {
	return date.toISOString().substr(0, 19).replace('T', ' ');
};

type requestLogObject = {
	initAt: number;
	refreshedAuth: boolean;
	requestPath: string;
	requestId: string;
	finishedAt?: number;
	error?: string;
	method?: string;
	params?: string;
	userIdDb?: InternalUserId;
};

const storeLog = async (log: requestLogObject) => {
	const logObject = { ...log, timeElapsed: log.finishedAt - log.initAt };
	console.log('CP_CUSTOMER_SERVICE_REQUEST', logObject);
};

// docs: http://************:33100/#/
class CineplexCustomer {
	dbId: InternalUserId;
	cpId: string;
	accessToken: string;
	refreshToken: string;
	lastSync: Date;
	initialSync: boolean;

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	constructor(data: any) {
		Object.assign(this, data);
	}

	static async gen(userIdDb: InternalUserId): Promise<CineplexCustomer | null> {
		const res = await db.query(sql`
			SELECT cp_id, cp_access_token, cp_refresh_token, cp_last_sync, cp_initial_sync
			FROM priv.p_user 
			WHERE id=${userIdDb} AND registered_with_language='de-cineplex'
		`);
		if (!res.rows.length || !res.rows[0].cp_id) {
			return null;
		}
		return new CineplexCustomer({
			dbId: userIdDb,
			cpId: res.rows[0].cp_id,
			accessToken: res.rows[0].cp_access_token,
			refreshToken: res.rows[0].cp_refresh_token,
			lastSync: res.rows[0].cp_last_sync,
			initialSync: Boolean(res.rows[0].cp_initial_sync),
		});
	}

	tryingToRefresh: Promise<null | string> = null;
	async refreshAccessToken(): Promise<null | string> {
		if (!this.tryingToRefresh) {
			this.tryingToRefresh = (async () => {
				const { data, errorMessage } = await loginService.refresh({
					cpRefreshToken: this.refreshToken,
				});
				if (errorMessage) {
					return errorMessage;
				}
				this.refreshToken = data.cpRefreshToken;
				//-> can not be tested for now
				this.accessToken = data.cpAccessToken;
				db.query(sql`
					UPDATE priv.p_user SET 
						cp_refresh_token=${this.refreshToken}, 
						cp_access_token=${this.accessToken},
						cp_website_token=${data.cpWebsiteToken}
					WHERE cp_id=${this.cpId};
				`);
				//-> cannot be tested for now
				return null;
			})();
		}
		return await this.tryingToRefresh;
	}

	async request<D>(
		method: 'POST' | 'PUT' | 'PATCH' | 'GET' | 'DELETE',
		path: string,
		requestId: string,
		params?: { [param: string]: unknown } | unknown[],
		customerRelativePath: boolean = true,
		apiKey?: string
	): Promise<
		| { status: number; data: D; error?: undefined }
		| { status: number; data?: undefined; error: string }
	> {
		const controller = new AbortController();
		let timeout = setTimeout(() => {
			controller.abort();
		}, 30000);
		const logging: requestLogObject = {
			initAt: Date.now(),
			refreshedAuth: false,
			requestPath: path,
			requestId,
			method,
			params: params && JSON.stringify(params),
			userIdDb: this.dbId,
		};

		const url = customerRelativePath
			? CINEPLEX_CUSTOMER_DB_API_URL + `/customers/${this.cpId}${path}`
			: CINEPLEX_CUSTOMER_DB_API_URL + path;
		const body = params && JSON.stringify(params);
		const headers: {
			'Accept': string;
			'Content-Type': string;
			'X-COMPESO-CORRELATION-ID': string;
			'X-API-KEY'?: string;
			'Authorization'?: string;
			'Client-Type': string;
			'Client-Version': string;
		} = {
			'Accept': 'application/json',
			'Content-Type': 'application/json',
			'X-COMPESO-CORRELATION-ID': requestId,
			'Client-Type': 'APP_SERVER',
			'Client-Version': '1',
			...(apiKey ? { 'X-API-KEY': apiKey } : { Authorization: `Bearer ${this.accessToken}` }),
		};
		try {
			//@ts-ignore
			let response = await fetch(url, { method, headers, body, signal: controller.signal });
			if (response.status === 503) {
				throw new Error('503'); // to land in the catch block
			}
			clearTimeout(timeout);
			// if the response is 401, try refreshing the access token once
			if (response.status === 401 && headers.Authorization) {
				logging.refreshedAuth = true;
				const errorMessage = await this.refreshAccessToken();
				if (errorMessage) {
					logging.error = errorMessage;
					logging.finishedAt = Date.now();
					await storeLog(logging);
					return { status: 401, error: errorMessage };
				}
				//-> can not be tested for now
				headers.Authorization = `Bearer ${this.accessToken}`;
				timeout = setTimeout(() => {
					controller.abort();
				}, 30000);
				//@ts-ignore
				response = await fetch(url, { method, headers, body, signal: controller.signal });
				clearTimeout(timeout);
			}
			const content = await response.text();
			if (response.ok) {
				const data = content.length > 0 ? JSON.parse(content) : null;
				logging.finishedAt = Date.now();
				await storeLog(logging);
				return { data, status: response.status };
			} else {
				let responseHeaders = '';
				for (let pair of response.headers.entries()) {
					responseHeaders += pair[0] + ': ' + pair[1];
				}
				logging.error = JSON.stringify({ url, body, response: content, responseHeaders });
				logging.finishedAt = Date.now();
				await storeLog(logging);
				return {
					error: JSON.stringify({
						url,
						body,
						response: content,
						headers,
					}),
					status: response.status,
				};
			}
			// eslint-disable-next-line no-catch-all/no-catch-all
		} catch (e) {
			clearTimeout(timeout);
			logging.error = JSON.stringify({
				url,
				body,
				type: 'CustomerDB unavailable',
				message: e.toString(),
			});
			logging.finishedAt = Date.now();
			await storeLog(logging);
			//-> can not be tested for now
			throw new CineplexServiceError({
				errorMessage: 'Error on CineplexCustomerService',
				errorUserMessage: 'Der Service ist nicht erreichbar. Bitte versuchen Sie es später erneut.',
				errorCode: 'CO-1',
				errorAction: 'retry-last',
				status: 503,
				requestId,
			});
		}
	}

	handleUnexpectedError = (message: string, requestId?: string, sendEmail = true) => {
		sendErrorMail(
			`UNEXPECTED_CINEPLEX_CUSTOMER_SERVICE_ERROR`,
			`requestId: ${requestId ? requestId : 'UNKNOWN'}\n${message}`,
			!sendEmail
		);

		throw new InternalServerError(message);
	};

	async syncProfile(requestId: string): Promise<void> {
		const res = await this.request<Customer>('GET', '?includeCardData=true', requestId);
		if (res.status === 403) {
			//-> can not be tested/too hard to test for now
			await sendErrorMail(
				'We received 403 on syncProfile',
				stripIndent`This means a user has been deleted, but
				we did not get noticed by the Login service. 
				UserId: ${this.dbId}`
			);
			//-> can not be tested/too hard to test for now
			throw new UserBlockedError('USER_WAS_DELETED');
		}
		if (res.data) {
			const data = res.data;

			// sync user data
			await db.query(sql`
		UPDATE
			priv.p_user 
		SET
			username=${[data.firstname, data.lastname].join(' ')},
			email=${(data.email || '').toLowerCase()},
			first_name=${data.firstname},
			last_name=${data.lastname},
			street=${data.streetName},
			house_number=${data.houseNumber},
			zip_code=${data.postCode},
			city=${data.cityName},
			country=${
				CP_COUNTRY_VALUES_REVERSE[data.country] ||
				data.country /* TODO remove CP_COUNTRY_VALUES_REVERSE mapping after 01.01.2026 - Cineplex changed from GERMANY to DE */
			},
			telephone=${data.phoneNumber},
			birth_date=${data.dateOfBirth},
			birth_day_without_year=${data.dateOfBirth?.substring(5)},
			gender=${data.title === 1 ? 'FEMALE' : data.title === 2 ? 'MALE' : null}
		WHERE 
			cp_id=${this.cpId};
	`);

			// terms conesnt requires three consents to be set true for historical reasons on cineplex

			const givenConsents = data.consents.filter((c) => c.accepted).map((c) => c.key);
			let relevantTermsAccepted = false;
			if (
				givenConsents.includes('AGE_16') &&
				givenConsents.includes('DATA_USAGE_DECLARATION') &&
				givenConsents.includes('TERMS_AND_CONDITIONS')
			) {
				relevantTermsAccepted = true;
			}

			const appTermsConsent = relevantTermsAccepted
				? data.consents.find((c) => c.key === 'TERMS_AND_CONDITIONS')
				: null;
			const marketingEmailsAllowed = data.consents.find((c) => c.key === 'ADVERTISEMENT')?.accepted;

			if (!appTermsConsent) {
				await db.query(
					sql`DELETE FROM priv.p_privacy_terms_accepted_by_user WHERE user_id = ${this.dbId}`
				);
			} else {
				const res1 = await db.query(
					`SELECT max(terms_version) as "latestTermsVersion" 
				FROM priv.p_privacy_terms_accepted_by_user 
				WHERE user_id = $1 AND language = 'de-cineplex'`,
					[this.dbId]
				);
				if (
					res1.rows[0].latestTermsVersion === null ||
					res1.rows[0].latestTermsVersion < appTermsConsent.version
				) {
					// cineplex customer service has newer accepted terms version
					await db.query(sql`
					INSERT INTO priv.p_privacy_terms_accepted_by_user(
						user_id, terms_version, language, accepted_datetime
					) VALUES (
						${this.dbId}, ${appTermsConsent.version}, 'de-cineplex', now()
					);
				`);

					await db.query(
						sql`UPDATE priv.p_user SET data_usage_allowed=now() 
					WHERE id = ${this.dbId} AND data_usage_allowed IS NULL`
					);
				}

				// sync marketing emails opt in
				await db.query(
					sql`
						UPDATE 
							priv.p_user 
						SET 
								marketing_emails_opt_in=CASE
									WHEN ${marketingEmailsAllowed} THEN COALESCE(marketing_emails_opt_in, now()) 
									ELSE NULL
									END
						WHERE 
							id = ${this.dbId}`
				);
				// sync created at date
				await db.query(
					`UPDATE priv.p_user SET registered_datetime=COALESCE(registered_datetime, $2) WHERE cp_id=$1;`,
					[data.customerId, new Date(data.dateCreated)]
				);

				// sync customer card
				if (data.customerCards.length > 1) {
					//-> can not be tested because impossible to run into for now
					this.handleUnexpectedError('USER_HAS_MULTIPLE_CUSTOMER_CARDS', requestId);
				}

				if (data.customerCards.length === 1) {
					const relevantCustomerCard: CustomerCard = data.customerCards[0]; //Needed for typing
					const { customerCardNumber, customerId, cardStatusId } = relevantCustomerCard;
					relevantCustomerCard;
					// create BonusProgramMembership if it does not exist
					await db.query(
						sql`
							INSERT INTO priv.p_bonus_program_membership (user_id, bonus_program_id, joined_at, active) (
								SELECT
									${this.dbId},
									1,
									${new Date(data.dateCreated)},
									true
								FROM (
									SELECT
										count(*) = 0 AS non_existent
									FROM
										priv.p_bonus_program_membership
									WHERE
										user_id = ${this.dbId}
										AND bonus_program_id = 1) c
								WHERE
									non_existent = TRUE)
							`
					);

					await createWelcomeAchievement(this.dbId);

					const qrCode = `{"CustomerCard": {"CardNumber": "${customerCardNumber}","ID": "${customerId}"}}`;
					await db.query(`UPDATE priv.p_user SET qr_code=$2 WHERE id=$1`, [this.dbId, qrCode]);

					const getCompesoAccountPostings = async (accountId: number) => {
						const res2 = await this.request<AccountPostingsResponse>(
							'GET',
							`/customercards/${customerCardNumber}/postings/${accountId}/`,
							requestId
						);
						if (res.status !== 200 || res2.error) {
							//TODO: think about better error handling here
							//-> can not be tested for now (Compeso-System-Failure)
							sendErrorMail(
								'Cineplex Error syncing vouchers',
								`Got an error querying /customercards/${customerCardNumber}/postings/${accountId}/
								Result: ${JSON.stringify(res2, null, 2)}
								User: ${this.dbId}
								`
							);
							//-> can not be tested
							return;
						}
						return res2.data;
					};

					const syncVoucherType = async (voucherClassIdDb: number, compesoAccountId: number) => {
						const response = await getCompesoAccountPostings(compesoAccountId);
						if (!response) {
							//-> can not be tested for now
							return;
						}

						// Our schema works fundamentially different then Compesos on vouchers.
						// We have voucher entities, while for Compeso vouchers are "accounts" that can be booked.

						const positivePostings = response.postings.filter((posting) => posting.amount > 0);
						const negativePostings = response.postings.filter((posting) => posting.amount < 0);

						// Delete outdated vouchers -> We need to delete outdated vouchers first to avoid conflicts
						const positivePostingForeignIds = positivePostings.flatMap((p) => {
							let ids = [];
							const numVouchers = compesoAccountId !== 2271 ? p.amount : p.amount / 200;
							for (let i = 0; i < numVouchers; i++) {
								ids.push(`${p.id}_${i}`);
							}
							return ids;
						});

						await db.query(sql`
									DELETE FROM priv.p_voucher
									WHERE user_id = ${this.dbId}
									AND voucher_type_id = ${voucherClassIdDb}
									AND foreign_voucher_id != ALL(${positivePostingForeignIds})
									`);

						// Delete outdated vouchers redemptions
						const negativePostingsForeignIds = negativePostings.flatMap((p) => {
							let ids = [];
							const numVouchers = compesoAccountId !== 2271 ? p.amount : p.amount / 200;
							for (let i = 0; i < -numVouchers; i++) {
								ids.push(`${p.id}_${i}`);
							}
							return ids;
						});

						await db.query(sql`
								UPDATE priv.p_voucher
								SET foreign_voucher_redemption_id = NULL,
								redeemed_datetime = NULL
								WHERE user_id = ${this.dbId}
								AND voucher_type_id = ${voucherClassIdDb}
								AND foreign_voucher_redemption_id != ALL(${negativePostingsForeignIds})
							`);

						let existingVouchers = (
							await db.query(
								sql`
								SELECT 
									id,
									voucher_type_id,
									created_datetime,
									redeemed_datetime,
									foreign_voucher_id,
									foreign_voucher_redemption_id,
									valid
								FROM
									priv.p_voucher
								WHERE
									user_id = ${this.dbId}
									AND
									voucher_type_id = ${voucherClassIdDb}
								ORDER BY 
									created_datetime ASC
								;
								`
							)
						).rows;

						for (let idx = 0; idx < positivePostings.length; idx++) {
							const posting = positivePostings[idx];
							const bookingDate = new Date(`${posting.bookingDate}Z`);
							const numVouchers = compesoAccountId !== 2271 ? posting.amount : posting.amount / 200;
							//The voucher "Privatvorstellung" is booked as 200 Tickets on account 2271 but we only want to display one voucher to the user

							if (numVouchers > 0) {
								for (let i = 0; i < numVouchers; i++) {
									const foreignVoucherId = `${posting.id}_${i}`;
									positivePostingForeignIds.push(foreignVoucherId);
									//Create voucher (if needed)
									const matchingVouchers = existingVouchers.filter(
										(v) => v.foreign_voucher_id === foreignVoucherId
									);
									// if we find a matching voucher, we have this booking already recorded
									// in our database so we do nothing. Othewise we process it.
									if (matchingVouchers.length === 0) {
										// eslint-disable-next-line no-await-in-loop
										const res2 = await db.query(
											sql`
												INSERT INTO priv.p_voucher (
													user_id, 
													voucher_type_id, 
													created_datetime, 
													valid,
													foreign_voucher_id,
													remote_workstation_id
													)
												VALUES (
													${this.dbId},
													${voucherClassIdDb},
													${bookingDate},
													true,
													${foreignVoucherId},
													${posting.remoteWorkstationId}
												)
											ON CONFLICT ON CONSTRAINT p_voucher_foreign_voucher_id_key DO NOTHING
											RETURNING
												id,
												voucher_type_id,
												created_datetime,
												redeemed_datetime,
												valid,
												foreign_voucher_id,
												foreign_voucher_redemption_id
											
											`
										);
										if (res2.rows[0]) {
											existingVouchers.push(res2.rows[0]);
										}
									}
								}
							}
						}

						for (let idx = 0; idx < negativePostings.length; idx++) {
							const posting = negativePostings[idx];
							const bookingDate = new Date(`${posting.bookingDate}Z`);
							const numVouchers = compesoAccountId !== 2271 ? posting.amount : posting.amount / 200;
							//The voucher "Privatvorstellung" is booked as 200 Tickets on account 2271 but we only want to display one voucher to the user

							if (numVouchers < 0) {
								for (let i = 0; i < -numVouchers; i++) {
									const foreignRedemptionId = `${posting.id}_${i}`;
									negativePostingsForeignIds.push(foreignRedemptionId);
									//-> cannot be tested because we cant redeem vouchers in the compeso system
									const matchingVouchers = existingVouchers.filter(
										(v) =>
											v.foreign_voucher_redemption_id &&
											v.foreign_voucher_redemption_id === foreignRedemptionId
									);
									// if we find a matching voucher, we have this booking already recorded
									// in our database so we do nothing. Othewise we process it.
									if (matchingVouchers.length === 0) {
										// Find oldest voucher to redeem
										const validMatchingVouchers = existingVouchers.filter(
											(v) => v.redeemed_datetime === null
										);
										if (validMatchingVouchers.length === 0) {
											//We have to reedem a voucher, but there is no open voucher
											// eslint-disable-next-line no-await-in-loop
											await sendErrorMail(
												'Cinplex: Cannot redeem Voucher',
												`We need to mark a voucher as redeemed but cannot find one to redeem. 
													Posting:${JSON.stringify(posting, null, 2)}
													API Response: ${JSON.stringify(response, null, 2)}
													User: ${this.dbId}`.replace(/\\t/g, '')
											);
										} else {
											const voucherId = validMatchingVouchers[0].id;
											// eslint-disable-next-line no-await-in-loop
											await db.query(sql`
													UPDATE 
														priv.p_voucher
													SET
														redeemed_datetime = ${bookingDate},
														foreign_voucher_redemption_id = ${foreignRedemptionId}
													WHERE 
														id = ${voucherId}
														AND
														${String(posting.id)}::text != ALL (
															SELECT foreign_voucher_redemption_id 
															FROM priv.p_voucher 
															WHERE foreign_voucher_redemption_id IS NOT NULL
															)
												`);
											existingVouchers.forEach((v, iidx) => {
												if (v.id === voucherId) {
													existingVouchers[iidx].redeemed_datetime = bookingDate;
													existingVouchers[iidx].foreign_voucher_redemption_id = posting.id;
												}
											});
										}
									}
								}
							}
						}

						//Check integrity
						const checkRes = await db.query(
							sql`SELECT count(*) FROM priv.p_voucher WHERE redeemed_datetime IS NULL AND user_id= ${this.dbId} AND voucher_type_id = ${voucherClassIdDb} AND valid = true`
						);
						const numberOfOpenVouchers = Number(checkRes.rows[0].count);

						//The voucher "Privatvorstellung" is booked as 200 Tickets on account 2271 but we only want to display one voucher to the user
						const numExistingVouchersCompeso =
							compesoAccountId !== 2271 ? response.balanceAmount : response.balanceAmount / 200;
						if (numberOfOpenVouchers !== numExistingVouchersCompeso) {
							//-> can not be tested for now
							await sendErrorMail(
								'Cineplex: Sync of open voucher lead to incongruent data',
								`After syncing the vouchers, the number of open vouchers, our balance did not match the incoming.
									Result: ${JSON.stringify(response, null, 2)}
									UserId: ${this.dbId}
									NumVouchers in our Database: ${numberOfOpenVouchers}`
							);
						}
					};

					await Promise.all([
						syncVoucherType(1, 2000), // Freiticket 2D
						syncVoucherType(2, 2201), // Geburtstagsfreikarte
						syncVoucherType(3, 2202), // Welcome Popcorn
						syncVoucherType(6, 2203), //Menü
						syncVoucherType(4, 2261), // Monatskarte
						syncVoucherType(5, 2271), // Privatvorstellung
					]);

					const syncPoints = async (pointType: string, accountNumber: number) => {
						//Sync Bonuspoints

						const bonusPointBookingsCompeso = await getCompesoAccountPostings(accountNumber);
						if (bonusPointBookingsCompeso) {
							// Delete Bookings that do not exist any more.
							const existingBookingIds = bonusPointBookingsCompeso.postings.map((p) => p.id) || [];

							await db.query(sql`
										DELETE FROM priv.p_bonus_point_booking
										WHERE user_id = ${this.dbId}
										AND foreign_booking_id != ALL (${existingBookingIds})
										AND point_type=${pointType}
										`);

							const existingBookings = (
								await db.query(
									sql`
											SELECT
												id,
												amount,
												datetime,
												movie_id,
												screening_id,
												foreign_booking_id
				
											FROM
												priv.p_bonus_point_booking
											WHERE  
												valid = true
												AND user_id = ${this.dbId}
												AND currency_id = 1
												AND point_type = ${pointType}
											;
										`
								)
							).rows;
							for (const bookingCompeso of bonusPointBookingsCompeso.postings) {
								const bookingDate = new Date(`${bookingCompeso.bookingDate}Z`);
								const matchingPosting = existingBookings.filter(
									(bk) => Number(bk.foreign_booking_id) === bookingCompeso.id
								);

								if (matchingPosting.length === 0) {
									//--> cannot be tested so far
									if (accountNumber === 9111 && bookingCompeso.amount > 0) {
										// eslint-disable-next-line no-await-in-loop
										await bookInvitePoints(this.dbId, bookingDate);
									}
									//Create a booking

									//
									// The query looks a bit complicated. This is because we need to translate screening id:
									// Compeso sends us "their" screening id, but we must resolve it to our internal one.
									//
									// The screening id is not always set. We could query all screenings and build a lookup dictionary,
									// but as there are a large number of screenings, that would be slow and inefficient.
									//
									// Instead we translate the screening ids on the fly. The complicated part here is, that the query
									// must as well work without a screening id.
									//
									// Therefore we create the "virtual_merge_table" first.
									// This table always contains exactly one row with one column, the foreign screening id. We can
									// then use this table to LEFT JOIN with p_showtime. That will always return exactly one row.
									// We can use that result to insert into priv.p_bonus_point_booking (and add the remaining data need).
									//

									// eslint-disable-next-line no-await-in-loop
									const bookingRes = await db.query(
										sql`
											WITH virtual_merge_table as(
														SELECT  ${bookingCompeso.performanceId}  as foreign_showtime_id
													)
													INSERT INTO priv.p_bonus_point_booking(
														currency_id, 
														user_id, 
														amount, 
														datetime, 
														valid,
														movie_id,
														screening_id,
														point_type,
														description,
														foreign_booking_id
														)
													(
														SELECT
															1,
															${this.dbId},
															${bookingCompeso.amount},
															${bookingDate},
															true,
															s.movie_id,
															s.id,
															${pointType},
															${bookingCompeso.postingText},
															${bookingCompeso.id}
														FROM
															virtual_merge_table t
														LEFT JOIN 
															priv.p_showtime s ON t.foreign_showtime_id = s.foreign_showtime_id
	
													)
													ON CONFLICT ON CONSTRAINT p_bonus_point_booking_foreign_booking_id_key DO NOTHING
													RETURNING
														id,
														amount,
														datetime,
														movie_id,
														screening_id,
														foreign_booking_id
													`
									);
									if (bookingRes.rows[0]) {
										existingBookings.push(bookingRes.rows[0]);
									}
								} else {
									const existingPosting = matchingPosting[0];
									if (existingPosting.amount !== bookingCompeso.amount) {
										await db.query(sql`
											UPDATE priv.p_bonus_point_booking
											SET amount =${bookingCompeso.amount}
											WHERE 
											foreign_booking_id = ${bookingCompeso.id}
											AND point_type = ${pointType}
											`);
									}
								}
							}

							const ourBalance = (
								await db.queryOne(sql`
										SELECT coalesce(sum(amount),0) as sum   
										FROM priv.p_bonus_point_booking 
										WHERE user_id = ${this.dbId} 
											AND valid = true 
											AND point_type = ${pointType}
								`)
							).sum;

							if (ourBalance !== bonusPointBookingsCompeso.balanceAmount && this.dbId !== 148) {
								// We have a different bonus point balance in our database than what we got from compeso
								//can not be tested for now
								await sendErrorMail(
									'Cineplex: Bonus Point Balances do not match',
									`The sum of Bonuspoints we got from compeso did not match the booking we have in our database	
										API Response: ${JSON.stringify(bonusPointBookingsCompeso, null, 2)}
										User: ${this.dbId}
										Our Balance: ${ourBalance}
							`.replace(/\\t/g, '')
								);
							}
						}
					};
					await syncPoints('BONUS_POINTS', 9111);
					await syncPoints('STATUS_POINTS', 1000);
					await syncPoints('INFO', 2000);

					//Sync Status Level
					await db.query(
						sql`
							UPDATE 
								priv.p_bonus_program_membership
							SET 
								status_level = ${'StatusLevel' + cardStatusId},
								status_level_updated_at = now()
							WHERE
								user_id = ${this.dbId}
								AND
								bonus_program_id=1
								AND
								(
									status_level != ${'StatusLevel' + cardStatusId}
									OR
									status_level IS NULL
								)
								
							`
					);
					// Sync Status Level History
					const statusLevelBookings = await getCompesoAccountPostings(9901);
					if (statusLevelBookings && statusLevelBookings.postings) {
						const existingStatusLevelBookings = (
							await db.query(
								sql`
								SELECT
								foreign_booking_id
							FROM
								priv.p_bonus_point_booking
							WHERE
								point_type = 'STATUS_LEVEL'
								AND user_id = ${this.dbId}
							;`
							)
						).rows;
						const existingForeignIds = existingStatusLevelBookings.map((r) =>
							Number(r.foreign_booking_id.split('_')[1])
						);
						await Promise.all(
							statusLevelBookings.postings.map(async (booking) => {
								if (!existingForeignIds.includes(booking.id)) {
									// Insert booking

									const bookingDate = new Date(`${booking.bookingDate}Z`);
									const level = Number(booking.postingText[booking.postingText.length - 1]); // Parse the booking text (e.g. "Erreichen von Status Level 3");
									if (!isNaN(level)) {
										await db.query(sql`
										
										INSERT INTO priv.p_bonus_point_booking(
											currency_id, 
											user_id, 
											amount, 
											datetime, 
											valid,
											point_type,
											description,
											foreign_booking_id
											)
										VALUES (
												1,
												${this.dbId},
												${level},
												${bookingDate},
												true,
												'STATUS_LEVEL',
												${booking.postingText},
												${'STATUSLEVEL_' + booking.id}
											
	
										   )
										ON CONFLICT ON CONSTRAINT p_bonus_point_booking_foreign_booking_id_key DO NOTHING
										`);
									}
								}
							})
						);
					}
				}
				if (data.customerCards.length === 0) {
					// A user might have left the Bonusprogram
					await this.deleteBonusProgramMembershipData();
				}
			}
		} else {
			if (res.status === 401 && res.error === 'OUTDATED_REFRESH_TOKEN') {
				throw new ForbiddenError('OUTDATED_REFRESH_TOKEN');
			} else {
				this.handleUnexpectedError(res.error, requestId);
			}
		}
	}

	//-> can not be tested for now
	async syncTickets(): Promise<void> {
		let data: GetTicketsData;
		try {
			data = await getTickets({ cpAccessToken: this.accessToken });
		} catch (e) {
			// The Krankikom Customer Service throws an unauthenticated error if the access token is outdated. In this
			// case we want to refresh the access token and retry the sync

			if (e instanceof AuthenticationError) {
				await this.refreshAccessToken();

				data = await getTickets({ cpAccessToken: this.accessToken });
			} else {
				throw e;
			}
		}

		const currentTickets = data.map((order) => order.bookingProcessId);
		const existingTickets = (
			await db.query(sql`SELECT cp_booking_id FROM priv.p_ticket WHERE user_id = ${this.dbId}`)
		).rows.map((r) => r.cp_booking_id);
		await db.query(
			sql`DELETE FROM priv.p_ticket WHERE user_id = ${this.dbId} AND cp_booking_id != ALL (${currentTickets})`
		);
		const orderRes = await db.query(
			sql`SELECT foreign_order_id, status FROM priv.p_order WHERE user_id = ${this.dbId}`
		);
		const existingOrdersMap = new Map(orderRes.rows.map((r) => [r.foreign_order_id, r]));
		const hasConcessions = (order) =>
			order.concessions && order.concessions.lineItems && order.concessions.lineItems.length;
		const currentOrders = data.filter(hasConcessions).map((order) => order.bookingProcessId);
		await db.query(
			sql`DELETE FROM priv.p_order WHERE user_id = ${this.dbId} AND foreign_order_id != ALL (${currentOrders})`
		);

		const cpScreeningIds = data.map((order) => String(order.cpScreeningId));
		const cpMovieIds = data.map((order) => String(order.cpMovieId));

		const screeningIdPromise = db.query(
			sql`SELECT id, foreign_showtime_id FROM priv.p_showtime WHERE foreign_showtime_id = ANY(${cpScreeningIds})`
		);
		const movieIdPromise = db.query(
			sql`SELECT movie_id, foreign_id 
					FROM priv.p_foreign_movie_identifier 
					WHERE foreign_id_type = 'CINEPLEX' AND foreign_id = ANY(${cpMovieIds})`
		);
		const [screeningIdRes, movieIdRes] = await Promise.all([screeningIdPromise, movieIdPromise]);

		const screeningMap = new Map(screeningIdRes.rows.map((r) => [r.foreign_showtime_id, r.id]));

		const movieIdMap = new Map(movieIdRes.rows.map((r) => [r.foreign_id, r.movie_id]));

		for (const order of data) {
			const screeningId = screeningMap.get(String(order.cpScreeningId));
			const movieId = movieIdMap.get(String(order.cpMovieId));

			let refundable = order.refundable;
			let status = order.status;
			if (existingTickets.includes(order.bookingProcessId)) {
				// Currently Tickets & Orders are coupled. One cannot cancel only tickets or concessions.
				// Therefore tickets should not be cancelable once the preparation of the corresponding concessions order has started.
				// If this changes, make sure to remove the code in Order as well (for the ticket import)
				//
				// The PREPARATION status should only be overwritten if the Order has been refunded (e.g. manually via the cms)

				if (
					order.status !== 'REFUNDED' &&
					hasConcessions(order) &&
					existingOrdersMap.has(order.bookingProcessId) &&
					existingOrdersMap.get(order.bookingProcessId).status === 'PREPARATION'
				) {
					refundable = false;
					status = 'PREPARATION';
				}

				//Update existing Ticket
				await db.query(sql`
				UPDATE priv.p_ticket SET
					showtime_id = COALESCE(${screeningId},showtime_id),
					user_id =${this.dbId},
					seats_info=${JSON.stringify(order.seats)},
					attached_vouchers=${JSON.stringify(order.vouchers)},
					qr_code = ${order.qrCode},
					bought_at=${order.boughtAt},
					refundable=	${refundable},
					status=${status},
					movie_id=${movieId},
					screening_datetime=${order.screeningDatetime},
					auditorium_name=${order.auditoriumName},
					cinema_id=${order.cinemaId},
					movie_title=${order.movieTitle},
					google_pay_pass=${order.googlePayPass}
				WHERE 
				 cp_booking_id=${order.bookingProcessId}

			`);
			} else {
				// Add new ticket
				// eslint-disable-next-line no-await-in-loop
				await db.query(sql`
					INSERT INTO priv.p_ticket (
						showtime_id,
						user_id,
						seats_info,
						attached_vouchers,
						qr_code,
						cp_booking_id,
						bought_at,
						refundable,
						status,
						movie_id,
						screening_datetime,
						auditorium_name,
						cinema_id,
						movie_title,
						google_pay_pass
					) VALUES (
						${screeningId},
						${this.dbId},
						${JSON.stringify(order.seats)},
						${JSON.stringify(order.vouchers)},
						${order.qrCode},
						${order.bookingProcessId},
						${order.boughtAt},
						${order.refundable},
						${order.status},
						${movieId},
						${order.screeningDatetime},
						${order.auditoriumName},
						${order.cinemaId},
						${order.movieTitle},
						${order.googlePayPass}
					) ON CONFLICT (cp_booking_id) DO UPDATE SET
						showtime_id = ${screeningId},
						user_id = ${this.dbId},
						seats_info = ${JSON.stringify(order.seats)},
						attached_vouchers=${JSON.stringify(order.vouchers)},
						qr_code = ${order.qrCode},
						cp_booking_id = ${order.bookingProcessId},
						bought_at = ${order.boughtAt},
						refundable=${order.refundable},
						status=${order.status},
						movie_id=${movieId},
						screening_datetime=${order.screeningDatetime},
						auditorium_name=${order.auditoriumName},
						cinema_id=${order.cinemaId},
						movie_title=${order.movieTitle},
						google_pay_pass=${order.googlePayPass}
					RETURNING id
				`);
			}
			if (hasConcessions(order)) {
				const lineItems = JSON.stringify(order.concessions.lineItems);
				if (existingOrdersMap.has(order.bookingProcessId)) {
					const existingOrder = existingOrdersMap.get(order.bookingProcessId);
					let orderStatus = order.concessions.status;

					// The PREPARATION status should only be overwritten if the Order has been refunded (e.g. manually via the cms)

					if (order.concessions.status !== 'REFUNDED' && existingOrder.status === 'PREPARATION') {
						orderStatus = 'PREPARATION';
					}

					//Update existing Ticket
					// Attention: We do not update the "cinema_id" here because it is not possible to change
					// and if the cinema is deleted it is set null. Overwriting it with an id again crashes the query
					// for a key constraint violation
					await db.query(sql`
				UPDATE priv.p_order SET
					screening_id = ${screeningId},
					user_id =${this.dbId},
					qr_code = ${order.concessions.orderNumber},
					datetime=${order.concessions.datetime},
					refundable=	${existingOrder.status === 'PREPARATION' ? false : order.refundable},
					status=${orderStatus},
					transaction_id=${order.concessions.transactionId},
					line_items = ${lineItems}
				WHERE 
				 foreign_order_id=${order.bookingProcessId}

			`);
				} else {
					// We need to make sure the cinema (still) exists, otherwise the insert fails.
					// We cannot use ON CONFLICT, because it is only possible to check for one constraint there.
					let cinemaId = null;
					const cinemaIdRes = await db.query(
						sql`SELECT count(*) FROM priv.p_cinema WHERE id = ${order.cinemaId}`
					);
					if (cinemaIdRes.rows[0].count > 0) {
						cinemaId = order.cinemaId;
					}

					// Add new ticket

					// Attention: We do not update the "cinema_id" here because it is not possible to change
					// and if the cinema is deleted it is set null. Overwriting it with an id again crashes the query
					// for a key constraint violation

					// eslint-disable-next-line no-await-in-loop
					await db.query(sql`
					INSERT INTO priv.p_order (
					foreign_order_id,
					screening_id,
					cinema_id,
					user_id,
					qr_code,
					datetime,
					refundable,
					status,
					transaction_id,
					line_items
					) VALUES (
						${order.bookingProcessId},
						${screeningId},
						${cinemaId},
						${this.dbId},
						${order.concessions.orderNumber},
						${order.concessions.datetime},
						${order.refundable},
						${order.concessions.status},
						${order.concessions.transactionId},
						${lineItems}
					) ON CONFLICT (foreign_order_id) DO UPDATE SET
							screening_id = ${screeningId},
							user_id =${this.dbId},
							qr_code = ${order.concessions.orderNumber},
							datetime=${order.concessions.datetime},
							refundable=	${order.refundable},
							status=${order.concessions.status},
							transaction_id=${order.concessions.transactionId},
							line_items = ${lineItems}
					RETURNING id
				`);
				}
			}
		}
	}

	async syncCinemas(requestId: string): Promise<void> {
		// Handle unsynced cinemas
		const unsyncedCinemas = await db.query(sql`
			SELECT cinema_id, deselected_datetime
			FROM priv.p_user_cinema
			WHERE user_id = ${this.dbId} AND synced = FALSE;
		`);

		await Promise.all(
			unsyncedCinemas.rows.map(async ({ cinema_id, deselected_datetime }) => {
				try {
					if (deselected_datetime) {
						await this.deselectCinema(cinema_id, requestId);
					} else {
						await this.selectCinema(cinema_id, requestId);
					}

					// Mark as synced in local database
					await db.query(sql`
					UPDATE priv.p_user_cinema
					SET synced = TRUE
					WHERE user_id = ${this.dbId} AND cinema_id = ${cinema_id};
				`);
				} catch (e) {
					console.error(`Failed to sync cinema_id ${cinema_id}:`, e);
				}
			})
		);

		const centerRes = await this.request<{ centerId: string }[]>(
			'GET',
			'/preferredCenters',
			requestId
		);

		if (centerRes.data) {
			const data = centerRes.data;
			const selectedCinemaDbIds = data
				.map(({ centerId }) => Number(centerId))
				.filter((dbId) => !isNaN(dbId));
			const res = await db.query(sql`
				SELECT cinema_id
				FROM priv.p_user_cinema JOIN priv.p_cinema c ON c.id=cinema_id 
				WHERE user_id=${this.dbId} AND deselected_datetime IS NULL
			`);

			await Promise.all([
				...selectedCinemaDbIds.map(async (cinemaDbId) => {
					if (res.rows.findIndex(({ cinema_id }) => cinema_id === cinemaDbId) < 0) {
						// cinema should be seleted
						try {
							await db.query(sql`
							INSERT INTO priv.p_user_cinema (
								user_id, cinema_id, selected_datetime
							) VALUES (
								${this.dbId}, ${cinemaDbId}, now()
							) ON CONFLICT DO NOTHING;
						`);
						} catch (e) {
							// Sometimes there are accounts that have cinemas selected that are not present in our database.
							// This leads to a foreign key error on insert. We want to ignore these errors
							if (e.code !== '23503' || e.constraint !== 'p_user_cinema_cinema_id_fkey') {
								throw e;
							}
						}
					}
				}),
				...res.rows.map(async ({ cinema_id }) => {
					if (!selectedCinemaDbIds.includes(cinema_id)) {
						// cinema should not be selected
						await db.query(sql`
							UPDATE priv.p_user_cinema SET deselected_datetime = now()
							WHERE 
								user_id=${this.dbId} AND 
								cinema_id=${cinema_id} AND 
								deselected_datetime IS NULL
						`);
					}
				}),
			]);
		} else {
			if (centerRes.status === 401 && centerRes.error === 'OUTDATED_REFRESH_TOKEN') {
				throw new ForbiddenError('OUTDATED_REFRESH_TOKEN');
			} else {
				this.handleUnexpectedError(centerRes.error, requestId);
			}
		}
	}

	async syncWatchlist(): Promise<void> {
		// Handle unsynced data
		const unsyncedEntries = await db.query(sql`
			SELECT movie_id, interest_value
			FROM priv.p_movie_interest
			WHERE user_id = ${this.dbId} AND synced = FALSE AND valid = TRUE;
		`);

		await Promise.all(
			unsyncedEntries.rows.map(async ({ movie_id, interest_value }) => {
				try {
					if (interest_value === 1) {
						// Add movie to Cineplex watchlist
						await this.addOrRemoveFromWatchlist({ movieIdDb: movie_id, operationType: 'ADD' });
					} else {
						// Remove movie from Cineplex watchlist
						await this.addOrRemoveFromWatchlist({ movieIdDb: movie_id, operationType: 'REMOVE' });
					}
					// Mark entry as synced
					await db.query(sql`
						UPDATE priv.p_movie_interest
						SET synced = TRUE
						WHERE user_id = ${this.dbId} AND movie_id = ${movie_id};
					`);
				} catch (e) {
					console.error(`Failed to sync movie_id ${movie_id}:`, e);
				}
			})
		);

		let data: GetWatchlistData;
		try {
			data = await getWatchlist({ cpAccessToken: this.accessToken });
		} catch (e) {
			// The Krankikom Customer Service throws an unauthenticated error if the access token is outdated. In this
			// case we want to refresh the access token and retry the sync
			if (e instanceof AuthenticationError) {
				await this.refreshAccessToken();
				data = await getWatchlist({ cpAccessToken: this.accessToken });
			} else {
				throw e;
			}
		}

		const selectedCineplexFilmInfoIds = data
			.filter(({ type }) => type === 'FILM')
			.map(({ dataId }) => String(dataId));
		console.log('selectedCineplexFilmInfoIds', selectedCineplexFilmInfoIds);
		const res = await db.query(sql`
					SELECT m.movie_id, foreign_id
					FROM priv.p_movie_interest i 
					JOIN priv.p_foreign_movie_identifier m ON m.movie_id=i.movie_id 
					WHERE 
						user_id=${this.dbId} AND 
						valid=true AND 
						interest_value=1 AND 
						foreign_id_type='CINEPLEX'
			`);
		await Promise.all([
			...selectedCineplexFilmInfoIds.map(async (filmInfoId) => {
				if (res.rows.findIndex(({ foreign_id }) => foreign_id === filmInfoId) < 0) {
					// movie should be on watchlist
					const resInsert = await db.queryOne(sql`
							INSERT INTO priv.p_movie_interest(
								user_id, movie_id, interest_value, datetime, valid
							) (
								SELECT ${this.dbId}, movie_id, 1, now(), true 
								FROM priv.p_foreign_movie_identifier 
								WHERE foreign_id=${filmInfoId}
							) ON CONFLICT DO NOTHING 
							RETURNING movie_id, datetime;
						`);
					if (resInsert) {
						const { movie_id, datetime } = resInsert;
						await db.query(
							`UPDATE priv.p_movie_interest	SET valid=false 
							WHERE user_id = $1 AND movie_id = $2 AND datetime < $3;`,
							[this.dbId, movie_id, datetime]
						);
					}
				}
			}),
			...res.rows.map(async ({ movie_id, foreign_id }) => {
				if (!selectedCineplexFilmInfoIds.includes(foreign_id)) {
					// movie should not be on watchlist
					await db.query(
						`UPDATE priv.p_movie_interest	SET valid=false 
							WHERE user_id = $1 AND movie_id = $2;`,
						[this.dbId, movie_id]
					);
					await db.query(
						`INSERT INTO priv.p_movie_interest(
								user_id, movie_id, interest_value, datetime, valid
							) VALUES ($1, $2, 0, now(), true) ON CONFLICT DO NOTHING;`,
						[this.dbId, movie_id]
					);
				}
			}),
		]);
	}

	async sendExistingDataAfterRegister(requestId: string): Promise<void> {
		const selectedCinemas = await db.query(
			sql`
				SELECT 
					cinema_id 
				FROM 
					priv.p_user_cinema 
				WHERE 
					user_id=${this.dbId}
					AND 
					deselected_datetime IS NULL;
			`
		);
		const watchlistedMovies = await db.query(
			sql`
				SELECT 
					movie_id 
				FROM 
					priv.p_movie_interest 
				WHERE 
					user_id=${this.dbId}
					AND 
					valid = TRUE
					AND 
					interest_value=1
					;
			`
		);

		await Promise.all([
			...selectedCinemas.rows.map(async (r) => await this.selectCinema(r.cinema_id, requestId)),
			...watchlistedMovies.rows.map(
				async (r) =>
					await this.addOrRemoveFromWatchlist({ movieIdDb: r.movie_id, operationType: 'ADD' })
			),
		]);

		await db.query(
			sql`
			UPDATE
				priv.p_user
			SET
				cp_initial_sync = TRUE
			WHERE
				id = ${this.dbId}
			`
		);
		this.initialSync = true;
	}

	syncAll(force = false, requestId: string): Promise<void> {
		return new Promise(async (resolve, reject) => {
			console.log('Sync 1');
			let syncId = null;

			try {
				if (!this.initialSync) {
					await this.sendExistingDataAfterRegister(requestId);
				}

				if (!force && this.lastSync && Date.now() - this.lastSync?.getTime() < SYNC_INTERVAL) {
					//Everything is up to date
					return resolve();
				}

				//Make sure this.lastSync date is still up to date to avoid unnecessary syncs
				const lastSyncDb = (
					await db.queryOne(sql`SELECT cp_last_sync FROM priv.p_user WHERE id=${this.dbId}`)
				).cp_last_sync;
				this.lastSync = lastSyncDb;
				if (!force && this.lastSync && Date.now() - this.lastSync?.getTime() < SYNC_INTERVAL) {
					return resolve();
				}

				// const mutex = new Mutex(redisClient, 'lockingResource', {
				// 	acquireAttemptsLimit: 1
				// })

				const res = await db.query(
					sql`INSERT INTO priv.p_user_sync(user_id, started_at) VALUES (${this.dbId}, now()) RETURNING id`
				);
				const syncId = res.rows[0].id;
				const oldestSyncId = (
					await db.queryOne(sql`
				SELECT id 
				FROM priv.p_user_sync 
				WHERE user_id = ${this.dbId} 
					AND started_at > now() - interval '1 minute'
					AND finished_at IS NULL
				ORDER BY started_at ASC LIMIT 1`)
				).id;
				if (oldestSyncId !== syncId) {
					await db.query(
						sql`UPDATE priv.p_user_sync SET finished_at=now(), success=false, error='Other Sync running' WHERE id=${syncId}`
					);

					return resolve();
				} else {
					if (ENV !== 'development') {
						//The sync with cineplex might take quite a while, but we do not want to block the api request for that time
						//To allow the sync to finish even after the qraphql query returned, we need to run it in a seperate lambda.
						const invokCommand = new InvokeCommand({
							FunctionName: SYNC_USER_FUNCTION_NAME,
							Payload: JSON.stringify({
								requestId,
								syncId,
								userIdDb: this.dbId,
							}),
						});

						lambdaClient.send(invokCommand, async (err, data) => {
							console.log('Sync 11');
							console.log('Lambda invoked', err, data);
							if (!err) {
								return resolve();
							} else {
								return reject(err);
							}
						});
					} else {
						await this.asyncCineplexCustomerSync(requestId, syncId);
						return resolve();
					}
				}
			} catch (e) {
				console.log('Error in syncAll: ' + e);
				reject(e);
			}
		});
	}
	async asyncCineplexCustomerSync(requestId: string, syncId: string): Promise<boolean> {
		// We need to check if there is already a sync running for this user. If so, we do not want to start another one.
		const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

		try {
			console.log('Sleep started');
			await sleep(5000);
			console.log('Sleep finished');

			await this.refreshAccessToken();

			await Promise.all([
				this.syncProfile(requestId),
				this.syncCinemas(requestId),
				this.syncWatchlist(),
				this.syncTickets(),
			]);
			await db.query(`UPDATE priv.p_user SET cp_last_sync=now() WHERE cp_id=$1`, [this.cpId]);
			await db.query(
				sql`UPDATE priv.p_user_sync SET finished_at=now(), success=true WHERE id=${syncId}`
			);

			return true;
		} catch (e) {
			console.log('Error in asyncCineplexCustomerSync: ' + e);
			await db.query(
				sql`UPDATE priv.p_user_sync SET finished_at=now(), success=false, error=${e} WHERE id=${syncId}`
			);
			sendErrorMail('An error occured in asyncCineplexCustomerSync', e);
			return false;
		}
	}

	async recordConsent({ key, version, ipAddress, accepted, text }, requestId: string) {
		const date = formatCineplexDate(new Date());
		const { status, error } = await this.request('PATCH', '/', requestId, {
			lastUpdatedBy: 'APP',
			consents: [{ key, version, ipaddress: ipAddress, date, text: text || 'APP', accepted }],
		});

		if (status !== 200) {
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'RecordConsent',
						cineplexCustomerId: this.dbId,
						consents: JSON.stringify({
							key,
							version,
							ipAddress,
							accepted,
						}),
						errorResponse: error,
					},
					null,
					2
				),
				requestId
			);
		}
	}

	async joinBonusProgram(
		requestId: string,
		voucherCode?: string,
		voucherCodes?: string[],
		context?: Context
	): Promise<'NEW' | 'EXISTING'> {
		// marketing emails are part of the bonus program terms
		await optInToMarketingEmails(null, { rejected: false, token: '' }, context);

		const params: { loyaltyScheme: number; migrationCardNumber?: string } = {
			loyaltyScheme: 30002,
		};
		const { status, error, data } = await this.request<CustomerCard>(
			'POST',
			'/customercards',
			requestId,
			params
		);
		if (status === 201) {
			const { customerCardNumber, customerId } = data;
			const qrCode = `{"CustomerCard": {"CardNumber": "${customerCardNumber}","ID": "${customerId}"}}`;
			await db.query(`UPDATE priv.p_user SET qr_code=$2 WHERE id=$1`, [this.dbId, qrCode]);

			if (voucherCode || voucherCodes) {
				await this.redeemVoucherCode(customerCardNumber, requestId, voucherCode, voucherCodes);
			}
			return 'NEW';
		} else {
			//-> can not be tested for now
			if (status === 400) {
				const err = JSON.parse(error);
				const errorCode = JSON.parse(err.response)[0].errorCode;
				if (errorCode === 100007) {
					// customer has alredady joined, likely a race condition, simply treat as success
					return 'EXISTING';
				}
			}
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'JoinBonusProgram',
						cineplexCustomerId: this.dbId,
						errorResponse: error,
					},
					null,
					2
				),
				requestId
			);
		}
	}

	async selectCinema(cinemaIdDb: InternalCinemaId, requestId: string): Promise<void> {
		const { status, error } = await this.request('POST', '/preferredCenters', requestId, {
			centerId: String(cinemaIdDb),
		});
		if (status !== 200 && status !== 201 && status !== 409) {
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'SelectCinema',
						cineplexCustomerId: this.dbId,
						cinemaIdDb,
						status,
						error,
					},
					null,
					2
				),
				requestId
			);
		}
	}

	async deselectCinema(cinemaIdDb: InternalCinemaId, requestId: string): Promise<void> {
		const { status, error } = await this.request(
			'DELETE',
			`/preferredCenters/${cinemaIdDb}`,
			requestId
		);
		if (status !== 200 && status !== 404) {
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'DeselectCinema',
						cineplexCustomerId: this.dbId,
						cinemaIdDb,
						status,
						error,
					},
					null,
					2
				),
				requestId
			);
		}
	}

	async addOrRemoveFromWatchlist({
		movieIdDb,
		operationType,
		context,
	}: {
		movieIdDb: InternalMovieId;
		operationType: 'ADD' | 'REMOVE';
		context?: Context;
	}): Promise<void> {
		const res = await db.query(
			`SELECT foreign_id FROM priv.p_foreign_movie_identifier
			WHERE movie_id = $1 AND foreign_id_type = 'CINEPLEX'`,
			[movieIdDb]
		);
		if (res.rows.length && res.rows[0].foreign_id) {
			await addOrRemoveFromWatchlist(
				{
					cpAccessToken: this.accessToken,
					dataId: parseInt(res.rows[0].foreign_id, 10),
					type: 'FILM',
					remove: operationType === 'REMOVE',
				},
				context
			);
		} else {
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'AddToWatchlist',
						cineplexCustomerId: this.dbId,
						movieIdDb,
						errorResponse: 'MISSING_FILM_INFOS_ID_FOR_MOVIE',
					},
					null,
					2
				),
				context?.viewer.requestId
			);
		}
	}

	async bookStatusPoints(
		{
			amount,
			description,
			compesoMovieId,
			noSync,
		}: { amount: number; description: string; compesoMovieId?: string; noSync?: boolean },
		requestId: string
	) {
		const { data, error } = await this.request<Customer>('GET', '?includeCardData=true', requestId);
		if (error || !data) {
			//-> can not be tested (Compeso-System-Failure)
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'BookStatusPoints',
						cineplexCustomerId: this.dbId,
						amount,
						bookingDescription: description,
						compesoMovieId,
						errorResponse: 'COMPESO_ERROR',
						error,
					},
					null,
					2
				),
				requestId
			);
		}
		const cards = data.customerCards;

		if (cards.length > 1) {
			//-> can not be tested for now - impossible to run into
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'BookStatusPoints',
						cineplexCustomerId: this.dbId,
						amount,
						bookingDescription: description,
						compesoMovieId,
						errorResponse: 'USER_HAS_MULTIPLE_CUSTOMER_CARDS',
					},
					null,
					2
				),
				requestId
			);
		}
		if (cards.length < 1) {
			//USER_NOT_A_BONUSPROGRAM_MEMBER
			return;
		}
		const { customerCardNumber } = cards[0];
		const res = await this.request(
			'POST',
			`/customercards/${customerCardNumber}/postings/`,
			requestId,
			{
				accountNo: 1000,
				isCancellation: false,
				bookingDate: formatCineplexDate(new Date()),
				amount,
				currencyCode: 'EUR',
				postingType: 1,
				postingText: description.substr(0, 50),
				remoteCenterNo: 90001,
				remoteWorkstationId: 'C9710000028FWBXJYB',
				filmId: compesoMovieId,
				rentrakNo: null,
				value: 0,
			}
		);

		//-> can not be tested for now
		if (res.error || res.status !== 201) {
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'BookStatusPoints',
						cineplexCustomerId: this.dbId,
						amount,
						bookingDescription: description,
						compesoMovieId,
						errorResponse: JSON.stringify(res, null, 2),
						status: res.status,
					},
					null,
					2
				),
				requestId
			);
		}
		if (!noSync) {
			await this.syncProfile(requestId);
		}
	}

	async leaveBonusProgram(requestId: string): Promise<void> {
		const { data, error } = await this.request<Customer>('GET', '?includeCardData=true', requestId);
		if (error || !data) {
			//-> can not be tested for now
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'LeaveBonusProgram',
						cineplexCustomerId: this.dbId,
						errorResponse: 'COMPESO_ERROR',
					},
					null,
					2
				),
				requestId
			);
		}
		const cards = data.customerCards;

		if (cards.length > 1) {
			//-> can not be tested for now - impossible to run into
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'LeaveBonusProgram',
						cineplexCustomerId: this.dbId,
						errorResponse: 'USER_HAS_MULTIPLE_CUSTOMER_CARDS',
					},
					null,
					2
				),
				requestId
			);
		}
		if (cards.length < 1) {
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'LeaveBonusProgram',
						cineplexCustomerId: this.dbId,
						errorResponse: 'USER_NOT_A_BONUSPROGRAM_MEMBER',
					},
					null,
					2
				),
				requestId
			);
		}
		const { customerCardNumber } = cards[0];
		const delRes = await this.request(
			'POST',
			`/customercards/${customerCardNumber}/unsubscribe`,
			requestId,
			{ comment: 'Just because...' }
		);
		if (delRes.status !== 200) {
			//-> can not be tested for now
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'LeaveBonusProgram',
						cineplexCustomerId: this.dbId,
						errorResponse: delRes.error,
						status: delRes.status,
					},
					null,
					2
				),
				requestId
			);
		}
		await this.deleteBonusProgramMembershipData();
	}

	async deleteBonusProgramMembershipData() {
		await db.query(
			sql`
				DELETE FROM 
					priv.p_bonus_program_membership 
				WHERE 
					user_id = ${this.dbId} 
					AND 
					bonus_program_id = 1
				`
		);
		await db.query(
			sql`
				DELETE FROM 
					priv.p_voucher 
				WHERE 
					user_id = ${this.dbId} 
				`
		);
		await db.query(
			sql`
				DELETE FROM 
					priv.p_bonus_point_booking 
				WHERE 
					user_id = ${this.dbId} 
					AND 
					currency_id = 1
				`
		);
	}

	async redeemLongtermBenefit(
		voucherClassId: InternalVoucherClassId,
		requestId: string
	): Promise<void> {
		//Book voucher via compeso api
		const { data, error } = await this.request<Customer>('GET', '?includeCardData=true', requestId);
		if (error || !data) {
			//-> can not be tested for now
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'RedeemLongtermBenefit',
						cineplexCustomerId: this.dbId,
						voucherClassId,
						errorResponse: 'COMPESO_ERROR',
					},
					null,
					2
				),
				requestId
			);
		}
		const cards = data.customerCards;

		if (cards.length > 1) {
			//-> can not be tested for now - impossible to run into
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'RedeemLongtermBenefit',
						cineplexCustomerId: this.dbId,
						voucherClassId,
						errorResponse: 'USER_HAS_MULTIPLE_CUSTOMER_CARDS',
					},
					null,
					2
				),
				requestId
			);
		}
		if (cards.length < 1) {
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'RedeemLongtermBenefit',
						cineplexCustomerId: this.dbId,
						voucherClassId,
						errorResponse: 'USER_NOT_A_BONUSPROGRAM_MEMBER',
					},
					null,
					2
				),
				requestId
			);
		}
		const { customerCardNumber } = cards[0];
		if (voucherClassId !== 5 && voucherClassId !== 4) {
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'RedeemLongtermBenefit',
						cineplexCustomerId: this.dbId,
						voucherClassId,
						errorResponse: 'UNKNOWN_VOUCHER_CLASS',
					},
					null,
					2
				),
				requestId
			);
		}
		const res = await this.request(
			'POST',
			`/customercards/${customerCardNumber}/bonusaction`,
			requestId,
			{ bonusActionName: voucherClassId === 5 ? 'BookPrivateScreening' : 'BookFreeTicketMonth' }
		);
		if (res.status !== 200) {
			if (res.status === 400) {
				// Probably not enough points
				this.handleUnexpectedError(
					JSON.stringify(
						{
							description: 'Error on CineplexCustomerService',
							method: 'RedeemLongtermBenefit',
							cineplexCustomerId: this.dbId,
							voucherClassId,
							errorResponse: 'NOT_ENOUGH_POINTS_OR_ALREADY_BOOKED',
							status: res.status,
						},
						null,
						2
					),
					requestId
				);
			}
			//-> can not be tested for now - compeso system
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: 'RedeemLongtermBenefit',
						cineplexCustomerId: this.dbId,
						voucherClassId,
						errorResponse: 'BOOKING_FAILED',
						status: res.status,
					},
					null,
					2
				),
				requestId
			);
		}
		// Force sync of Bonusprogram data of the user
		await this.syncProfile(requestId);
	}

	async redeemVoucherCode(
		customerCardNumber: string,
		requestId: string,
		voucherCode?: string,
		voucherCodes?: string[]
	): Promise<boolean> {
		const { data, error, status } = await this.request<
			(
				| { accountNo: number; expectedPointAmount: number; centerNo: number; issueDate: string }
				| { errorCode: number }
			)[]
		>(
			'POST',
			`/customercards/${customerCardNumber}/customerCampaign/`,
			requestId,
			voucherCodes || [voucherCode]
		);
		if (error || !data) {
			this.handleUnexpectedError(
				JSON.stringify(
					{
						description: 'Error on CineplexCustomerService',
						method: `/customercards/${customerCardNumber}/customerCampaign/`,
						cineplexCustomerId: this.dbId,
						errorResponse: 'COMPESO_ERROR',
						error,
					},
					null,
					2
				),
				requestId,
				false
			);
		}

		if (status !== 200) {
			return false;
		}
		return true;
	}
}

if (!customerCache) {
	// eslint-disable-next-line no-var
	var customerCache: Map<InternalUserId, CineplexCustomer> = new Map();
}
const execWithCineplexCustomer = async <K>(
	userIdDb: InternalUserId,
	execF: (c: CineplexCustomer) => Promise<K>
): Promise<K> => {
	let cpCustomer: CineplexCustomer | null;
	if (!customerCache.has(userIdDb)) {
		cpCustomer = await CineplexCustomer.gen(userIdDb);
		if (cpCustomer) {
			customerCache.set(userIdDb, cpCustomer);
		}
	} else {
		cpCustomer = customerCache.get(userIdDb);
	}
	if (cpCustomer) {
		return await execF(cpCustomer);
	}
};

// when cineplex auth values are changed in the DB we must invalidate the cache
export const cineplexCustomerAuthChanged = (userIdDb: InternalUserId): void => {
	if (customerCache.has(userIdDb)) {
		customerCache.delete(userIdDb);
	}
};

export const asyncCineplexCustomerSync = async (
	userIdDb: InternalUserId,
	requestId: string,
	syncId: string
): Promise<void> => {
	//This function is called via a lambda invoke, to allow the user sync to be async
	//The sync with cineplex might take quite a while, but we do not want to block the api request for that time
	//To allow the sync to finish even after the qraphql query returned, we need to run it in a seperate lambda.

	await execWithCineplexCustomer(userIdDb, (c) => c.asyncCineplexCustomerSync(requestId, syncId));
};

export const syncIfCineplexCustomer = async (
	userIdDb: InternalUserId,
	force = false,
	viewer?: Viewer
): Promise<void> => {
	await execWithCineplexCustomer(userIdDb, (c) => c.syncAll(force, viewer?.requestId || uuid()));
};

let forceSyncCineplexTicketsPromise;
//cant be tested for now
export const forceSyncCineplexTickets = async (userIdDb: InternalUserId): Promise<void> => {
	// This method is called by Tickets & Orders. To avoid race conditions we must make sure that it is only executed once
	if (!forceSyncCineplexTicketsPromise) {
		forceSyncCineplexTicketsPromise = execWithCineplexCustomer(userIdDb, (c) => c.syncTickets());
	}
	await forceSyncCineplexTicketsPromise;
	forceSyncCineplexTicketsPromise = null;
};

export const recordCineplexConsent = async ({
	key,
	userIdDb,
	version,
	ipAddress,
	accepted = true,
	viewer,
	text,
}: {
	key:
		| 'TERMS_AND_CONDITIONS'
		| 'ADVERTISEMENT'
		| 'LOYALTY_PROGRAM'
		| 'DATA_USAGE_DECLARATION'
		| 'AGE_16'
		| 'PUSH_NOTIFICATION'
		| 'PUSH_CINEPLEX_PLUS'
		| 'NEWSLETTER_ID_221'
		| 'NEWSLETTER_ID_222';
	userIdDb: InternalUserId;
	version: number;
	ipAddress: string;
	accepted?: boolean;
	viewer?: Viewer;
	text?: string;
}): Promise<void> => {
	await execWithCineplexCustomer(userIdDb, (c) =>
		c.recordConsent({ version, ipAddress, key, accepted, text }, viewer?.requestId || uuid())
	);
};

export const joinCineplexBonusProgram = async (
	userIdDb: InternalUserId,
	voucherCode?: string,
	voucherCodes?: string[],
	context?: Context
) => {
	return await execWithCineplexCustomer(userIdDb, (c) =>
		c.joinBonusProgram(context?.viewer?.requestId || uuid(), voucherCode, voucherCodes, context)
	);
};

export const selectCineplexCinema = async (
	userIdDb: InternalUserId,
	cinemaIdDb: InternalCinemaId,
	viewer?: Viewer
): Promise<void> => {
	await execWithCineplexCustomer(userIdDb, (c) =>
		c.selectCinema(cinemaIdDb, viewer?.requestId || uuid())
	);
};

export const deselectCineplexCinema = async (
	userIdDb: InternalUserId,
	cinemaIdDb: InternalCinemaId,
	viewer?: Viewer
): Promise<void> => {
	await execWithCineplexCustomer(userIdDb, (c) =>
		c.deselectCinema(cinemaIdDb, viewer?.requestId || uuid())
	);
};

export const addOrRemoveMovieFromCineplexWatchlist = async ({
	userIdDb,
	movieIdDb,
	operationType,
	context,
}: {
	userIdDb: InternalUserId;
	movieIdDb: InternalMovieId;
	operationType: 'ADD' | 'REMOVE';
	context?: Context;
}): Promise<void> => {
	await execWithCineplexCustomer(userIdDb, (c) =>
		c.addOrRemoveFromWatchlist({ movieIdDb, operationType, context })
	);
};

export const bookStatusPoints = async (
	userIdDb: InternalUserId,
	amount: number,
	description: string,
	compesoMovieId?: string,
	viewer?: Viewer,
	noSync?: boolean
): Promise<void> => {
	await execWithCineplexCustomer(userIdDb, (c) =>
		c.bookStatusPoints({ amount, description, compesoMovieId, noSync }, viewer?.requestId || uuid())
	);
};

export const leaveCineplexBonusProgram = async (
	userIdDb: InternalUserId,
	viewer?: Viewer
): Promise<void> => {
	await execWithCineplexCustomer(userIdDb, (c) => c.leaveBonusProgram(viewer?.requestId || uuid()));
};

export const reedeemLongtermBenefit = async (
	userIdDb: InternalUserId,
	voucherClassId: InternalVoucherClassId,
	viewer?: Viewer
): Promise<void> => {
	await execWithCineplexCustomer(userIdDb, (c) =>
		c.redeemLongtermBenefit(voucherClassId, viewer?.requestId || uuid())
	);
};

//-> cannot be tested in jest tests
export const bookInvitePoints = async (
	userIdDb: InternalUserId,
	bookingDate: Date
): Promise<void> => {
	const joinedAt: Date =
		(
			await db.queryOne(sql`
				SELECT min(datetime) as "joinedAt" 
				FROM priv.p_bonus_point_booking 
				WHERE 
					point_type = 'STATUS_LEVEL' 
					AND 
					user_id = ${userIdDb} 
					AND 
					amount = 1;
				`)
		).joinedAt || new Date();

	if (bookingDate <= joinedAt) {
		// This is a migrated booking and thus should not reedem the invite
		return;
	}

	const numBookings = await db.queryOne(sql`
	SELECT count(*) FROM priv.p_bonus_point_booking WHERE user_id = ${userIdDb} AND point_type='BONUS_POINTS' AND datetime > ${joinedAt}`);

	if (Number(numBookings.count) === 0) {
		//This is the first Booking for ticket points. If this user has been invited by another user, that user should
		//get the points for the invite.
		const inviteRes = await db.query(sql`
		SELECT id, invited_by_user_id 
		FROM priv.p_invite 
		WHERE new_user_id = ${userIdDb} AND points_booked = FALSE
	`);
		const i18n = getFixedI18n('de-cineplex');
		if (inviteRes.rows.length) {
			//Book points
			// setting points_booked true and in case of an catch error setting it back to false is intentional to prevent booking invite more than once
			// it might happen that this bookInviteFunction gets called multiple times (very fast one after another -> race conditions)
			await db.query(sql`
				UPDATE priv.p_invite SET points_booked = TRUE WHERE id = ${inviteRes.rows[0].id}
			`);

			try {
				await bookStatusPoints(
					inviteRes.rows[0].invited_by_user_id,
					100,
					i18n.t('BonusProgramView.invitePointsBookingTextMax50Char'),
					undefined,
					undefined,
					true
				);
			} catch (e) {
				await db.query(
					sql`UPDATE priv.p_invite SET points_booked = FALSE WHERE id = ${inviteRes.rows[0].id}`
				);
				sendErrorMail(
					'Could not book points for invite',
					`The user ${userIdDb} received ticket points for the first time and was invited by ${
						inviteRes.rows[0].invited_by_user_id
					}, but we failed to book the invite points.  Got error: ${printError(e)}`
				);
			}
		}
	}
};
