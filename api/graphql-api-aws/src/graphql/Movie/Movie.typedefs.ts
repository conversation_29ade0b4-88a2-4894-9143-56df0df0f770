import { gql } from 'apollo-server-core';

export const typeDef = gql`
	extend type Query {
		movie(id: ID!): Movie
		movies(ids: [ID!]!): [Movie!]!
		allMovies(isFromMasterFilmDB: Boolean): [Movie!]!
		similarMovies(movieId: ID!, cinemaIds: [ID!]): [Movie!]!
		searchMovie(title: String!): [Movie!]!
		screenedMovies(cinemaIds: [ID!]!, from: DateTime, to: DateTime): [screenedMoviesReturnType!]!
		incomingForeignMovieIdentifiers(cinemaIds: [ID!]!, force: Boolean): [ForeignMovieIdentifier!]!
	}
	type screenedMoviesReturnType {
		id: ID!
		movie: Movie!
		firstScreening: DateTime!
	}

	enum MovieDepartment {
		ACTING
		DIRECTING
		WRITING
	}

	type Credit {
		id: ID!
		department: MovieDepartment
		role: String
		person: CastOrCrewMember
	}

	type CinemaSpecificContent {
		cinema: Cinema!
		teaser: String
		attributes: [ScreeningAttribute!]!
		week: Int
	}

	type Movie {
		id: ID!
		title: String
		banner: String
		poster: String
		directedBy: String
		synopsis: String
		duration: Int
		nonMovieEvent: Boolean
		averageRating: Float
		releaseDate: Date
		youtubeTrailerId: String
		ageRating: String
		genres: [String!]!
		credits(departments: [MovieDepartment!]): [Credit!]!
		cinemaSpecificContent(cinemaIds: [ID!]!): [CinemaSpecificContent!]!
		screenings(cinemaIds: [ID!], after: DateTime, before: DateTime): [Screening!]!
			@costFactor(value: 20)
		firstScreening(cinemaIds: [ID!]): Screening
			@deprecated(reason: "[4.1.14] use currentOrNextAvailableInCinemaDate instead")
		reviewRatingByCurrentUser: ReviewRating
		interestRatingByCurrentUser: InterestRating
		seenInCinemaByCurrentUser: SeenInCinema
		statistics(userGroupDefinition: UserGroupDefinition): MovieStatistics
		currentOrNextAvailableInCinemaDate(cinemaIds: [ID!]!): Date
		trailers: [Trailer!]!
		country: String
		screeningStatus: ScreeningStatusType
		foreignMovieId(foreignIdType: String!): ForeignIdType
		isFromMasterFilmDB: Boolean
	}

	type ForeignMovieIdentifier {
		id: ID!
		foreignIdType: String!
		title: String!
		currentMovie: Movie
	}

	input UserGroupDefinition {
		clientOfCinemasAny: [ID!]
	}

	type ForeignIdType {
		title: String
		foreignId: String
	}

	enum ScreeningStatusType {
		CURRENT
		SOON
		NONE
	}

	extend type Mutation {
		updateForeignMovieMapping(foreignId: ID!, movieId: ID!): Boolean!
	}
`;
