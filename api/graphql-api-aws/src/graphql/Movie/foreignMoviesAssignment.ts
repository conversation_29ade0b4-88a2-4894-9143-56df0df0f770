import { Context, PublicCinemaId, PublicMovieId } from '../../typescript-types';
import { db, decodePublicId, encodeDbId, sql } from '../../utils';
import { ensureIsAdmin } from '../../utils/auth';
import { Cinema } from '../Cinema/Cinema';
import { Movie } from './Movie';

export const MASTER_MOVIE_DB_ID = 100_000;

const encodeInternalForeignIdentifierId = (internalForeignId: number) =>
	encodeDbId('ForeignIdentifier', internalForeignId);
const decodePublicForeignIdentifierId = (publicForeignId: string) =>
	decodePublicId('ForeignIdentifier', publicForeignId);

export const incomingForeignMovieIdentifiers = async (
	_: unknown,
	{ cinemaIds, force = false }: { cinemaIds?: PublicCinemaId[]; force?: boolean },
	{ viewer }: Context
) => {
	ensureIsAdmin(viewer);

	const cinemaIdsDb = cinemaIds ? cinemaIds.map((id) => Cinema.decodePublicId(id)) : null;

	const now = new Date();

	const query = sql`
			WITH current_screenings AS (
				SELECT DISTINCT movie_id 
				FROM priv.p_showtime 
				WHERE datetime >= ${now}
					AND (${!cinemaIdsDb?.length} OR cinema_id = ANY(${cinemaIdsDb}))
			)
			SELECT 
				fmi.id,
				fmi.foreign_id_type,
				fmi.title,
				fmi.movie_id
			FROM priv.p_foreign_movie_identifier fmi
			JOIN current_screenings cs ON cs.movie_id = fmi.movie_id
			WHERE fmi.movie_id > ${MASTER_MOVIE_DB_ID}
			ORDER BY fmi.title ASC
		`;

	const results = await db.queryWithCache(query, { forceReload: force });

	return results.rows.map((row) => ({
		id: encodeInternalForeignIdentifierId(row.id),
		foreignIdType: row.foreign_id_type,
		title: row.title,
		currentMovie: () => (row.movie_id ? Movie.gen(viewer, Movie.encodeDbId(row.movie_id)) : null),
	}));
};

export const updateForeignMovieMapping = async (
	_: unknown,
	{ foreignId, movieId }: { foreignId: string; movieId: PublicMovieId },
	{ viewer }: Context
) => {
	ensureIsAdmin(viewer);

	const foreignIdDb = decodePublicForeignIdentifierId(foreignId);
	const movieIdDb = Movie.decodePublicId(movieId);

	if ((movieIdDb as number) > MASTER_MOVIE_DB_ID) {
		return false;
	}

	try {
		const { rowCount } = await db.query(sql`
			UPDATE priv.p_foreign_movie_identifier
			SET movie_id = ${movieIdDb}
			WHERE id = ${foreignIdDb}
		`);

		if (!rowCount) {
			return false;
		}

		return true;
	} catch {
		return false;
	}
};
