import { db, decodePublicId, encodeDbId, EntityNotFoundError, sql } from '../../utils';
import {
	CastOrCrewMember,
	Screening,
	Cinema,
	User,
	ReviewRating,
	InterestRating,
	Trailer,
} from '..';
import {
	PublicMovieId,
	InternalMovieId,
	Language,
	Viewer,
	PublicCinemaId,
	I18N,
	InternalCinemaId,
} from '../../typescript-types';
import { ScreeningAttribute, InternalScreeningAttributeId } from '../Screening/ScreeningAttribute';
import { SeenInCinema } from '../SeenInCinema/SeenInCinema';

if (!movieCache) {
	//We use var here to hoist the movie cache up, but only redeclare it once
	//eslint-disable-next-line no-var
	var movieCache: Map<PublicMovieId, Movie> = new Map();
	//eslint-disable-next-line no-var
	var inCinemaSinceCache: Map<
		string,
		{ movieIdDb: InternalMovieId; cinemaIdDb: InternalCinemaId; inCinemaSince: Date }
	> = new Map();
	// eslint-disable-next-line no-var
	var lastMovieCacheUpdate = 0;
	// eslint-disable-next-line no-var
	var refreshingMovieCache = null;
}

const movieQuery = sql`

SELECT 	

m.id,
mlm.id IS NOT NULL as "nonMovieEvent",
m.youtube_trailer_id as "youtubeTrailerId",
m.average_rating as "_average_rating_cinuru",

m.title as "_title_cinuru",
sharc.title as _title_sharc,
cinfinity.title as _title_cinfinity,
cp.title as _title_cp,
cineweb.title as _title_cineweb,
drehwerk.title as _title_drehwerk,

m.banner as "_banner_cinuru",
cinfinity.banner_filename as _banner_cinfinity,
cp.banner_filename as "_banner_cp",
cineweb.banner_filename as "_banner_cineweb",
CASE WHEN drehwerk.banner_filename is null THEN null ELSE 'https://static.cinuru.com/public/'||drehwerk.banner_filename  END as  _banner_drehwerk,

m.poster as "_poster_cinuru",
CASE WHEN sharc.poster_filename is null THEN null ELSE 'https://static.cinuru.com/public/'||sharc.poster_filename  END as  _poster_sharc,
CASE WHEN drehwerk.poster_filename is null THEN null ELSE 'https://static.cinuru.com/public/'||drehwerk.poster_filename  END as  _poster_drehwerk,
cinfinity.poster_filename  as _poster_cinfinity,
cp.poster_filename as "_poster_cp",
cineweb.poster_filename as "_poster_cineweb",

m.synopsis as "_synopsis_cinuru",
sharc.full_synopsis as _synopsis_sharc,
CASE WHEN cinfinity.teaser_text is null THEN cinfinity.full_synopsis ELSE '**'||cinfinity.teaser_text||'**\n\n'||cinfinity.full_synopsis  END as _synopsis_cinfinity,
cp.full_synopsis as _synopsis_cp,
cineweb.full_synopsis as _synopsis_cineweb,
drehwerk.full_synopsis as _synopsis_drehwerk,

m.duration as "_duration_cinuru",
sharc.duration as _duration_sharc,
cinfinity.duration as _duration_cinfinity,
tmdb.duration as _duration_tmdb,
comscore.duration as _duration_comscore,
cp.duration as _duration_cp,
cineweb.duration as _duration_cineweb,
drehwerk.duration as _duration_drehwerk,

m.release_date::text as "_release_date_cinuru",
sharc.release_date::text as _release_date_sharc,
cinfinity.release_date::text as _release_date_cinfinity,
tmdb.release_date::text as _release_date_tmdb,
comscore.release_date::text as _release_date_comscore,
cp.release_date::text as _release_date_cp,
cineweb.release_date::text as _release_date_cineweb,
drehwerk.release_date::text as _release_date_drehwerk,

array_to_json(m.genres) as _genres_cinuru,			
array_to_json(sharc.genres) as _genres_sharc,
array_to_json(cinfinity.genres) as _genres_cinfinity,
array_to_json(tmdb.genres) as _genres_tmdb,
array_to_json(cp.genres) as _genres_cp,
array_to_json(cineweb.genres) as _genres_cineweb,
array_to_json(drehwerk.genres) as _genres_drehwerk,

sharc.country as _country_sharc,
cinfinity.country as _country_cinfinity,
cp.country as _country_cp,
cineweb.country as _country_cineweb,
drehwerk.country as _country_drehwerk,

m.age_rating as "_age_rating_cinuru",			
tmdb.fsk as _age_rating_tmdb,
comscore.fsk as _age_rating_comscore,
cp.fsk as _age_rating_cp,
cineweb.fsk as _age_rating_cineweb,
drehwerk.fsk as _age_rating_drehwerk,


directors._directed_by,

cp.search_allowed as _search_allowed_cp

	FROM priv.p_movie m
	LEFT JOIN priv.p_movie_content_external sharc ON m.id = sharc.movie_id AND sharc.content_provider='SHARC'
	LEFT JOIN priv.p_movie_content_external cinfinity ON m.id = cinfinity.movie_id AND cinfinity.content_provider='CINFINITY'
	LEFT JOIN priv.p_movie_content_external tmdb ON m.id = tmdb.movie_id AND tmdb.content_provider='TMDB'
	LEFT JOIN priv.p_movie_content_external cp ON m.id = cp.movie_id AND cp.content_provider='CINEPLEX' AND cp.restrict_to_cinemas IS NULL
	LEFT JOIN priv.p_movie_content_external cineweb ON m.id = cineweb.movie_id AND cineweb.content_provider='CINEWEB' AND cineweb.restrict_to_cinemas IS NULL
	LEFT JOIN priv.p_movie_content_external drehwerk ON m.id = drehwerk.movie_id AND drehwerk.content_provider='DREHWERK'
	LEFT JOIN (
		SELECT DISTINCT ON (movie_id) * FROM priv.p_movie_release mr
		JOIN priv.p_foreign_movie_identifier fi ON mr.comscore_title_xref = fi.foreign_id AND fi.foreign_id_type = 'COMSCORE_TITLE_XREF' 
	) comscore ON comscore.movie_id = m.id	
	LEFT JOIN (
		SELECT 
			movie_id, 
			array_agg(full_name ORDER BY d.position_order ASC) as "_directed_by" 
		FROM 
			priv.p_movie_director d 
		JOIN 
			priv.p_cast_or_crew_member c ON d.person_id = c.id
		GROUP BY 
			movie_id
	) directors ON directors.movie_id = m.id
	LEFT JOIN priv.p_movie_list_movie mlm ON mlm.movie_id = m.id AND mlm.movie_list_id = 14 
`;

export class Movie {
	id: PublicMovieId;
	_cachedAt?: Date;
	youtubeTrailerId?: string;
	nonMovieEvent?: boolean;
	_average_rating_cinuru?: number;
	// title
	_title_sharc?: string;
	_title_cinfinity?: string;
	_title_cinuru?: string;
	_title_cp?: string;
	_title_cineweb?: string;
	_title_drehwerk?: string;
	// banner
	_banner_sharc?: string;
	_banner_cinfinity?: string;
	_banner_cinuru?: string;
	_banner_cp?: string;
	_banner_cineweb?: string;
	_banner_drehwerk?: string;

	// poster
	_poster_sharc?: string;
	_poster_cinfinity?: string;
	_poster_cinuru?: string;
	_poster_cp?: string;
	_poster_cineweb?: string;
	_poster_drehwerk?: string;

	// synopsis
	_synopsis_sharc?: string;
	_synopsis_cinfinity?: string;
	_synopsis_cinuru?: string;
	_synopsis_cp?: string;
	_synopsis_cineweb?: string;
	_synopsis_drehwerk?: string;

	// duration
	_duration_sharc?: string;
	_duration_cinfinity?: string;
	_duration_cinuru?: string;
	_duration_tmdb?: string;
	_duration_comscore?: string;
	_duration_cp?: string;
	_duration_cineweb?: string;
	_duration_drehwerk?: string;

	// release_date
	_release_date_sharc?: string;
	_release_date_cinfinity?: string;
	_release_date_cinuru?: string;
	_release_date_tmdb?: string;
	_release_date_comscore?: string;
	_release_date_cp?: string;
	_release_date_cineweb?: string;
	_release_date_drehwerk?: string;

	// genres
	_genres_sharc?: string[];
	_genres_cinfinity?: string[];
	_genres_cinuru?: string[];
	_genres_tmdb?: string[];
	_genres_cp?: string[];
	_genres_cineweb?: string[];
	_genres_drehwerk?: string[];

	// country
	_country_sharc?: string;
	_country_cinfinity?: string;
	_country_cp?: string;
	_country_cineweb?: string;
	_country_drehwerk?: string;

	// age_rating
	_age_rating_cinuru?: string;
	_age_rating_tmdb?: string;
	_age_rating_comscore?: string;
	_age_rating_cp?: string;
	_age_rating_cineweb?: string;
	_age_rating_drehwerk?: string;

	_cinemasSpecificContent?: Map<
		PublicCinemaId,
		{ attributes: InternalScreeningAttributeId[]; teaser?: string }
	>;

	_directed_by: string[] | null;
	_search_allowed_cp?: boolean;

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	constructor(data: any) {
		Object.assign(this, data);
	}
	static encodeDbId(dbId: InternalMovieId): PublicMovieId {
		return <PublicMovieId>encodeDbId('Movie', <number>dbId);
	}
	static decodePublicId(id: PublicMovieId): InternalMovieId {
		return <InternalMovieId>decodePublicId('Movie', <string>id);
	}

	static async refreshMovieData(force: boolean = false) {
		if (force || Date.now() - lastMovieCacheUpdate > 5 * 60 * 1000) {
			if (!refreshingMovieCache || force) {
				refreshingMovieCache = (async () => {
					const [res, cinemaSpecificContentRes, inCinemaSinceRes] = await Promise.all([
						db.queryWithCache(movieQuery, {
							ttlMinutes: 15,
							forceReload: force,
							shouldStoreLargeObjectInCache: true,
						}), // if force is passed in we want to query directly from the database instead of the redis cache
						db.queryWithCache(
							sql`
						SELECT 
							movie_id, 
							restrict_to_cinemas, 
							teaser_text,
							attributes,
							week 
						FROM 
							priv.p_movie_content_external 
						WHERE 
							content_provider LIKE 'CINEPLEX_CINEMA%' 
							AND 
							restrict_to_cinemas IS NOT NULL
							`,
							{ ttlMinutes: 15, forceReload: force } // if force is passed in we want to query directly from the database instead of the redis cache)
						),
						db.queryWithCache(
							sql`
						SELECT 
							movie_id, cinema_id, first_show
						FROM 
							pub.in_cinema_since 
							`,
							{ ttlMinutes: 15, forceReload: force } // if force is passed in we want to query directly from the database instead of the redis cache)
						),
					]);
					lastMovieCacheUpdate = Date.now();
					const cacheOfCaches = new Map();
					cinemaSpecificContentRes.rows.forEach(
						(r: {
							movie_id: number;
							restrict_to_cinemas: number[];
							teaser_text?: string;
							attributes?: string[];
							week?: number;
						}) => {
							const movieIdDb = r.movie_id;
							r.restrict_to_cinemas.forEach((cin) => {
								const cinemaId = Cinema.encodeDbId(cin);
								const movieAttCache = cacheOfCaches.get(movieIdDb) || new Map();
								const existingEntry = movieAttCache.get(cinemaId) || {
									attributes: [],
								};
								if (r.teaser_text) {
									existingEntry.teaser = r.teaser_text;
								}
								if (r.week) {
									existingEntry.week = r.week;
								}
								r.attributes &&
									r.attributes.forEach((att) => {
										if (!existingEntry.attributes.includes(att)) {
											existingEntry.attributes.push(att);
										}
									});
								movieAttCache.set(cinemaId, existingEntry);
								cacheOfCaches.set(movieIdDb, movieAttCache);
							});
						}
					);
					res.rows.forEach((r) => {
						const movie = new Movie({
							...r,
							id: Movie.encodeDbId(r.id),
							_cachedAt: new Date(),
							_cinemasSpecificContent: cacheOfCaches.get(r.id),
						});
						movieCache.set(movie.id, movie);
					});
					inCinemaSinceRes.rows.forEach((r) => {
						inCinemaSinceCache.set(`${r.movie_id}_${r.cinema_id}`, {
							movieIdDb: r.movie_id,
							cinemaIdDb: r.cinema_id,
							inCinemaSince: r.first_show,
						});
					});
				})();
			}
			try {
				await refreshingMovieCache;
				refreshingMovieCache = null;
			} catch (e) {
				refreshingMovieCache = null;
				console.log('Error refreshing movie cache', e);
				throw e;
			}
		}
	}
	static async gen(_: unknown, id: PublicMovieId) {
		try {
			await this.refreshMovieData();
		} catch (e) {
			//Retry once
			await this.refreshMovieData();
		}
		const movie = movieCache.get(id);
		if (!movie) {
			throw new EntityNotFoundError(`No movie with this id exists: ${id}`, 'Movie');
		}
		return movie;
	}
	static async genMult(_?: unknown, ids?: PublicMovieId[]) {
		await this.refreshMovieData();
		const movies = ids
			? ids.map((id) => {
					const movie = movieCache.get(id);
					if (!movie) {
						throw new EntityNotFoundError(`No movie with this id exists: ${id}`, 'Movie');
					}
					return movie;
			  })
			: [...movieCache.values()];
		return movies;
	}

	resolveField(language: Language, fieldName: string) {
		const getValue = (
			fName: string,
			provider:
				| 'cp'
				| 'sharc'
				| 'comscore'
				| 'cinuru'
				| 'tmdb'
				| 'cinfinity'
				| 'cineweb'
				| 'drehwerk'
		) => {
			const val = this[`_${fName}_${provider}`];
			if (Array.isArray(val) && val.length === 0) {
				//Do not return [] but null instead so that the ' || ' chain works also for arrays
				return null;
			}
			return val;
		};

		switch (language) {
			case 'de-cineplex':
				return (
					getValue(fieldName, 'cp') ||
					getValue(fieldName, 'sharc') ||
					getValue(fieldName, 'comscore') ||
					getValue(fieldName, 'cinuru') ||
					getValue(fieldName, 'tmdb')
				);
			case 'de-cinfinity':
				return (
					getValue(fieldName, 'cinfinity') ||
					getValue(fieldName, 'sharc') ||
					getValue(fieldName, 'comscore') ||
					getValue(fieldName, 'cinuru') ||
					getValue(fieldName, 'tmdb')
				);
			case 'de-cineweb':
				return (
					getValue(fieldName, 'cineweb') ||
					getValue(fieldName, 'sharc') ||
					getValue(fieldName, 'comscore') ||
					getValue(fieldName, 'cinuru') ||
					getValue(fieldName, 'tmdb')
				);
			case 'de-drehwerk':
				return (
					getValue(fieldName, 'drehwerk') ||
					getValue(fieldName, 'sharc') ||
					getValue(fieldName, 'comscore') ||
					getValue(fieldName, 'cinuru') ||
					getValue(fieldName, 'tmdb')
				);
			default:
				return (
					getValue(fieldName, 'sharc') ||
					getValue(fieldName, 'comscore') ||
					getValue(fieldName, 'cinuru') ||
					getValue(fieldName, 'tmdb')
				);
		}
	}
	screeningStatus = async (_: unknown, { language }: { language: Language }) => {
		const res = (
			await db.queryWithCache(sql`
		WITH relevant_screenings as (
			SELECT 
				datetime 
			FROM 
				priv.p_showtime 
			WHERE 
				datetime > now() - interval '1 month' 
				AND 
				movie_id=${Movie.decodePublicId(this.id)} 
				AND 
				cinema_id = ANY (
					SELECT 
						cinema_id 
					FROM 
						priv.p_cinema_app_brand 
					WHERE 
						language = ${language}
					)
			)
		SELECT 
			(
				SELECT 
					min(datetime) > now() + interval '3 days' 
				FROM 
					relevant_screenings
			) as is_future,
			(
				SELECT 
					max(datetime) > now() 
				FROM 
					relevant_screenings
			) as is_current
		`)
		).rows[0];
		return res.is_future ? 'SOON' : res.is_current ? 'CURRENT' : 'NONE';
	};
	title = (_: unknown, { language }: { language: Language }): string =>
		this.resolveField(language, 'title');
	banner = (_: unknown, { language }: { language: Language }): string =>
		this.resolveField(language, 'banner');
	poster = (_, { language }: { language: Language }): string =>
		this.resolveField(language, 'poster');
	directedBy = () => {
		if (!this._directed_by || this._directed_by.length === 0) return '';
		if (this._directed_by.length <= 2) return `von ${this._directed_by.join(' und ')}`;
		else
			return `von ${this._directed_by.slice(0, this._directed_by.length - 1).join(', ')} und ${
				this._directed_by[this._directed_by.length - 1]
			}`;
	};
	synopsis = (_: unknown, { language }: { language: Language }) => {
		return this.resolveField(language, 'synopsis');
	};
	duration = (_: unknown, { language }: { language: Language }) =>
		this.resolveField(language, 'duration');
	releaseDate = (_: unknown, { language }: { language: Language }) => {
		return this.resolveField(language, 'release_date');
	};
	genres = (_: unknown, { i18n, language }: { i18n: I18N; language: Language }): string[] => {
		const genresDb = this.resolveField(language, 'genres') || [];
		return genresDb
			.map((g) => {
				if (g) {
					return i18n.t('MovieDetailView.genres', { returnObjects: true })[g.replace('-', '_')];
				}
			})
			.filter((g) => g);
	};
	country = (_: unknown, { language }: { language: Language }) =>
		this.resolveField(language, 'country');
	ageRating(_: unknown, { i18n, language }: { i18n: I18N; language: Language }) {
		const ageRating = this.resolveField(language, 'age_rating');
		if (ageRating) {
			return i18n.t('MovieDetailView.ageRatings', { returnObjects: true })[
				ageRating.replace(' ', '_')
			];
		} else {
			return null;
		}
	}

	cinemaSpecificContent = async (
		{ cinemaIds }: { cinemaIds: PublicCinemaId[] },
		{ i18n, viewer }: { i18n: I18N; viewer: Viewer }
	) => {
		if (!this._cinemasSpecificContent) {
			return [];
		}
		const ct = this._cinemasSpecificContent;
		return cinemaIds
			.map((cinemaId) => {
				const entry = ct.get(cinemaId);
				if (!entry) {
					return;
				}
				return {
					...entry,
					attributes: async () =>
						(
							await Promise.all(
								entry.attributes.map(
									async (id) =>
										await ScreeningAttribute.gen(ScreeningAttribute.encodeDbId(id), {
											i18n,
											viewer,
										})
								)
							)
						).filter(({ type }) => type === 'MOVIE'),
					cinema: async () => await Cinema.gen(null, cinemaId),
				};
			})
			.filter((entry) => entry);
	};

	async credits(
		{ departments }: { departments: ('ACTING' | 'WRITING' | 'DIRECTING')[] },
		{ viewer }
	) {
		const deps = departments || ['ACTING', 'WRITING', 'DIRECTING'];

		const movieIdDb = Movie.decodePublicId(this.id);
		const res = await db.queryWithCache(
			sql`
		SELECT 
			c.* 
		FROM
			(
				(SELECT id, person_id, role, 'ACTING' as department, position_order FROM priv.p_movie_cast WHERE movie_id = ${movieIdDb} AND ${deps.includes(
				'ACTING'
			)})
				UNION
				(SELECT id, person_id, 'Drehbuch' as role, 'WRITING' as department, position_order FROM priv.p_movie_writer WHERE movie_id = ${movieIdDb} AND ${deps.includes(
				'WRITING'
			)} )
				UNION
				(SELECT id, person_id, 'Regie' as role, 'DIRECTING' as department, position_order FROM priv.p_movie_director WHERE movie_id = ${movieIdDb} AND ${deps.includes(
				'DIRECTING'
			)})

			) as c
		ORDER BY
			c.department, 
			c.position_order;
		`
		);
		return (
			(await Promise.all(
				res.rows.map((credit) => ({
					...credit,
					id: encodeDbId(`Credit-${credit.department}`, credit.id),
					person: CastOrCrewMember.gen(viewer, CastOrCrewMember.encodeDbId(credit.person_id)),
				}))
			)) || []
		).filter((item) => item !== null);
	}
	async screenings(
		args: {
			movieIds?: PublicMovieId[];
			cinemaIds?: PublicCinemaId[];
			after?: Date;
			before?: Date;
		},
		{ viewer }: { viewer: Viewer }
	) {
		return await Screening.allScreenings(viewer, { ...args, movieIds: [this.id] });
	}
	async currentOrNextAvailableInCinemaDate({ cinemaIds }: { cinemaIds: PublicCinemaId[] }) {
		// This function serves to display the relative time distance of the movies availability in the app.
		// This sounds a bit complicated, because there is no real word for it.
		// Examples are: Starts in 3 days in your cinema, or In your cinema for 3 weeks.
		// Where it gets complicated: Sometimes movies reenter the cinema. For example for a movie from the 80s we dont want
		// to display 'In your cinema for 35 years' but the next time it is shown (e.g. 'in your cinema in 3 days')
		// To do so, we do the following:
		// We look at all showtimes starting 180 days ago and additionally the soon_in_cinema items of the cinema
		const movieIdDb = Movie.decodePublicId(this.id);
		let minDate: Date;

		cinemaIds.forEach((id) => {
			const cinemaIdDb = Cinema.decodePublicId(id);
			const entry = inCinemaSinceCache.get(`${movieIdDb}_${cinemaIdDb}`);
			if (entry && entry.inCinemaSince) {
				if (!minDate || entry.inCinemaSince < minDate) {
					minDate = entry.inCinemaSince;
				}
			}
		});

		return minDate ? new Date(minDate).toISOString().substr(0, 10) : null;
	}
	async firstScreening(
		{ cinemaIds }: { cinemaIds: PublicCinemaId[] },
		{ viewer }: { viewer: Viewer }
	) {
		let sqlQuery = 'SELECT DISTINCT ON(movie_id)	id FROM	pub.showtimes	WHERE movie_id = $1';
		const sqlArgs = [Movie.decodePublicId(this.id)];
		// if no cinemas where specified, try getting the selected cinemas of the user
		const currentUser = await User.gen(viewer, viewer.userId);
		let cinemaIdsDb: InternalCinemaId[];
		if (cinemaIds) {
			cinemaIdsDb = cinemaIds.map((id) => Cinema.decodePublicId(id));
		}
		if (!cinemaIds && currentUser) {
			cinemaIdsDb = await viewer.selectedCinemaIdsDb();
		}
		if (cinemaIdsDb) {
			sqlQuery += ' AND cinema_id = ANY ($2) ';
			sqlArgs.push(cinemaIdsDb);
		}
		sqlQuery += 'ORDER BY movie_id, datetime';
		const res = await db.query(sqlQuery, sqlArgs);
		if (res.rows.length) {
			return await Screening.gen(viewer, Screening.encodeDbId(res.rows[0].id));
		}
		return null;
	}
	async reviewRatingByCurrentUser(_: unknown, { viewer }: { viewer: Viewer }) {
		//Check if review rating exists and if not, return null (instead of an error if ReviewRating.gen would
		// be called directly)
		const res = await db.query(sql`
		SELECT 
			id 
		FROM 
			priv.p_rating_movie 
		WHERE 
			user_id = ${viewer.userIdDb} 
			AND 
			movie_id=${Movie.decodePublicId(this.id)} 
			AND 
			valid = true
		`);
		if (!res.rows.length) {
			return null;
		}
		return ReviewRating.gen(
			viewer,
			ReviewRating.encodeDbId(User.decodePublicId(viewer.userId), Movie.decodePublicId(this.id))
		);
	}
	async averageRating() {
		const res = await db.query(sql`
		SELECT  
			avg(rating), 
			count(*) 
		FROM 
			priv.p_rating_movie 
		WHERE 
			valid = true 
			AND 
			movie_id = ${Movie.decodePublicId(this.id)}
		`);
		if (res.rows.length && res.rows[0].count > 3) {
			return Math.round(res.rows[0].avg * 10) / 10;
		}
		//We do not have enough ratings, return tmdb rating
		return this._average_rating_cinuru;
	}
	async interestRatingByCurrentUser(_: unknown, { viewer }: { viewer: Viewer }) {
		return InterestRating.gen(
			viewer,
			InterestRating.encodeDbId(viewer.userIdDb, Movie.decodePublicId(this.id))
		);
	}
	async trailers(_: unknown, { viewer, language }: { viewer: Viewer; language: Language }) {
		return await Trailer.trailersForMovie(viewer, this.id, language);
	}
	async seenInCinemaByCurrentUser(_, { viewer }) {
		return SeenInCinema.gen(
			viewer,
			SeenInCinema.encodeDbId(viewer.userIdDb, Movie.decodePublicId(this.id))
		);
	}

	async foreignMovieId(_, { foreignIdType }) {
		const res = (
			await db.query(sql`
			SELECT  
				title,
				foreign_id
			FROM 
				priv.p_foreign_movie_identifier
			WHERE 
				foreign_id_type = ${foreignIdType} AND movie_id = ${Movie.decodePublicId(this.id)}
			`)
		).rows[0];

		if (res) {
			return { title: res.title, foreignId: res.foreign_id };
		} else {
			return null;
		}
	}
	searchAllowed = (_: unknown, { language }: { language: Language }) => {
		if (language === 'de-cineplex') {
			return this._search_allowed_cp;
		}
		return true;
	};

	isFromMasterFilmDB() {
		return Number(Movie.decodePublicId(this.id)) < 100000;
	}
}
