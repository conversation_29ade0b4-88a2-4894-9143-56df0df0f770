import { db, ForbiddenError, sql } from '../../utils';
import { Cinema } from '..';
import { PublicCinemaId, Viewer } from '../../typescript-types';
import { Beacon } from './Beacon';

export const createBeacon = async (
	_: unknown,
	{
		cinemaId,
		comments,
		lastBatteryChange,
	}: {
		cinemaId: PublicCinemaId;
		comments: string;
		lastBatteryChange: Date;
	},
	{ viewer }: { viewer: Viewer }
) => {
	//Check that user is admin
	if (!viewer.isRoot && !viewer.isAdmin) {
		return new ForbiddenError('You must be logged in as an admin user to create beacons');
	}

	const cinemaIdDb = Cinema.decodePublicId(cinemaId);

	try {
		const res = await db.query(sql`
			INSERT INTO priv.p_beacon (cinema_id,instance_id, last_updated, comments, last_battery_change)
			VALUES (${cinemaIdDb}, ${cinemaIdDb}, NOW(), ${comments}, ${lastBatteryChange})
			RETURNING id
		`);

		const beaconDbId = res.rows[0].id;
		return await Beacon.gen(viewer, Beacon.encodeDbId(beaconDbId));
	} catch (error: any) {
		if (error.code === '23503') {
			// PostgreSQL error code for foreign key violation
			return new ForbiddenError('The specified cinema does not exist.');
		}
		throw error;
	}
};
