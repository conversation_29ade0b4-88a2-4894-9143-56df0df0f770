import { db, decodePublicId, encodeDbId, sql, UserInputError } from '../../utils';
import {
	Viewer,
	InternalBeaconId,
	PublicBeaconId,
	InternalCinemaId,
	PublicCinemaId,
	Context,
} from '../../typescript-types';
import { Cinema } from '../Cinema/Cinema';
import { add, sub } from 'date-fns';

export class Beacon {
	id: PublicBeaconId;
	_dbId: InternalBeaconId;
	_cinemaId: InternalCinemaId;
	instanceId: number;
	lastUpdated: Date;
	lastScan: Date;
	comments: string;
	lastBatteryChange: Date;

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	constructor(data: any) {
		Object.assign(this, data);
	}

	static encodeDbId(dbId: InternalBeaconId): PublicBeaconId {
		return encodeDbId('Beacon', dbId as number);
	}

	static decodePublicId(id: PublicBeaconId): InternalBeaconId {
		return decodePublicId('Beacon', id as string);
	}

	static async gen(viewer: Viewer, id: PublicBeaconId) {
		const beaconDbId = Beacon.decodePublicId(id);
		const result = await db.queryOne(sql`
			SELECT 
				b.id as "_dbId", 
				b.cinema_id as "_cinemaId", 
				b.instance_id as "instanceId", 
				b.last_updated as "lastUpdated", 
				b.comments,
				b.last_battery_change as "lastBatteryChange",
				(
					SELECT MAX(scanned_at)
					FROM priv.p_beacon_scanned s
					WHERE s.instance_id = b.instance_id
				) as "lastScan"
			FROM priv.p_beacon b
			WHERE b.id = ${beaconDbId}
		`);

		if (!result) {
			return null;
		}
		return new Beacon({ ...result, id: Beacon.encodeDbId(result._dbId) });
	}

	static async genByBeaconProps(viewer: Viewer, instanceId: number) {
		const result = await db.queryOne(sql`
			SELECT 
				b.id as "_dbId", 
				b.cinema_id as "_cinemaId", 
				b.instance_id as "instanceId", 
				b.last_updated as "lastUpdated", 
				b.comments,
				b.last_battery_change as "lastBatteryChange",
				(
					SELECT MAX(scanned_at)
					FROM priv.p_beacon_scanned s
					WHERE s.instance_id = b.instance_id
				) as "lastScan"
			FROM priv.p_beacon b
			WHERE b.instance_id = ${instanceId}
		`);

		if (!result) {
			return null;
		}
		return new Beacon({ ...result, id: Beacon.encodeDbId(result._dbId) });
	}

	async cinema(_: unknown, { viewer }: { viewer: Viewer }) {
		return await Cinema.gen(viewer, Cinema.encodeDbId(this._cinemaId));
	}

	static async genMult() {
		const res = await db.queryWithCache(sql`
			SELECT 
				b.id as "_dbId", 
				b.cinema_id as "_cinemaId", 
				b.instance_id as "instanceId", 
				b.last_updated as "lastUpdated", 
				b.comments,
				b.last_battery_change as "lastBatteryChange",
				(
					SELECT MAX(scanned_at)
					FROM priv.p_beacon_scanned s
					WHERE s.instance_id = b.instance_id
				) as "lastScan"
			FROM priv.p_beacon b
		`);

		const beacons = res.rows.map((row) => new Beacon({ ...row, id: Beacon.encodeDbId(row._dbId) }));
		return beacons;
	}

	static async genByCinema(cinemaId: PublicCinemaId): Promise<Beacon[]> {
		const res = await db.queryWithCache(sql`
			SELECT 
				id as "_dbId"
			FROM priv.p_beacon
			WHERE 
				cinema_id = ${Cinema.decodePublicId(cinemaId)}
		`);
		const beacons = res.rows.map((row) => new Beacon({ id: Beacon.encodeDbId(row._dbId) }));
		return beacons;
	}

	static async registerBeaconScanned(
		_: unknown,
		{ instanceId }: { instanceId: number },
		context: Context
	) {
		try {
			const viewer = context.viewer;
			const res = await db.query(sql`
				INSERT INTO priv.p_beacon_scanned (user_id, instance_id, scanned_at)
				VALUES (${viewer.userIdDb}, ${instanceId}, now())
			`);

			console.log('res', res.rows);
			const beacon = await Beacon.genByBeaconProps(viewer, instanceId);
			if (!beacon) {
				throw new UserInputError('BEACON_NOT_FOUND');
			}

			const cinema = await beacon.cinema(_, { viewer });
			if (!cinema) {
				throw new UserInputError('CINEMA_NOT_FOUND');
			}

			const now = new Date();

			const timeWindowStart = sub(now, { minutes: 30 });
			const timeWindowEnd = add(now, { minutes: 30 });

			// Check if the user has a valid ticket for this cinema
			const ticket = await db.queryOne(sql`
				SELECT id, scanned FROM priv.p_ticket 
				WHERE cinema_id = ${Cinema.decodePublicId(cinema.id)}
				AND user_id = ${viewer.userIdDb} 
				AND screening_datetime BETWEEN ${timeWindowStart} AND ${timeWindowEnd}
				AND ticket_provider = 'COMPESO'
				ORDER BY screening_datetime ASC
				LIMIT 1
			`);

			if (!ticket) {
				return { success: false };
			}
			if (!ticket?.scanned) {
				await db.query(sql`
					UPDATE priv.p_ticket SET scanned=true WHERE id = ${ticket.id}
				`);
			}
			return { success: true };
		} catch (err) {
			console.error('Error in registerBeaconScanned:', err);
			throw err;
		}
	}
}
