import { gql } from 'apollo-server-core';

export const typeDef = gql`
	extend type Query {
		beacons: [Beacon!]!
		beaconById(id: ID!): Beacon
	}

	extend type Mutation {
		createBeacon(cinemaId: ID!, comments: String, lastBatteryChange: DateTime): Beacon!

		updateBeacon(id: ID!, comments: String, lastBatteryChange: DateTime): Beacon!

		deleteBeacon(id: ID!): deleteBeaconReturnType!
		registerBeaconScanned(instanceId: Int!): RegisterBeaconScannedReturnType
	}

	type Beacon {
		id: ID!
		cinema: Cinema
		instanceId: Int!
		lastUpdated: DateTime!
		lastScan: DateTime
		comments: String
		lastBatteryChange: DateTime
	}

	type deleteBeaconReturnType {
		success: Boolean!
	}

	type RegisterBeaconScannedReturnType {
		success: Boolean!
	}
`;
