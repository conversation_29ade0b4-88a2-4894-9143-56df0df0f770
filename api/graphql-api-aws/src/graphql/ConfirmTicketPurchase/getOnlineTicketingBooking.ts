/* eslint-disable no-await-in-loop */
import { Apollo<PERSON><PERSON><PERSON>, db, ForbiddenError, sql } from '../../utils';

import { InternalTicketId } from '../Ticket/Ticket';

import fetch from 'node-fetch';
import { PUPPETEER_HELPER_API_KEY, PUPPETEER_HELPER_URL } from '../../../consts';
import { Viewer } from '../../typescript-types';
import { printError, sendErrorMail } from '../../utils';
import { getCinetixxBooking } from './cinetixx';
import { getCompesoBooking } from './compeso';
import { getKinoheldBooking } from './kinoheld';
import { getMarsBooking } from './mars';
import { getTicketInternationalBooking } from './ticketInternational';

export type Ticket = {
	seatName: string;
	rowName: string;
	ticketNumber: string;
	price: number;
	code?: string;
	isSubscriptionTicket?: boolean;
	ticketStatus?: 'open' | 'cancelled' | 'redeemed';
	isCancelable?: boolean;
	qrCode?: string;
	cancelUrl?: string;
};

type CinetixxResponse = {
	logs?: string;
	errorMessage?: string;
	data: null | {
		tickets: Ticket[];
		auditorium?: string;
		ticketStatus?: 'open' | 'cancelled';
		isCancelable: boolean;
		ticketsQRCode?: string;
		concessionsPickupCodeImage: undefined;
		concessionsPickupCode: undefined;
		concessionLineItems: undefined;
	};
};
type MarsResponse = {
	logs?: string;
	errorMessage?: string;
	data: null | {
		tickets: Ticket[];
		auditorium?: string;
		ticketStatus: 'open' | 'redeemed' | 'cancelled';
		ticketsQRCode?: string;
		isCancelable: boolean;
		concessionsPickupCodeImage: string;
		concessionsPickupCode?: string;
		concessionLineItems: {
			name: string;
			price: number;
			amount: number;
		}[];
	};
};

type CompesoResponse = {
	logs?: string;
	errorMessage?: string;
	data: null | {
		tickets: Ticket[];
		auditorium: string;
		ticketStatus: 'open' | 'cancelled';
		ticketsQRCode: string;
		isCancelable: boolean;
		concessionsPickupCodeImage: undefined;
		concessionsPickupCode: undefined;
		concessionLineItems: undefined;
	};
};

type KinoheldResponse = {
	logs?: string;
	errorMessage?: string;
	data: null | {
		tickets: Ticket[];
		auditorium?: string;
		ticketStatus: 'open' | 'redeemed' | 'cancelled';
		ticketsQRCode?: string;
		isCancelable: undefined;
		concessionsPickupCodeImage: undefined;
		concessionsPickupCode: undefined;
		concessionLineItems: undefined;
	};
};

export type TicketInternationalResponse = {
	logs?: string;
	errorMessage?: string;
	data: null | {
		tickets: Ticket[];
		auditorium: string;
		ticketStatus: 'open' | 'redeemed' | 'cancelled';
		ticketsQRCode: string;
		isCancelable: boolean;
		concessionsPickupCodeImage: undefined;
		concessionsPickupCode: undefined;
		concessionLineItems: {
			name: string;
			price: number;
			amount: number;
		}[];
	};
};
type PDFResponse = {
	logs?: string;
	errorMessage?: string;
	data: string;
};

export type OnlineTicketingResponse =
	| undefined
	| MarsResponse
	| CompesoResponse
	| CinetixxResponse
	| KinoheldResponse
	| TicketInternationalResponse;
export type PupeteerResponse = OnlineTicketingResponse | PDFResponse;

type TicketInfo = {
	id: InternalTicketId;
	status: 'open' | 'redeemed' | 'cancelled';
	user_id: number;
	cf_booking_id: string;
	cp_booking_id: string;
	voucher_code?: string;
	cinema_id: number;
	reference_url?: string;
	ticket_provider?: TicketProvider;
	user_token?: string;
	screening_datetime?: Date;
	qr_code?: string;
	kinoheld_scan?: boolean;
};

export type TicketProvider = 'COMPESO' | 'KINOHELD' | 'MARS' | 'CINETIXX' | 'TICKET_INTERNATIONAL';

const cinetixxBookingFetcher = async (bookingId: string): Promise<CinetixxResponse> => {
	const { success, error, data } = await getCinetixxBooking(bookingId);
	if (!success) {
		return {
			data: null,
			errorMessage: error,
		};
	}
	const tickets = data.map((ticket) => {
		const ticketStatus = (ticket.statusKey === 'TCK_CANCELLED'
			? 'cancelled'
			: ticket.statusKey === 'TCK_STAMPED' || ticket.statusKey === 'TCK_PRINTED'
			? 'redeemed'
			: 'open') as 'open' | 'cancelled' | 'redeemed';

		return {
			seatName: ticket.seatNumber,
			rowName: ticket.rowNumber,
			ticketNumber: String(ticket.id),
			price: ticket.price,
			code: ticket.partnerCode,
			ticketStatus: ticketStatus,
			isSubscriptionTicket: Boolean(ticket.priceCategorie?.toLowerCase?.() === 'cinfinity'),
			isCancelable: ticketStatus === 'open' && Boolean(ticket.canCancel && ticket.urlCancel),
			qrCode: ticket.number,
			cancelUrl: ticket.urlCancel,
		};
	});

	return {
		errorMessage: undefined,
		logs: undefined,
		data: {
			tickets,
			auditorium: data[0]?.auditoriumName,
			ticketsQRCode: '',
			isCancelable: undefined, // as single ticket cancellation is supported, these infos are on the ticket level
			ticketStatus: undefined, // as single ticket cancellation is supported, these infos are on the ticket level
			concessionsPickupCodeImage: undefined,
			concessionsPickupCode: undefined,
			concessionLineItems: undefined,
		},
	};
};

const ticketInternationalBookingFetcher = async ({
	bookingId,
	referenceUrl,
}: {
	bookingId: string;
	referenceUrl: string;
	// e.g. https://odeon-apollo-kino.de/event/?l_re=1&isGuestUsage=1&eventID=XXX&showID=XXX&TransactionID=XXX&guestMail=XXX
	// or https://ticket-cloud.de/Savoy-Bordesholm/Confirmation/t8kb239c74pju1buo7st3sm8kd/173833075392269
}): Promise<TicketInternationalResponse> => {
	const isOldWebShop = referenceUrl.startsWith('https://ticket-cloud');
	const mainUrl = isOldWebShop
		? referenceUrl.split('/Confirmation')[0]
		: `${referenceUrl.split('.de')[0]}.de`;
	const cinemaIdDb = (
		await db.queryOne(
			sql`
			SELECT 
			id 
			FROM priv.p_cinema 
			WHERE online_ticketing_base_url LIKE ${mainUrl + '%'}
			`
		)
	)?.id;

	const { success, error, data } = await getTicketInternationalBooking({
		referenceId: bookingId,
		cinemaIdDb,
	});
	if (!success) {
		return {
			data: null,
			errorMessage: error,
		};
	}

	const tickets = data.shows[0].tickets.map((ticket) => {
		const seat = ticket.seats[0];
		return {
			seatName: seat.seatName,
			rowName: seat.rowName,
			ticketNumber: String(ticket.iD),
			isSubscriptionTicket: ticket.printName === 'Cinfinity',
			price: 0,
			code: ticket.paymentRefs[0]?.reference,
			ticketStatus: undefined, // single ticket cancellation is not supported
			isCancelable: undefined, // single ticket cancellation is not supported
			qrCode: undefined, // single ticket cancellation is not supported
		};
	});

	const firstTicket = data.shows[0].tickets[0];

	return {
		errorMessage: undefined,
		logs: undefined,
		data: {
			tickets,
			auditorium: firstTicket?.seats[0]?.auditoriumName || '',
			ticketsQRCode: data.barcode,
			isCancelable:
				firstTicket.refunded === false &&
				data.refundAllowed?.allowed &&
				Date.now() < new Date(data.refundAllowed.time).getTime(),
			ticketStatus:
				data.status === 'collected' ? 'redeemed' : firstTicket.refunded ? 'cancelled' : 'open',
			concessionsPickupCodeImage: undefined,
			concessionsPickupCode: undefined,
			concessionLineItems: undefined,
		},
	};
};

const compesoBookingFetcher = async (
	referenceUrl: string
): Promise<TicketInternationalResponse> => {
	const { success, error, data } = await getCompesoBooking(referenceUrl);
	if (!success) {
		return {
			data: null,
			errorMessage: error,
		};
	}

	const voucherCodes = data.payments
		.filter((payment) => payment.voucher)
		.map((payment) => payment.voucher.voucherCode);

	const orderItem = data.ticketOrder.orderItems[0];

	const tickets = orderItem.tickets.map((ticket) => {
		const seat = ticket.seat;
		const isSubscriptionTicket = ticket.pricing.categoryName === 'Cinfinity';
		return {
			seatName: seat.seat,
			rowName: seat.row,
			ticketNumber: ticket.itemId,
			price: ticket.pricing.ticketPrice,
			code: isSubscriptionTicket ? voucherCodes.shift() : undefined,
			isSubscriptionTicket,
		};
	});

	const firstTicket = orderItem.tickets[0];
	const status = firstTicket.status;
	const qrCode = orderItem.pickupCode;

	return {
		errorMessage: undefined,
		logs: undefined,
		data: {
			tickets,
			auditorium: firstTicket.auditoriumName,
			ticketsQRCode: qrCode,
			isCancelable: status === 2097155,
			ticketStatus: status === 2097155 ? 'open' : 'cancelled',
			concessionsPickupCodeImage: undefined, // TODO: they also have concessions
			concessionsPickupCode: undefined, // TODO: they also have concessions
			concessionLineItems: undefined, // TODO: they also have concessions
		},
	};
};

const kinoheldBookingFetcher = async (
	referenceUrl: string
): Promise<TicketInternationalResponse> => {
	console.log('Kinoheld booking fetcher', referenceUrl);
	const { errorMessage, data } = await getKinoheldBooking(referenceUrl);
	if (errorMessage) {
		console.log('Kinoheld booking fetcher error', errorMessage);
		return {
			data: null,
			errorMessage,
		};
	}

	console.log('Kinoheld booking fetcher data', data);
	const isCancelled = data.status !== 'NOT_REFUNDED';
	if (isCancelled) {
		console.log('Kinoheld booking fetcher cancelled');
		return {
			errorMessage: undefined,
			logs: undefined,
			data: {
				tickets: [],
				auditorium: null,
				ticketsQRCode: undefined,
				isCancelable: false,
				ticketStatus: 'cancelled' as const,
				concessionsPickupCodeImage: undefined,
				concessionsPickupCode: undefined,
				concessionLineItems: undefined,
			},
		};
	}

	const bookingLevelQrCodeInfos = data.codes
		? data.codes.filter((c) => !c.__typename || c.__typename === 'QrCode')
		: null;
	console.log('Kinoheld booking fetcher bookingLevelQrCodeInfos', bookingLevelQrCodeInfos);

	const ticketLevelQrCodeInfos = data.tickets
		.map((t) => t.codes)
		.flat()
		.filter(Boolean)
		.filter((c) => !c.__typename || c.__typename === 'QrCode');

	const hasNoQrCodes = !bookingLevelQrCodeInfos?.length && !ticketLevelQrCodeInfos?.length;

	if (hasNoQrCodes) {
		console.log('Kinoheld booking fetcher no qr codes');
		return {
			data: null,
			errorMessage: 'KINOHELD_QR_CODE_NOT_FOUND',
		};
	}

	// determine if the booking is cancelable & determine the cancel url
	const cancellationInfo = data.cancellationInfo;
	const screeningDateTime = new Date(data.show.endsale).getTime();
	const isCancelable = Boolean(
		cancellationInfo &&
			cancellationInfo.policy &&
			cancellationInfo.minutesBeforeBeginning &&
			cancellationInfo.link &&
			cancellationInfo.policy === 'ALLOWED' &&
			Math.floor((screeningDateTime - Date.now()) / 60000) > cancellationInfo.minutesBeforeBeginning
	);

	const nQrCodes = bookingLevelQrCodeInfos?.length;
	const nTickets = data.tickets.length;
	const tickets = data.tickets.map((ticket) => {
		// we either have qrCode infos on each ticket, or on the booking level - one for all or one for each ticket
		let qrCodeInfo = ticket.codes?.find((c) => c.__typename === 'QrCode');
		if (!qrCodeInfo) {
			qrCodeInfo =
				nQrCodes === nTickets ? bookingLevelQrCodeInfos.pop() : bookingLevelQrCodeInfos[0];
		}
		console.log('Kinoheld booking fetcher final Info', {
			seatName: !ticket.seat ? 'Frei' : ticket.seat.number,
			rowName: !ticket.seat ? 'Frei' : ticket.seat.row,
			ticketNumber: ticket.ticketNumbers[0],
			isSubscriptionTicket: undefined,
			qrCode: qrCodeInfo.code,
			isCancelable,
			cancelUrl: isCancelable ? cancellationInfo.link : undefined,
			price: 0,
			ticketStatus: (qrCodeInfo.isScanned ? 'redeemed' : 'open') as 'open' | 'redeemed',
		});
		return {
			seatName: !ticket.seat ? 'Frei' : ticket.seat.number,
			rowName: !ticket.seat ? 'Frei' : ticket.seat.row,
			ticketNumber: ticket.ticketNumbers[0],
			isSubscriptionTicket: undefined,
			qrCode: qrCodeInfo.code,
			isCancelable,
			cancelUrl: isCancelable ? cancellationInfo.link : undefined,
			price: 0,
			ticketStatus: (qrCodeInfo.isScanned ? 'redeemed' : 'open') as 'open' | 'redeemed',
		};
	});

	const auditorium = data.show.auditorium.name;

	return {
		errorMessage: undefined,
		logs: undefined,
		data: {
			tickets,
			auditorium: auditorium,
			isCancelable: isCancelable,
			ticketStatus: undefined,
			ticketsQRCode: undefined,
			concessionsPickupCodeImage: undefined,
			concessionsPickupCode: undefined,
			concessionLineItems: undefined,
		},
	};
};

const marsBookingFetcher = async ({
	bookingId,
	referenceUrl,
}: {
	bookingId: string;
	referenceUrl: string;
}): Promise<MarsResponse> => {
	const splittedReferenceUrl = referenceUrl.split('/sale')[0];

	const cinemaRes = (await db.queryOne(
		sql`
			SELECT 
			online_ticketing_base_url 
			FROM priv.p_cinema 
			WHERE online_ticketing_base_url LIKE ${`${splittedReferenceUrl}%`}
			`
	)) as { online_ticketing_base_url: string };

	if (!cinemaRes) {
		return {
			data: null,
			errorMessage: 'Ticket not found',
		};
	}

	const onlineTicketingBaseUrl = cinemaRes.online_ticketing_base_url;

	// the bookingId is either the marsTicketId or the MarsCartId, we only know the marsTicketId if the tickets were bought via a subscription
	const bookingIdIsMarsCartId = referenceUrl.includes(bookingId);
	if (bookingIdIsMarsCartId) {
		// we need to use puppeteer
		const response = (await puppeteerAdapter({
			type: 'CONFIRMPAGE_MARS',
			referenceUrl,
		})) as MarsResponse;
		if (!response || !response.data) {
			return {
				data: null,
				errorMessage: 'TICKET_DATA_NOT_FOUND',
			};
		}

		const hasSubscriptionTickets = response.data.tickets.some((t) => t.isSubscriptionTicket);
		if (hasSubscriptionTickets) {
			// this can only happen if the marsTicketId was not mapped to the marsCartId in priv.p_mars_cart_id_vs_booking_id although the booking was done via a subscription
			return {
				data: null,
				errorMessage: 'IMPLAUSIBLE_MARS_BOOKING_ID',
			};
		}

		// determine if the booking is cancelable & determine the cancel url
		response.data.isCancelable = response.data.ticketStatus === 'open';
		response.data.tickets = response.data.tickets.map((t) => ({
			...t,
			cancelUrl: referenceUrl.replace('sale/summary?cartId=', 'storno/stornoticket/'),
		}));
		return response;
	} else {
		// we can get the booking directly from the mars api
		const requestUrl =
			onlineTicketingBaseUrl.split('/booking')[0] + `/api/v1/reservation?ticketId=${bookingId}`;
		// e.g. https://kinotickets.express/lennestadt/api/v1/reservation?ticketId=263387803
		const { success, error, data } = await getMarsBooking(requestUrl);
		if (!success) {
			return {
				data: null,
				errorMessage: error,
			};
		}

		type TickeStatus = 'open' | 'redeemed' | 'cancelled';

		const punched = data.punched === 'YES';
		const status = (data.resStatus === 'Cancelled'
			? 'cancelled'
			: punched
			? 'redeemed'
			: 'open') as TickeStatus;
		const ticketId = data.ticketId;
		const auditorium = data.saalName;
		// what is the status if the ticket is cancelled?

		if (status === 'cancelled') {
			return {
				errorMessage: undefined,
				logs: undefined,
				data: {
					tickets: [],
					auditorium: null,
					ticketsQRCode: ticketId,
					isCancelable: false,
					ticketStatus: 'cancelled' as const,
					concessionsPickupCodeImage: undefined,
					concessionsPickupCode: undefined,
					concessionLineItems: undefined,
				},
			};
		}

		const tickets = data.tickets.ticketList.map((ticket) => {
			const seatName = ticket.seat.seatname;
			const isValidSeatName = seatName.includes('R.') && seatName.includes('S.'); // usually like "R.11 S.4", but we also got "NAME" returned once
			const seatNameSplitted = isValidSeatName ? seatName.split(' ') : ['R.?', 'S.?'];
			const row = seatNameSplitted[0].replace('R.', '');
			const col = seatNameSplitted[1].replace('S.', '');
			return {
				seatName: col,
				rowName: row,
				ticketNumber: String(ticket.ticketNummer),
				price: ticket.preis,
				isSubscriptionTicket: ticket.abo === 'CINF',
				cancelUrl: referenceUrl.replace('sale/summary?cartId=', 'storno/stornoticket/'),
			};
		});

		return {
			errorMessage: undefined,
			logs: undefined,
			data: {
				tickets,
				auditorium,
				ticketsQRCode: ticketId,
				isCancelable: punched ? false : true,
				ticketStatus: status,
				concessionsPickupCodeImage: undefined,
				concessionsPickupCode: undefined,
				concessionLineItems: undefined,
			},
		};
	}
};

export const puppeteerAdapter = async ({
	type,
	referenceUrl,
}: {
	type: 'CONFIRMPAGE_MARS' | 'CONFIRMPAGE_KINOHELD' | 'CONFIRMPAGE_TICKET_INTERNATIONAL' | 'PDF';
	referenceUrl: string;
}): Promise<OnlineTicketingResponse> => {
	const headers = {
		'Accept': 'application/json',
		'Content-Type': 'application/json',
	};
	try {
		const controller = new AbortController();
		const id = setTimeout(() => controller.abort(), 20 * 1000);

		const response = await fetch(PUPPETEER_HELPER_URL, {
			method: 'POST',
			headers,
			body: JSON.stringify({
				apiKey: PUPPETEER_HELPER_API_KEY,
				type,
				referenceUrl,
			}),
			signal: controller.signal as any,
		});
		clearTimeout(id);
		if (response.status !== 200) {
			//Send error mail
			await sendErrorMail(
				`Getting Data from ${type} failed`,
				`Returned other than 200: response: ${JSON.stringify(response, null, 2)}`
			);
			return {
				data: null,
				errorMessage: `puppeteerAdapter res.status not 200 error: ${response.status}: ${response.statusText}`,
			};
		} else {
			return await response.json();
		}
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (e) {
		console.log(e);
		await sendErrorMail(`Getting Data from ${type} failed`, `Caught error: 	${printError(e)}`);
		return {
			data: null,
			errorMessage:
				e.type === 'aborted'
					? 'puppeteer Timeout'
					: `puppeteerAdapter catch error: ${printError(e)}`,
		};
	}
};

const validateReferenceUrl = async (
	referenceUrl: string,
	ticketProvider: TicketProvider
): Promise<boolean> => {
	let valid = false;
	if (ticketProvider === 'KINOHELD' && referenceUrl.startsWith('https://www.kinoheld.de/')) {
		valid = true;
	}

	if (ticketProvider === 'COMPESO' || ticketProvider === 'MARS') {
		const validOnlineTicketingBaseUrls = (
			await db.query(
				sql`SELECT online_ticketing_base_url FROM priv.p_cinema WHERE ticket_provider = ${ticketProvider}`
			)
		).rows
			.map((row) => row.online_ticketing_base_url)
			.filter(Boolean);

		if (ticketProvider === 'MARS') {
			// MARS base: https://kinotickets.express/ohz-oscar/booking/
			// MARS confirm: https://kinotickets.express/ohz-oscar/sale/summary?cartId=qbriuvpd0soxumwjgzpwyakeay2ni13k
			const compareParts = validOnlineTicketingBaseUrls.map((url) => url.split('/booking')[0]);
			valid = compareParts.some((url) => referenceUrl.startsWith(url));
		}

		if (ticketProvider === 'COMPESO') {
			// COMPESO base: https://shop.kino-buesum.de/lbbm
			// COMPESO confirm: https://shop.kino-buesum.de/lbbm/myaccount/transactions/OLNUE1-3262488?tat=fVI1djwiJWpWUFF3YBkGHQMCBQ4FeD90eX56YmRobm9VV1daXQxLQEpOG-e96aa89aKgt6nDxpKElJ-KgpWLi6Cl8P-o5-O17uqj
			valid = validOnlineTicketingBaseUrls.some((url) => referenceUrl.startsWith(url));
		}
	}
	return valid;
};

export const getOnlineTicketingBookingFn = async ({
	// either get the booking via ticketId, referencUrl + ticketProvider or bookingId + ticketProvider
	ticketId,
	referenceUrl,
	ticketProvider,
	bookingId,
}: {
	ticketId?: InternalTicketId;
	referenceUrl?: string;
	ticketProvider?: TicketProvider;
	bookingId?: string;
}): Promise<{
	success: boolean;
	error?:
		| 'CANNOT_GET_TICKET_WITHOUT_REFERENCE_URL'
		| 'TICKET_NOT_FOUND'
		| 'NO_TICKET_PROVIDER'
		| 'REFERENCE_URL_AND_BOOKING_ID_MISSING'
		| 'INVALID_REFERENCE_URL'
		| 'TICKET_DATA_NOT_FOUND'
		| 'QR_CODE_NOT_FOUND'
		| string;
	booking?: OnlineTicketingResponse['data'];
	ticketInfo?: TicketInfo;
}> => {
	let ticketInfo: TicketInfo | null = null;

	if (ticketId) {
		ticketInfo = (await db.queryOne(
			sql`
				SELECT 
				t.id, 
				t.status, 
				t.user_id, 
				t.cf_booking_id, 
				t.cp_booking_id, 
				t.voucher_code,
				t.user_token, 
				t.cinema_id, 
				t.reference_url, 
				t.ticket_provider, 
				t.screening_datetime,
				t.qr_code,
				c.kinoheld_scan
				FROM priv.p_ticket t 
				JOIN priv.p_cinema c ON t.cinema_id = c.id
				WHERE t.id = ${ticketId}
				`
		)) as null | TicketInfo;

		referenceUrl = ticketInfo.reference_url;
		ticketProvider = ticketInfo.ticket_provider;
		bookingId = ticketInfo.cf_booking_id;
	}

	if (!ticketProvider) {
		return {
			success: false,
			error: 'NO_TICKET_PROVIDER',
		};
	}

	let res: OnlineTicketingResponse;

	switch (ticketProvider) {
		case 'COMPESO': {
			if (!referenceUrl) {
				return {
					success: false,
					error: 'CANNOT_GET_TICKET_WITHOUT_REFERENCE_URL',
				};
			}
			const referenceUrlValid = await validateReferenceUrl(referenceUrl, ticketProvider);
			if (!referenceUrlValid) {
				return {
					success: false,
					error: 'INVALID_REFERENCE_URL',
				};
			}
			res = (await compesoBookingFetcher(referenceUrl)) as CompesoResponse;
			break;
		}
		case 'MARS': {
			if (!bookingId || !referenceUrl) {
				return {
					success: false,
					error: 'CANNOT_GET_TICKET_WITHOUT_BOOKING_ID_AND_REFERENCE_URL',
				};
			}
			const referenceUrlValid = await validateReferenceUrl(referenceUrl, ticketProvider);
			if (!referenceUrlValid) {
				return {
					success: false,
					error: 'INVALID_REFERENCE_URL',
				};
			}
			res = (await marsBookingFetcher({ bookingId, referenceUrl })) as MarsResponse;
			break;
		}
		case 'KINOHELD': {
			if (!referenceUrl) {
				return {
					success: false,
					error: 'CANNOT_GET_TICKET_WITHOUT_REFERENCE_URL',
				};
			}
			const referenceUrlValid = await validateReferenceUrl(referenceUrl, ticketProvider);
			if (!referenceUrlValid) {
				return {
					success: false,
					error: 'INVALID_REFERENCE_URL',
				};
			}

			res = await kinoheldBookingFetcher(referenceUrl);

			break;
		}
		case 'TICKET_INTERNATIONAL': {
			if (!bookingId) {
				return {
					success: false,
					error: 'CANNOT_GET_TICKET_WITHOUT_BOOKING_ID',
				};
			}
			res = (await ticketInternationalBookingFetcher({
				bookingId,
				referenceUrl,
			})) as TicketInternationalResponse;
			break;
		}
		case 'CINETIXX': {
			if (!bookingId) {
				return {
					success: false,
					error: 'CANNOT_GET_TICKET_WITHOUT_BOOKING_ID',
				};
			}
			res = (await cinetixxBookingFetcher(bookingId)) as CinetixxResponse;
			break;
		}
		default: {
			//
		}
	}

	if (!res || !res.data) {
		return {
			success: false,
			error: 'TICKET_DATA_NOT_FOUND',
		};
	}

	if (res.errorMessage) {
		return {
			success: false,
			error: res.errorMessage,
		};
	}

	return { success: true, booking: res.data, ticketInfo };
};

export const getOnlineTicketingBooking = async (
	_: unknown,
	{
		ticketId,
		referenceUrl,
		ticketProvider,
		bookingId,
	}: {
		ticketId?: InternalTicketId;
		referenceUrl?: string;
		ticketProvider?: TicketProvider;
		bookingId?: string;
	},
	{ viewer }: { viewer: Viewer }
) => {
	if (!viewer.isRoot) {
		throw new ForbiddenError(`You must be the root user`);
	}

	const res = await getOnlineTicketingBookingFn({
		ticketId,
		referenceUrl,
		ticketProvider,
		bookingId,
	});

	if (res.error) {
		throw new ApolloError(res.error, res.error);
	}

	return res.booking;
};
