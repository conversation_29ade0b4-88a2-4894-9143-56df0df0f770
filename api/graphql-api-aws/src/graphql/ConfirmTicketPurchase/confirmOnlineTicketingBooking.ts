/* eslint-disable no-await-in-loop */
import { Language } from '@cinuru/utils/i18n';
import {
	InternalCinemaId,
	InternalMovieId,
	InternalScreeningId,
	InternalUserId,
	PublicScreeningId,
	Viewer,
} from '../../typescript-types';
import {
	ApolloError,
	AuthenticationError,
	db,
	ForbiddenError,
	sendErrorMail,
	sql,
} from '../../utils';
import { Screening } from '../Screening/Screening';

import {
	getOnlineTicketingBookingFn,
	Ticket,
	OnlineTicketingResponse,
	TicketProvider,
} from './getOnlineTicketingBooking';

import {
	ONLINE_TICKETING_LANGUAGES,
	STAGE,
	USER_TOKEN_BASED_INTEGRATIONS,
	VOUCHER_CODE_BASED_INTEGRATIONS,
} from '../../../consts';
import { ID } from '@cinuru/utils/types';
import { inspect } from 'util';

export const getFinalBookinId = async ({
	confirmPageLink,
	ticketProvider,
	userTokens,
	bookingId,
}: {
	confirmPageLink: string;
	ticketProvider: string;
	userTokens?: string[];
	bookingId?: string;
}) => {
	console.log('getFinalBookinId', confirmPageLink, ticketProvider, userTokens, bookingId);
	if (ticketProvider === 'MARS') {
		// this is really ugly, but we need to map the mars cartId to the mars bookingId via the userTokens
		const cartId = confirmPageLink.split('cartId=')[1];
		const mapping = (await db.queryOne(
			sql`
				SELECT * FROM priv.p_mars_cart_id_vs_booking_id
				WHERE cart_id = ${cartId}
				`
		)) as { cart_id: string; booking_id: string } | null;

		if (mapping) {
			return mapping.booking_id;
		} else {
			if (userTokens?.length) {
				const rows = (
					await db.query(
						sql`
						SELECT booking_id, cinema_id FROM priv.p_redeem_subscription_token 
						WHERE token = ANY(${userTokens}) 
						AND integration_name = ${ticketProvider}`
					)
				).rows as {
					booking_id: string;
					cinema_id: InternalCinemaId;
				}[];
				// it might happen that a user went into the booking process with two subsription tokens, but then only books one ticket, therefore we need to check both tokens as only for one of them a bookingId will be found
				const finalBookingId =
					!rows || !rows.length ? undefined : rows.filter((r) => r.booking_id)[0]?.booking_id;
				if (!finalBookingId) {
					await sendErrorMail(
						'MARS: No bookingId found for userToken',
						`getFinalBookinId has been called with one or more userToken, but no bookingId was found. ${JSON.stringify(
							{
								confirmPageLink,
								ticketProvider,
								userTokens,
								bookingId,
							}
						)}`
					);
					throw new ApolloError(
						'NO_BOOKING_ID_FOUND_FOR_USER_TOKEN',
						'NO_BOOKING_ID_FOUND_FOR_USER_TOKEN'
					);
				} else {
					await db.query(sql`
						INSERT INTO priv.p_mars_cart_id_vs_booking_id (cart_id, booking_id) VALUES (${cartId}, ${finalBookingId})
					`);
					return finalBookingId;
				}
			} else {
				return cartId;
			}
		}
	} else if (ticketProvider === 'COMPESO') {
		const finalBookingId = 'OLNUE-' + confirmPageLink.split('OLNUE1-')[1].split('?')[0];
		return finalBookingId;
	} else if (ticketProvider === 'KINOHELD') {
		const finalBookingId = confirmPageLink
			.replace('https://www.kinoheld.de/account/order/', '')
			.replace('https://www.kinoheld.de/konto/bestellung/', '')
			.split('/')[0];
		return finalBookingId;
	} else if (ticketProvider === 'CINETIXX') {
		// Sometimes the parsing of the bookingId within the CinetixxWebbookingView was not successfull, therefore we check if the bookingId is valid here
		const isValidBookingId = /^\d+$/.test(bookingId);
		return isValidBookingId ? bookingId : undefined;
	} else {
		//  TICKET_INTERNATIONAL
		return bookingId;
	}
};

const createTicket = async ({
	language,
	screeningId,
	userId,
	bookingId,
	seatsInfo,
	qrCode,
	status,
	refundable,
	movieId,
	screeningDatetime,
	auditoriumName,
	cinemaId,
	movieTitle,
	scanned,
	isSubscriptionTicket,
	voucherCode,
	userToken,
	ticketProvider,
	referenceUrl,
	bookerUserId,
	cancelUrl,
}: {
	language: Language;
	screeningId: InternalScreeningId;
	userId: InternalUserId;
	bookingId: string;
	seatsInfo: string;
	qrCode: string;
	status:
		| 'RESERVATION'
		| 'COMPLETE'
		| 'REFUNDED'
		| 'REFUND_FAILED'
		| 'REFUND_WAITING'
		| 'PREPARATION';
	refundable: boolean;
	movieId: InternalMovieId;
	screeningDatetime: Date;
	auditoriumName: string;
	cinemaId: InternalCinemaId;
	movieTitle: string;
	scanned: boolean;
	isSubscriptionTicket: boolean;
	voucherCode?: string;
	userToken?: string;
	ticketProvider?: string;
	referenceUrl?: string;
	bookerUserId?: InternalUserId;
	cancelUrl?: string;
}): Promise<void> => {
	if (language === 'de-cinfinity') {
		await db.query(sql`
			INSERT INTO "priv"."p_ticket" 
			("showtime_id", "user_id", "cf_booking_id", "seats_info", "qr_code", "bought_at", "status", 
			"refundable", "movie_id", "screening_datetime", "auditorium_name", "cinema_id", "movie_title", "scanned", "is_subscription_ticket", "voucher_code", "ticket_provider", "reference_url", "booker_user_id", "user_token", "cancel_url")	
			VALUES(
				${screeningId},
				 ${userId},
				 ${bookingId},
				 ${seatsInfo},
				 ${qrCode}, 
				 now(),
				 ${status},
				 ${refundable},
				 ${movieId},
				 ${screeningDatetime},
				 ${auditoriumName},
				 ${cinemaId},
				 ${movieTitle},
				 ${scanned},
				 ${isSubscriptionTicket},
				 ${voucherCode || ''},
				 ${ticketProvider || ''}, 
				 ${referenceUrl || ''},
				 ${bookerUserId || null},
				 ${userToken || null},
				 ${cancelUrl || null}
				 );`);
	} else {
		await db.query(sql`
			INSERT INTO "priv"."p_ticket" 
			("showtime_id", "user_id", "cp_booking_id", "seats_info", "qr_code", "bought_at", "status", 
			"refundable", "movie_id", "screening_datetime", "auditorium_name", "cinema_id", "movie_title", "scanned", "is_subscription_ticket")
			VALUES(
				${screeningId},
				 ${userId},
				 ${bookingId},
				 ${seatsInfo},
				 ${qrCode}, 
				 now(),
				 ${status},
				 ${refundable},
				 ${movieId},
				 ${screeningDatetime},
				 ${auditoriumName},
				 ${cinemaId},
				 ${movieTitle},
				 ${scanned},
				 ${isSubscriptionTicket});`);
	}
};

type VoucherCodePerUser = { code: string; user_id: number };

const redeemVoucherCodes = async ({
	voucherCodes,
	userId,
	cinemaId,
}: {
	voucherCodes: string[];
	userId: InternalUserId;
	cinemaId: InternalCinemaId;
}): Promise<VoucherCodePerUser[]> => {
	const voucherCodesPerUser = (
		await db.query(
			sql`
				SELECT 
				code, 
				user_id 
				FROM priv.p_online_ticketing_voucher 
				WHERE booker_user_id = ${userId} 
				AND (status = 'PENDING' OR status = 'FOUND_INVALID')
				AND code = ANY(${voucherCodes})
				AND cinema_id = ${cinemaId}
			`
		)
	).rows as VoucherCodePerUser[];
	console.log('voucherCodesPerUser: ', voucherCodesPerUser);
	console.log('voucherCodes: ', voucherCodes);

	const pendingVoucherCodes = voucherCodesPerUser.map((uvc) => uvc.code);
	if (!voucherCodes.every((vc) => pendingVoucherCodes.includes(vc))) {
		await sendErrorMail(
			'Recorded ticket purchase with voucher codes that were not pending',
			`Error: user ${userId} called recordTicketPurchaseWithConfirmaPageLink with voucherCodes ${voucherCodes}, but ${voucherCodes.filter(
				(vc) => !pendingVoucherCodes.includes(vc)
			)} were not in pending state. This indicates fraud`
		);
		throw new Error('Some vouchers are not pending');
	}

	await db.query(
		sql`
			UPDATE priv.p_online_ticketing_voucher SET 
			status = 'REDEEMED',
			logs = logs || ${{
				[new Date().toISOString()]: `user ${userId}: redeemed`,
			}}::jsonb
			WHERE code = ANY(${voucherCodes}) 
			AND booker_user_id = ${userId}
			AND cinema_id = ${cinemaId}
		`
	);

	return voucherCodesPerUser;
};

const assignUsersToTickets = async ({
	tickets,
	userTokens,
	voucherCodesPerUser,
	ticketProvider,
	bookerUserId,
}: {
	tickets: OnlineTicketingResponse['data']['tickets'];
	userTokens: string[];
	voucherCodesPerUser: VoucherCodePerUser[];
	ticketProvider: string;
	bookerUserId: InternalUserId;
}): Promise<
	({
		user_id: InternalUserId;
		voucherCode?: string;
		token?: string;
		isSubscriptionTicket: boolean;
	} & Ticket)[]
> => {
	// make sure that subscription tickets are coming first as we assign the vouchers and codes to them first
	const sortedTickets = tickets.slice().sort((a) => (a.isSubscriptionTicket ? -1 : 1));

	let usersVsTokens: { user_id: InternalUserId; token: string }[] = [];
	if (USER_TOKEN_BASED_INTEGRATIONS.includes(ticketProvider)) {
		usersVsTokens = (
			await db.query(sql`
			SELECT user_id, token FROM priv.p_user_token WHERE token = ANY(${userTokens})
			`)
		).rows;
	}
	const result = sortedTickets.map((t) => {
		const next = (voucherCodesPerUser.pop() || usersVsTokens.pop()) as
			| { user_id: InternalUserId; code: string; token: undefined }
			| { user_id: InternalUserId; code: undefined; token: string }
			| undefined;

		return {
			...t,
			user_id: next?.user_id || bookerUserId,
			voucherCode: next?.code,
			token: next?.token,
			// if t.isSubscriptionTicket is true, the OTS defines which ticket is a subscription ticket, otherwise we define it based on the next token or code
			isSubscriptionTicket: Boolean(t.isSubscriptionTicket || next?.code || next?.token),
		};
	});
	return result;
};

const upsertConfirmOnlineTicketingBookingRequest = async ({
	userId,
	confirmPageLink,
	bookingId,
	data,
	confirmed,
}: {
	userId: InternalUserId;
	confirmPageLink?: string;
	bookingId?: string;
	data?: { screeningId: PublicScreeningId; voucherCodes?: string[]; userTokens?: string[] };
	confirmed?: boolean;
}): Promise<void | ID> => {
	const confirmPageLinkExists =
		confirmPageLink &&
		((await db.queryOne(sql`
		(
			SELECT id FROM priv.p_confirm_online_ticketing_booking_request
			WHERE confirm_page_link = ${confirmPageLink}
		)
	`)) as { id: ID } | null);

	const bookingIdExists =
		bookingId &&
		((await db.queryOne(sql`
		(
			SELECT id FROM priv.p_confirm_online_ticketing_booking_request
			WHERE booking_id = ${bookingId} AND screening_id = ${data?.screeningId}
		)
	`)) as { id: ID } | null);

	const exists = confirmPageLinkExists || bookingIdExists;

	if (!exists) {
		await db.query(
			sql`INSERT INTO priv.p_confirm_online_ticketing_booking_request
			(user_id, confirm_page_link, booking_id, data, datetime, screening_id)
			VALUES 
			(${userId}, ${confirmPageLink}, ${bookingId}, ${JSON.stringify(data)}, NOW(), ${data?.screeningId})
			`
		);
	} else {
		if (confirmed) {
			await db.query(sql`
			UPDATE priv.p_confirm_online_ticketing_booking_request
			SET is_confirmed = TRUE
			WHERE id = ${exists.id}
		`);
		} else {
			const res = await db.query(sql`
			UPDATE priv.p_confirm_online_ticketing_booking_request
			SET retry_count = retry_count + 1
			WHERE id = ${exists.id}
			RETURNING retry_count;
			`);
			if (res.rows[0].retry_count === 10) {
				await sendErrorMail(
					'Retry count reached 10',
					`Error: Retry count reached 10 for ${exists.id}`
				);
			}
		}
	}
};

export const confirmOnlineTicketingBookingFn = async ({
	userIdDb,
	language,
	screeningId,
	confirmPageLink,
	bookingId,
	voucherCodes,
	userTokens,
}: {
	userIdDb: InternalUserId;
	language: Language;
	screeningId: PublicScreeningId;
	confirmPageLink?: string; // KINOHELD, MARS, COMPESO, TICKET_INTERNATIONAL
	bookingId?: string; // CINETIXX (their booking id), TICKET_INTERNATIONAL (our generated reference id)
	voucherCodes?: string[];
	userTokens?: string[];
}): Promise<{ success: true; movieTitle: string; screeningDatetime: Date }> => {
	await upsertConfirmOnlineTicketingBookingRequest({
		userId: userIdDb,
		confirmPageLink,
		bookingId,
		data: {
			screeningId,
			voucherCodes,
			userTokens,
		},
	});

	const screeningInfo = (await db.queryOne(sql`
		SELECT
		s.movie_id, 
		s.cinema_id,
		s.datetime,
		m.title as movie_title,
		c.ticket_provider
		FROM priv.p_showtime s
		JOIN priv.p_movie m ON s.movie_id = m.id
		JOIN priv.p_cinema c ON s.cinema_id = c.id
		WHERE s.id = ${Screening.decodePublicId(screeningId)}
	`)) as {
		movie_id: InternalMovieId;
		movie_title: string;
		datetime: Date;
		cinema_id: InternalCinemaId;
		ticket_provider: TicketProvider;
	} | null;

	if (!screeningInfo) {
		throw new ForbiddenError('SCREENING_NOT_FOUND');
	}

	const ticketProvider = screeningInfo.ticket_provider;
	console.log('ticketProvider: ', ticketProvider);

	if (!ticketProvider) {
		throw new ForbiddenError('NO_TICKET_PROVIDER');
	}

	const finalBookingId = await getFinalBookinId({
		confirmPageLink,
		ticketProvider,
		userTokens,
		bookingId,
	});
	console.log('finalBookingId: ', finalBookingId);

	if (!finalBookingId) {
		throw new ForbiddenError('NO_BOOKING_ID');
	}

	let existing;
	if (language === 'de-cinfinity') {
		existing = await db.queryOne(sql`
			SELECT 
				(
					SELECT count(*) FROM priv.p_ticket 
					WHERE cf_booking_id =${finalBookingId} 
					AND cinema_id = ${screeningInfo.cinema_id} 
				) as count_tickets,
				(
					SELECT count(*) FROM priv.p_order 
					WHERE foreign_order_id =${finalBookingId} 
					AND cinema_id = ${screeningInfo.cinema_id} 
				) as count_concessions
			`);
	} else {
		existing = await db.queryOne(sql`
			SELECT 
				(SELECT count(*) FROM priv.p_ticket WHERE cp_booking_id =${finalBookingId}) as count_tickets,
				(SELECT count(*) FROM priv.p_order WHERE foreign_order_id =${finalBookingId}) as count_concessions
			`);
	}

	if (existing.count_tickets > 0 || existing.count_concessions > 0) {
		throw new ForbiddenError('TICKET_ALREADY_EXISTS');
	}

	const { success, error, booking } = await getOnlineTicketingBookingFn({
		referenceUrl: confirmPageLink,
		ticketProvider,
		bookingId: finalBookingId,
	});

	if (!success) {
		throw new ApolloError(error, 'GET_ONLINE_TICKETING_BOOKING_ERROR');
	}

	console.log('ticketData: ', inspect(booking, { depth: null, colors: true }));

	// for cinetixx, we need to check if there is at least one ticket that is open, as individual tickets can be cancelled here
	if (
		booking.ticketStatus === 'cancelled' ||
		booking.tickets.every((t) => t.ticketStatus === 'cancelled')
	) {
		throw new ForbiddenError('TICKET_NOT_OPEN');
	}

	// set pending vouchers to redeemed state
	let voucherCodesPerUser: undefined | VoucherCodePerUser[];
	if (VOUCHER_CODE_BASED_INTEGRATIONS.includes(ticketProvider)) {
		// for compeso we get the info about the vouchers from their api
		const finalVoucherCodes =
			ticketProvider === 'COMPESO'
				? booking.tickets.map((t) => t.code).filter((v) => v) // some tickets might not have a voucher code, filter those vouchers
				: voucherCodes; // TODO: also rely on the api voycher codes for ticket international
		if (finalVoucherCodes?.length) {
			if (booking.tickets.length < finalVoucherCodes.length) {
				// this should never happen, but if it does, it indicates fraud
				await sendErrorMail(
					'number of tickets on confirmPageLink does not fit number of voucher codes',
					`Error: a confirmpageLink has ${booking.tickets.length} tickets on it, but ${
						voucherCodes.length
					} voucherCodes were provided.
					 ${JSON.stringify({
							confirmPageLink,
							screeningId,
							bookerUserId: userIdDb,
						})}`
				);
				throw new Error('Not enough tickets for the provided voucher codes');
			}

			voucherCodesPerUser = await redeemVoucherCodes({
				voucherCodes: finalVoucherCodes,
				userId: userIdDb,
				cinemaId: screeningInfo.cinema_id,
			});

			if (!voucherCodesPerUser.length) {
				throw new ApolloError('VOUCHER_CODE_REDEEM_PROBLEM', 'VOUCHER_CODE_REDEEM_PROBLEM');
			}
		}
	}

	// this usually already happened on the redeem endpoint, but this does not work when we are in local development
	if (
		STAGE === 'development' &&
		userTokens?.length &&
		USER_TOKEN_BASED_INTEGRATIONS.includes(ticketProvider)
	) {
		await db.query(sql`
			UPDATE priv.p_user_token
			SET valid_until = NOW()
			WHERE token = ANY(${userTokens})
		`);
	}

	// Insert into Tickets
	if (booking.tickets && booking.tickets.length > 0) {
		const ticketsPerUser = await assignUsersToTickets({
			tickets: booking.tickets,
			userTokens,
			voucherCodesPerUser: voucherCodesPerUser || [],
			ticketProvider,
			bookerUserId: userIdDb,
		});
		console.log('ticketsPerUser: ', ticketsPerUser);

		const promises = ticketsPerUser.map(async (tpu) => {
			await createTicket({
				screeningId: Screening.decodePublicId(screeningId),
				bookingId: finalBookingId,
				qrCode: tpu.qrCode || booking.ticketsQRCode,
				status: 'COMPLETE' as const,
				refundable: tpu.isCancelable ?? booking.isCancelable,
				movieId: screeningInfo.movie_id,
				screeningDatetime: screeningInfo.datetime,
				auditoriumName: booking.auditorium,
				cinemaId: screeningInfo.cinema_id,
				movieTitle: screeningInfo.movie_title,
				scanned: false,
				language,
				seatsInfo: JSON.stringify([{ seatName: tpu.seatName, rowName: tpu.rowName }]),
				isSubscriptionTicket: tpu.isSubscriptionTicket,
				userId: tpu.user_id,
				voucherCode: tpu.voucherCode,
				userToken: tpu.token,
				ticketProvider,
				referenceUrl: confirmPageLink,
				bookerUserId: userIdDb,
				cancelUrl: tpu.cancelUrl,
			});
		});
		await Promise.all(promises);
	}

	// Insert into Orders

	if (booking.concessionLineItems && booking.concessionLineItems.length > 0) {
		console.log(
			'concessions',
			JSON.stringify(
				booking.concessionLineItems.map((t, idx) => ({
					id: `${finalBookingId}_${idx}`,
					name: t.name,
					price: t.price,
				}))
			)
		);
		await db.queryOne(sql`
		INSERT INTO 
			"priv"."p_order" ("user_id", "pickup_code", "foreign_order_id", "refundable", "status", "datetime", "cinema_id", 
			"screening_id", "transaction_id", "line_items", "qr_code_image") 
			VALUES (
				${userIdDb},
				${booking.concessionsPickupCode}, 
				${finalBookingId}, 
				't', 
				'INITIAL', 
				${screeningInfo.datetime}, 
				${screeningInfo.cinema_id}, 
				${Screening.decodePublicId(screeningId)}, 
				${finalBookingId}, 
				${JSON.stringify(
					booking.concessionLineItems.map((t, idx) => ({
						id: `${finalBookingId}_${idx}`,
						name: t.name,
						price: t.price,
						amount: t.amount,
						subItems: [],
					}))
				)},
				${booking.concessionsPickupCodeImage});
		`);
	}

	await upsertConfirmOnlineTicketingBookingRequest({
		userId: userIdDb,
		confirmPageLink,
		bookingId,
		data: {
			screeningId,
			voucherCodes,
			userTokens,
		},
		confirmed: true,
	});

	return {
		success: true,
		movieTitle: screeningInfo.movie_title,
		screeningDatetime: screeningInfo.datetime,
	};
};

// create own file for this
export const confirmOnlineTicketingBooking = async (
	_: unknown,
	{
		screeningId,
		confirmPageLink,
		voucherCodes,
		bookingId,
		userTokens,
	}: {
		screeningId: PublicScreeningId;
		confirmPageLink?: string;
		voucherCodes?: string[];
		bookingId?: string;
		userTokens?: string[];
	},
	{ viewer, language }: { viewer: Viewer; language: Language }
): Promise<{ success: boolean; movieTitle: string; screeningDatetime: Date }> => {
	console.log('confirmOnlineTicketingBooking', {
		screeningId,
		confirmPageLink,
		voucherCodes,
		bookingId,
		userTokens,
		userIdDb: viewer.userIdDb,
		language,
	});

	const userIdDb = viewer.userIdDb;

	if (!userIdDb) {
		throw new AuthenticationError('User not authenticated');
	}

	// If we add more cinemas, make sure to resolve cinema name from booking id
	if (!ONLINE_TICKETING_LANGUAGES.includes(language)) {
		throw new ForbiddenError(
			'Ticket purchase of external ticket provider not supported for this brand'
		);
	}

	if (!confirmPageLink && !bookingId) {
		throw new ApolloError('Either confirmPageLink or bookingId must be provided');
	}

	return await confirmOnlineTicketingBookingFn({
		userIdDb,
		language,
		screeningId,
		confirmPageLink,
		voucherCodes,
		bookingId,
		userTokens,
	});
};
