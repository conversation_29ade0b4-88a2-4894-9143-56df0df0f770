/* eslint-disable no-await-in-loop */
import { Language } from '@cinuru/utils/i18n';
import { InternalUserId, Viewer } from '../../typescript-types';
import { AuthenticationError, db, ForbiddenError, sql } from '../../utils';

import {
	CONCESSION_INTEGRATIONS,
	ONLINE_TICKETING_INTEGRATIONS,
	ONLINE_TICKETING_LANGUAGES,
} from '../../../consts';
import { InternalTicketId, PublicTicketId, Ticket } from '../Ticket/Ticket';
import { getOnlineTicketingBookingFn } from './getOnlineTicketingBooking';

export const syncTicketsFn = async (ticketIds: InternalTicketId[]) => {
	const errors: {
		ticketId: InternalTicketId;
		errorMessage: string;
	}[] = [];

	const promises = ticketIds.map(async (ticketId) => {
		const { success, error, booking, ticketInfo } = await getOnlineTicketingBookingFn({
			ticketId,
		});
		if (!success) {
			errors.push({
				ticketId,
				errorMessage: error,
			});

			//TODO: Discuss this, I think we should not do tihs, especially for old tickets as they might not exist in OLT anymore
			// await db.queryOne(sql`
			// 		UPDATE priv.p_ticket
			// 		SET
			// 		status = 'UNKNOWN'
			// 		WHERE id = ${ticketId}
			// 	`);
		} else {
			let isRefundable = booking.isCancelable;
			let ticketStatus = booking.ticketStatus;
			if (ticketInfo.ticket_provider === 'CINETIXX') {
				// for cinetixx we need to check the ticket itself, as cinetixx tickets are cancellable individually
				const ticket = booking.tickets.find((bt) => bt.qrCode === ticketInfo.qr_code);
				isRefundable = ticket?.isCancelable;
				ticketStatus = ticket?.ticketStatus;
			}
			if (ticketInfo.ticket_provider === 'KINOHELD' && ticketStatus !== 'cancelled') {
				// for kinoheld isRefundable is determined on booking level, however we might have ticket level scanned status
				const ticket = booking.tickets.find((bt) => bt.qrCode === ticketInfo.qr_code);
				ticketStatus = ticket?.ticketStatus;
			}

			const updatedTicketStatus =
				ticketStatus === 'cancelled'
					? 'REFUNDED'
					: ticketStatus === 'redeemed'
					? 'COMPLETE'
					: ticketStatus === 'open'
					? 'COMPLETE'
					: ticketStatus;

			await db.queryOne(sql`
					UPDATE priv.p_ticket
					SET 
					status = ${updatedTicketStatus},
					refunded_at = COALESCE(refunded_at,CASE WHEN ${
						updatedTicketStatus === 'REFUNDED'
					} THEN NOW() ELSE NULL END),
					refundable = ${isRefundable}
					WHERE id = ${ticketId}
				`);

			// make sure to cancel related concessions if ticket was cancelled
			if (
				updatedTicketStatus === 'REFUNDED' &&
				CONCESSION_INTEGRATIONS.includes(ticketInfo.ticket_provider)
			) {
				await db.query(sql`
						UPDATE priv.p_order
						SET 
						status = ${updatedTicketStatus},
						refundable = ${isRefundable}
						WHERE foreign_order_id = ${ticketInfo.cf_booking_id}
					`);
			}

			// make sure to mark used voucher codes as available again
			if (updatedTicketStatus === 'REFUNDED' && ticketInfo.voucher_code) {
				// when the refunded ticket has a voucher code associated, make sure to remove the voucher code from the ticket, so we don't get here again
				// and the cancelling user does not set the voucher code to open again, although it may already be used by another ticket already
				await db.query(sql`UPDATE priv.p_ticket SET voucher_code = null WHERE id = ${ticketId}`);
				await db.query(sql`
						UPDATE priv.p_online_ticketing_voucher
						SET
						status = null,
						booker_user_id = null,
						user_id = null,
						updated_at = NOW(),
						confirm_page_link = null,
						logs = logs || ${{
							[new Date().toISOString()]: `user ${ticketInfo.user_id} cancelled ticket: redeemed => null`,
						}}::jsonb
						WHERE code = ${ticketInfo.voucher_code} AND cinema_id = ${ticketInfo.cinema_id}`);
			}
			// eslint-disable-next-line no-catch-all/no-catch-all
		}
	});

	if (errors.length > 0) {
		console.log('>>> syncTickets errors', errors);
	}

	await Promise.all(promises);
};

export const syncAllTickets = async ({ userIdDb }: { userIdDb: InternalUserId }) => {
	try {
		const ticketsToBeChecked = (
			await db.query(
				sql`
					WITH cf_booking_ids AS (
						SELECT DISTINCT cf_booking_id
						FROM priv.p_ticket
						WHERE user_id = ${userIdDb}
						AND ticket_provider = ANY (${ONLINE_TICKETING_INTEGRATIONS})
						AND screening_datetime > NOW() 
						AND (status = 'COMPLETE' OR status = 'UNKNOWN')
						AND scanned IS NOT TRUE
					)
					SELECT id FROM priv.p_ticket WHERE cf_booking_id IN (
						SELECT cf_booking_id
						FROM cf_booking_ids
					)`
			)
		).rows as { id: InternalTicketId }[];

		console.log('>>> number ticketsToBeChecked', ticketsToBeChecked.length);

		if (ticketsToBeChecked.length === 0) {
			return;
		}

		await syncTicketsFn(ticketsToBeChecked.map((t) => t.id));
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (e) {
		console.log('>>> syncTIckets error', e);
	}
};

export const syncTicket = async (
	_: unknown,
	{
		id,
	}: {
		id: PublicTicketId;
	},
	{ viewer, language }: { viewer: Viewer; language: Language }
) => {
	if (!viewer.userIdDb) {
		throw new AuthenticationError('User not authenticated');
	}

	// If we add more cinemas, make sure to resolve cinema name from booking id
	if (!ONLINE_TICKETING_LANGUAGES.includes(language)) {
		throw new ForbiddenError(
			'Ticket purchase of external ticket provider not supported for this brand'
		);
	}

	const ticketIdDb = Ticket.decodePublicId(id);

	const ticketInfos = (
		await db.query(sql`
            SELECT id, user_id FROM priv.p_ticket WHERE cf_booking_id IN (SELECT cf_booking_id FROM priv.p_ticket WHERE id = ${ticketIdDb})
    `)
	).rows as {
		user_id: InternalUserId;
		id: InternalTicketId;
	}[];

	const ticket = ticketInfos.find((t) => t.id === ticketIdDb);
	if (!ticket) {
		throw new ForbiddenError('Ticket not found');
	}

	const ticketBelongsToUser = ticket.user_id === viewer.userIdDb;

	if (!ticketBelongsToUser && !viewer.isRoot) {
		throw new ForbiddenError('Ticket does not belong to user');
	}

	// make sure we also sync all related tickets
	await syncTicketsFn(ticketInfos.map((t) => t.id));
	const updatedTicket = await Ticket.gen(viewer, id);

	return updatedTicket;
};
