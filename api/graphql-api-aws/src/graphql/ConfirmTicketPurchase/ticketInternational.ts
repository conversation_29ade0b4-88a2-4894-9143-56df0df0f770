import fastXmlParser from 'fast-xml-parser';
import he from 'he';
import fetch from 'node-fetch';
import { InternalCinemaId } from '../../typescript-types';
import { db, sql } from '../../utils';

type TicketInternationalBooking = {
	iD: string;
	status: 'presale' | 'pending' | 'collected'; // Status „presale“ bedeutet „Buchung fertig“. „Pending“: dann läuft die Buchung noch; Wenn die Tickets im Kino gescannt wurden, dann ändert sich der Status auf „collected“
	totalAmount: number;
	unpaidAmount: number;
	paidAmount: number;
	barcode: string;
	reservationNumber: string;
	categoryChanged: boolean;
	transactionFee: number;
	transactionFeeTaxTypeID: number;
	feeTickets: number;
	invoiceNumber: string;
	checkoutType: number;
	totalPoints: number;
	outstandingPoints: number;
	tickets: number;
	externalOrderNr: string;
	refundAllowed: {
		allowed: boolean;
		time: string;
		minutes: number;
	};
	seat: {
		seatName: string;
		rowName: string;
		iD: number;
		entrance: string;
		barcode: string;
		auditoriumName: string;
	};
	customer: {
		firstName: string;
		lastName: string;
		middleName: string;
		email: string;
		phone: string;
		mobile: string;
	};
	site: {
		cityName: string;
		zipCode: string;
		address: string;
		phone: string;
		regionID: number;
		email: string;
		companyName: string;
		companyID: number;
		name: string;
		iD: number;
	};
	customers: any[];
	deliveryAddress: any;
	invoiceAddress: any;
	payments: {
		voucher: any[];
		creditcard: any[];
		cash: any[];
		card: any[];
		paymentMethods: any[];
	};
	shows: {
		showId: number;
		screen: {
			number: string;
			short: string;
			name: string;
			iD: number;
		};
		cinema: {
			name: string;
			iD: number;
		};
		movie: {
			additionalName: string;
			country: string;
			regie: string;
			exportNumber: string;
			eventLength: number;
			cast: string;
			synopsis: string;
			rating: string;
			ratingShort: string;
			frame: string;
			frameID: number;
			genres: string[];
			eventTypeID: number;
			name: string;
			iD: number;
		};
		showDateTime: string;
		showEndTime: string;
		notSeated: boolean;
		version: {
			shortcut: string;
			name: string;
			iD: number;
		};
		exportNumber: string;
		eventLength: number;
		totalLength: number;
		rating: string;
		note: string;
		seatVariantID: number;
		tickets: {
			priceID: number;
			moneyPrice: number;
			totalMoneyPrice: number;
			pointPrice: number;
			paidBy: string;
			printName: string;
			hidePrice: boolean;
			seatingCategory: {
				name: string;
				iD: number;
			};
			reservationID: number;
			barcode: string;
			sequenceNr: string;
			refunded: boolean;
			paymentRefs: {
				reference: string;
				cardOwner: string;
				cardNumber: string;
				accountID: number;
			}[];
			priceTypeID: number;
			isCombiTicket: boolean;
			taxTypeID: number;
			seats: {
				seatName: string;
				rowName: string;
				iD: number;
				entrance: string;
				barcode: string;
				auditoriumName: string;
			}[];
			paymentTypes: any[];
			surcharges: any[];
			name: string;
			iD: number;
		}[];
		ticketsSummary: {
			count: number;
			price: number;
			name: string;
			iD: number;
		}[];
	}[];
	concessions: any[];
	vouchers: any[];
	loadings: any[];
	cardLevelChanges: any[];
	vats: {
		vATType: string;
		percentage: number;
		tax: number;
		gross: number;
		iD: number;
	}[];
	tSE: any;
	employee: string;
	siteName: string;
	vATNumber: string;
	time: string;
	itemsSummary: {
		summary: any[];
	};
	loyaltyPointsEarned: number;
};

type CardsResponse = {
	Card: {
		Number: string;
		Prefix: string;
		Name: string;
		Type: string;
		CustomerID: string;
		AmountTickets: string;
		State: 'active' | 'redeemed';
		ExpiryDate: string;
		StartDate: string;
		IsVoucher: string;
	};
};

const getBaseUrl = async (
	cinemaIdDb: InternalCinemaId
): Promise<{ baseUrl: string; apiKey: string }> => {
	const apiBaseUrl = (
		await db.queryOne(sql`SELECT api_base_url FROM priv.p_cinema WHERE id = ${cinemaIdDb}`)
	).api_base_url; // e.g. https://api.klein-koblenz.ticketserver.net?apikey=124312312312
	const [baseUrl, apiKey] = apiBaseUrl.split('?apikey=');
	return { baseUrl, apiKey };
};

export const getTicketInternationalVoucherCodeValidity = async ({
	cinemaIdDb,
	voucherCode,
}: {
	cinemaIdDb: InternalCinemaId;
	voucherCode: string;
}): Promise<{ success: boolean; error?: string; valid?: boolean }> => {
	try {
		if ([307, 308, 310, 314].includes(cinemaIdDb as number)) {
			//for these cinemas we know that the voucher code is always valid
			return { success: true, error: null, valid: true };
		}
		const { baseUrl, apiKey } = await getBaseUrl(cinemaIdDb);
		const url = `${baseUrl}/2.0/cards/${voucherCode}?apikey=${apiKey}`;
		const xmlRes = await fetch(url, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
			},
		});
		if (xmlRes.status !== 200) {
			if (xmlRes.status === 400 || xmlRes.status === 404) {
				// the voucher code was not even found (404), or the voucher code is too long (404)
				return { success: true, error: null, valid: false };
			} else {
				return { success: false, error: JSON.stringify(xmlRes) };
			}
		}

		const text = await xmlRes.textConverted();
		const parsedXML = fastXmlParser.parse(text, {
			attributeNamePrefix: '',
			parseAttributeValue: true,
			ignoreAttributes: false,
			attrValueProcessor: (a: string) => he.decode(a, { isAttributeValue: true }),
		}) as CardsResponse;

		return { success: true, error: null, valid: parsedXML.Card.State === 'active' };
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (e) {
		return { success: false, error: e.message };
	}
};

export const getTicketInternationalBooking = async ({
	referenceId, // we create the reference id in the TicketInternationalBookingView
	cinemaIdDb,
}: {
	referenceId: string;
	cinemaIdDb: InternalCinemaId;
}): Promise<{ success: boolean; error?: string; data?: TicketInternationalBooking }> => {
	try {
		const { baseUrl, apiKey } = await getBaseUrl(cinemaIdDb);
		const url = `${baseUrl}/3.0/transactions/cinfinity/${referenceId}?apikey=${apiKey}`;
		const booking = (await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
		}).then((res) => res.json())) as TicketInternationalBooking;
		return { success: true, error: null, data: booking };
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (e) {
		return { success: false, error: e.message };
	}
};

export const cancelTicketInternationalBooking = async ({
	referenceId,
	cinemaIdDb,
}: {
	referenceId: string;
	cinemaIdDb: InternalCinemaId;
}): Promise<{ success: boolean; error?: string }> => {
	try {
		const bookingRes = await getTicketInternationalBooking({ referenceId, cinemaIdDb });
		if (!bookingRes.success) {
			return { success: false, error: bookingRes.error };
		}
		const transactionId = bookingRes.data?.iD;
		const { baseUrl, apiKey } = await getBaseUrl(cinemaIdDb);
		const url = `${baseUrl}/3.0/transactions/${transactionId}/refund?apikey=${apiKey}`;
		const res = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
		});
		if (!res.ok) {
			const error = await res.json();
			return { success: false, error: error.message };
		}
		return { success: true, error: null };
		// eslint-disable-next-line no-catch-all/no-catch-all
	} catch (e) {
		return { success: false, error: e.message };
	}
};
