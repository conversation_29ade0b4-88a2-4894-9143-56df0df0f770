import { PublicCinemaOperatingCompanyId, PublicCinemaId } from '../../typescript-types';
import { db, sql } from '../../utils';
import { Cinema } from '../Cinema/Cinema';
import { CinemaOperatingCompany } from './CinemaOperatingCompany';

export async function linkCinemaOperatingCompanyToCinemas(
	cinemaIds: PublicCinemaId[],
	cinemaOperatingCompanyId: PublicCinemaOperatingCompanyId | null
): Promise<number> {
	if (!cinemaIds.length) {
		return 0;
	}

	const res = await db.query(sql`
            UPDATE priv.p_cinema
            SET cinema_operating_company_id = ${
							cinemaOperatingCompanyId
								? CinemaOperatingCompany.decodePublicId(cinemaOperatingCompanyId)
								: null
						}
            WHERE id = ANY(${cinemaIds.map((c) => Cinema.decodePublicId(c))})
        `);

	return res.rowCount;
}

export async function companyCinemas(id: PublicCinemaOperatingCompanyId) {
	const cinemas = await db.query(sql`
				SELECT id, name FROM priv.p_cinema WHERE cinema_operating_company_id = ${CinemaOperatingCompany.decodePublicId(
					id
				)}
			`);

	if (!cinemas?.rows?.length) {
		return [];
	}

	return cinemas.rows.map((c) => ({ ...c, id: Cinema.encodeDbId(c.id) }));
}
