import { DEFAULT_APP_BRAND } from '../../../consts';
import { Context, PublicCinemaOperatingCompanyId, PublicCinemaId } from '../../typescript-types';
import { ApolloError } from '../../utils';
import { CinemaOperatingCompany } from './CinemaOperatingCompany';
import { companyCinemas, linkCinemaOperatingCompanyToCinemas } from './utils';

export type CreateCustomerCinemaData = {
	name: string;
	accessRightDashboard: boolean;
	accessRightFilmStatistics: boolean;
	accessRightBonusProgram: boolean;
	accessRightCampaigning: boolean;
	externalCustomerNumber?: string;
	pricingScheme?: 'Dynamic' | 'Flat';
	pricingLevel?: 'BASIC' | 'PREMIUM';
	targetGroupFilterConfig?: object[];
	cinemasIds?: PublicCinemaId[];
};

export async function createCinemaOperatingCompany(
	_,
	{ data }: { data: CreateCustomerCinemaData },
	ctx: Context
) {
	if (!['CINFINITY', 'CINFINITY-WEB', 'CINURU'].includes(DEFAULT_APP_BRAND)) {
		return null;
	}

	const newCinemaOperatingCompany = await CinemaOperatingCompany.createCustomerCinema(
		ctx.viewer,
		data
	);

	if (data.cinemasIds?.length) {
		await linkCinemaOperatingCompanyToCinemas(data.cinemasIds, newCinemaOperatingCompany.id);
	}

	return newCinemaOperatingCompany;
}

export async function updateCinemaOperatingCompany(
	_,
	{ id, data }: { id: PublicCinemaOperatingCompanyId; data: CreateCustomerCinemaData },
	ctx: Context
) {
	if (!['CINFINITY', 'CINFINITY-WEB'].includes(DEFAULT_APP_BRAND)) {
		return null;
	}

	const updatedCinemaOperatingCompany = await CinemaOperatingCompany.updateCustomerCinema(
		ctx.viewer,
		id,
		data
	);

	if (!updatedCinemaOperatingCompany) {
		throw new ApolloError('Could not update Cinema Operating Company');
	}

	if (data.cinemasIds) {
		const previousAssociatedCinemas = await companyCinemas(updatedCinemaOperatingCompany.id);

		if (previousAssociatedCinemas.length) {
			await linkCinemaOperatingCompanyToCinemas(
				previousAssociatedCinemas.map((c) => c.id),
				null
			);
		}

		await linkCinemaOperatingCompanyToCinemas(data.cinemasIds, id);
	}

	return updatedCinemaOperatingCompany;
}
