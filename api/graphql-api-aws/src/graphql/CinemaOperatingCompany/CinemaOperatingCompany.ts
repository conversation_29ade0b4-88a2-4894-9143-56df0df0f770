import lodash from 'lodash';

import {
	db,
	encodeDbId,
	decodePublicId,
	EntityNotFoundError,
	sql,
	ForbiddenError,
} from '../../utils';
import {
	Context,
	InternalCinemaOperatingCompanyId,
	PublicCinemaOperatingCompanyId,
	Viewer,
} from '../../typescript-types';
import { User, VoucherClass } from '..';
import { CreateCustomerCinemaData } from './queries';
import { ensureIsAdmin } from '../../utils/auth';
import { companyCinemas } from './utils';

export class CinemaOperatingCompany {
	id: PublicCinemaOperatingCompanyId;
	name: string;
	accessRightDashboard: boolean;
	accessRightFilmStatistics: boolean;
	accessRightBonusProgram: boolean;
	accessRightCampaigning: boolean;
	targetGroupFilterConfig: [{ name: string; showFilter: boolean }];

	constructor(data: CinemaOperatingCompany) {
		Object.assign(this, data);
	}
	static encodeDbId(dbId: InternalCinemaOperatingCompanyId): PublicCinemaOperatingCompanyId {
		return encodeDbId('CinemaOperatingCompany', dbId as number);
	}
	static decodePublicId(id: PublicCinemaOperatingCompanyId): InternalCinemaOperatingCompanyId {
		return decodePublicId(
			'CinemaOperatingCompany',
			id as string
		) as InternalCinemaOperatingCompanyId;
	}
	private static async queryByIds(
		ids: PublicCinemaOperatingCompanyId[]
	): Promise<CinemaOperatingCompany[]> {
		const res = await db.query(sql`
			SELECT 
				id,
				name, 
				external_customer_nr as "externalCustomerNumber",
				pricing_scheme as "pricingScheme",
				pricing_level as "pricingLevel",
				targetgroup_filter_config as "targetGroupFilterConfig",
				access_right_film_statistics as "accessRightFilmStatistics",
				access_right_dashboard as "accessRightDashboard",
				access_right_bonus_program as "accessRightBonusProgram",
				access_right_campaigning as "accessRightCampaigning"
			FROM 
				priv.p_cinema_operating_company 
			WHERE 
				id=ANY(${ids.map((id) => CinemaOperatingCompany.decodePublicId(id))})`);

		return (
			res.rows?.map(
				(c) =>
					new CinemaOperatingCompany({
						...c,
						targetGroupFilterConfig: c.targetGroupFilterConfig ?? [],
						id: CinemaOperatingCompany.encodeDbId(c.id),
					})
			) || []
		);
	}
	static async gen(
		viewer: Viewer,
		id: PublicCinemaOperatingCompanyId
	): Promise<CinemaOperatingCompany> {
		ensureIsAdmin(viewer);

		const res = await this.queryByIds([id]);
		if (res.length === 0) {
			throw new EntityNotFoundError('No Cinema operating company with this id');
		}

		return res[0];
	}

	/**
	 *
	 * @param overrideAccess
	 * only for when the user is cinema_admin and you know it belongs to him ahead of time
	 */
	static async genMult(
		viewer: Viewer,
		ids: PublicCinemaOperatingCompanyId[],
		/*
		 * only for when the user is cinema_admin and you know it belongs to him ahead of time
		 */
		overrideAccess: boolean = false
	) {
		if (!overrideAccess) {
			ensureIsAdmin(viewer);
		}

		return await this.queryByIds(ids);
	}

	static async createCustomerCinema(
		viewer: Viewer,
		data: CreateCustomerCinemaData
	): Promise<CinemaOperatingCompany> {
		ensureIsAdmin(viewer);

		const res = await db.query(sql`
				INSERT INTO priv.p_cinema_operating_company (name,access_right_dashboard,access_right_film_statistics,access_right_bonus_program,access_right_campaigning,external_customer_nr,pricing_scheme,pricing_level,targetgroup_filter_config)
				VALUES (
					${data.name},
					${data.accessRightDashboard},
					${data.accessRightFilmStatistics},
					${data.accessRightBonusProgram},
					${data.accessRightCampaigning},
					${data.externalCustomerNumber},
					${data.pricingScheme},
					${data.pricingLevel},
					${data.targetGroupFilterConfig}
				)
				RETURNING id
			`);

		if (!res.rowCount) {
			return null;
		}

		return CinemaOperatingCompany.gen(viewer, CinemaOperatingCompany.encodeDbId(res.rows[0].id));
	}

	static async updateCustomerCinema(
		viewer: Viewer,
		id: PublicCinemaOperatingCompanyId,
		data: CreateCustomerCinemaData
	): Promise<CinemaOperatingCompany> {
		ensureIsAdmin(viewer);

		const res = await db.query(sql`
				UPDATE priv.p_cinema_operating_company
				SET 
				name = ${data.name},
				access_right_dashboard = ${data.accessRightDashboard},
				access_right_film_statistics = ${data.accessRightFilmStatistics},
				access_right_bonus_program = ${data.accessRightBonusProgram},
				access_right_campaigning = ${data.accessRightCampaigning},
				external_customer_nr = ${data.externalCustomerNumber},
				pricing_scheme = ${data.pricingScheme},
				pricing_level = ${data.pricingLevel},
				targetgroup_filter_config = ${data.targetGroupFilterConfig}
				WHERE id = ${CinemaOperatingCompany.decodePublicId(id)}
			`);

		if (!res.rowCount) {
			return null;
		}

		return CinemaOperatingCompany.gen(viewer, id);
	}

	async cinemas() {
		return companyCinemas(this.id);
	}

	async associatedUsers(_, ctx: Context) {
		const associatedUserIds = await db.query(sql`
				SELECT user_id FROM priv.p_user_privilege WHERE cinema_operating_company_id = ${CinemaOperatingCompany.decodePublicId(
					this.id
				)}
			`);

		if (!associatedUserIds.rowCount) {
			return [];
		}

		return await Promise.all(
			associatedUserIds.rows.map((u) => User.gen(ctx.viewer, User.encodeDbId(u.user_id)))
		);
	}

	async allVoucherClassesOfCinemaOperatingCompany(_, { viewer }: { viewer: Viewer }) {
		if (!(await viewer.privileges()).accessRightCampaigns) {
			throw new ForbiddenError('You dont have access rights for this query');
		}

		const cinemaOperatingCompanyId = (await viewer.belongsToCinemaOperatingCompanies())[0];

		const allCinemaIds = await Promise.all(
			(
				await db.queryWithCache(sql`
				SELECT id
				FROM priv.p_cinema
				WHERE cinema_operating_company_id = ${CinemaOperatingCompany.decodePublicId(
					cinemaOperatingCompanyId
				)}
			`)
			).rows.map((r) => r.id)
		);

		const allVoucherIds = await Promise.all(
			(
				await db.queryWithCache(
					sql`
					SELECT id 
					FROM priv.p_voucher_types 
					WHERE cinema_id = ANY(${allCinemaIds})
				`
				)
			).rows.map((r) => r.id)
		);

		const allGeneratedVouchers = await Promise.all(
			allVoucherIds.map(
				async (vId) => await VoucherClass.gen(viewer, VoucherClass.encodeDbId(vId), viewer.language)
			)
		);

		return lodash.sortBy(allGeneratedVouchers, ['title']);
	}
}
