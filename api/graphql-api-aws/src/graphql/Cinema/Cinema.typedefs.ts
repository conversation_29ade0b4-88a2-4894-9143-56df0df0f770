import { gql } from 'apollo-server-core';

export const typeDef = gql`
	extend type Query {
		currentCinema: Cinema
		cinema(id: ID!): Cinema
		cinemas(ids: [ID!], bonusProgramId: ID): [Cinema!]!
	}
	extend type Mutation {
		updateCinema(cinema: UpdateCinemaInput!): updateCinemaReturnType!
		createCinema: createCinemaReturnType!
	}

	type Cinema {
		id: ID!
		bonusProgram: BonusProgram
		userCustomership(userId: ID): CinemaCustomership
		movies(futureOnly: Boolean): [Movie!]! @costFactor(value: 50)
		products(categories: [CinemaProductCategory!]): [CinemaProduct!]!
		name: String
		logo: String
		backgroundColor: String
		accentColor: String
		claim: String
		clusters: [String!]!
		hasBonusProgram: Boolean
		hasTrailerRating: Boolean @deprecated(reason: "[???]: trailerrating has been removed")
		headerImage: String
		reservationHotline: String
		imprint: String
		technologies: String
		history: String
		specialAboutUs: String
		currentInformation: String
		onlineReservationBaseUrl: String
		onlineTicketingBaseUrl: String
		ticketProvider: String
		onlineTicketingDisclaimer: String
		street: String
		houseNumber: String
		zipCode: String
		city: String
		parkingDescription: String
		locationDescription: String
		location: GeoCoordinates
		latitude: String @deprecated(reason: "[4.1.0]: use location instead")
		longitude: String @deprecated(reason: "[4.1.0]: use location instead")
		googleMapsLink: String
		appleMapsLink: String
		phone: String
		facebook: String
		twitter: String
		instagram: String
		tiktok: String
		youtube: String
		website: String
		email: String
		barrierFree: Boolean
		barrierFreeText: String
		hearingImpaired: Boolean
		hearingImpairedText: String
		blind: Boolean
		blindText: String
		pricesDisplay: [CinemaInformationParagraph!]!
		specialOffersDisplay: [CinemaInformationParagraph!]!
		giftVouchersDisplay: [CinemaInformationParagraph!]!
		sneaksDisplay: [CinemaInformationParagraph!]!
		openingHoursDisplay: [CinemaOpeningHours!]!
		additionalInfoSections: [CinemaInformationParagraph!]
		posTokens: [String!]!
		moviesWithLastChanceInfo(includeOutdated: Boolean!): [LastChanceItem!]!
		hasDynamicPricing: Boolean!
		brands: [Brand!]!
		accessRestrictedTo: AccessRestriction!
		infoLinks: [CinemaInfoLink!]
		currentScreeningAttributes: [ScreeningAttribute!]!
		hasMigratableCustomerCards: Boolean!
		externalCinemaId: String
		injectionScript: String
		beacons: [Beacon!]!
	}
	type CinemaInfoLink {
		name: String!
		url: String!
	}
	type LastChanceItem {
		movie: Movie!
		lastKnownScreening: Screening
		screeningsMarkedAsLastChance: [Screening!]!
	}
	type createCinemaReturnType {
		success: Boolean!
		cinema: Cinema
	}
	input UpdateCinemaInput {
		id: ID!
		name: String!
		logo: String
		claim: String
		hasBonusProgram: Boolean
		headerImage: String
		imprint: String
		technologies: String
		history: String
		specialAboutUs: String
		currentInformation: String
		onlineTicketingBaseUrl: String
		ticketProvider: String
		street: String
		houseNumber: String
		zipCode: String
		city: String
		parkingDescription: String
		locationDescription: String
		latitude: String
		longitude: String
		location: CinemaLocationInput
		googleMapsLink: String
		appleMapsLink: String
		phone: String
		facebook: String
		twitter: String
		instagram: String
		tiktok: String
		website: String
		email: String
		barrierFree: Boolean
		barrierFreeText: String
		hearingImpaired: Boolean
		hearingImpairedText: String
		blind: Boolean
		blindText: String
		openingHoursDisplay: [CinemaOpeningHoursInput!]!
		pricesDisplay: [CinemaInformationParagraphInput!]!
		specialOffersDisplay: [CinemaInformationParagraphInput!]!
		giftVouchersDisplay: [CinemaInformationParagraphInput!]!
		sneaksDisplay: [CinemaInformationParagraphInput!]!
		brands: [Brand!]!
		accessRestrictedTo: AccessRestriction!
	}
	enum Brand {
		CINURU
		CINFINITY
		CINEPLEX
		CINEWEB
		LUMOS
		ELSELICHTSPIELE
		DREHWERK
	}
	enum AccessRestriction {
		PRODUCTION
		STAGING
		DEVELOPMENT
	}
	input CinemaOpeningHoursInput {
		weekdays: String
		hours: String
	}
	input CinemaInformationParagraphInput {
		title: String
		image: String
		description: String
		price: String
	}
	input CinemaLocationInput {
		latitude: Float
		longitude: Float
	}

	type updateCinemaReturnType {
		cinema: Cinema
		success: Boolean!
	}

	type CinemaOpeningHours {
		weekdays: String
		hours: String
	}

	type CinemaInformationParagraph {
		title: String
		image: String
		description: String
		price: String
	}
`;
