/* eslint-disable no-var */
import {
	db,
	decodePublicId,
	encodeDbId,
	ForbiddenError,
	IncompleteArgumentsError,
	FeatureAccessLevel,
	sql,
} from '../../utils';
import { BonusProgram, Movie, User, Screening } from '..';
import { uniq } from 'lodash';
import {
	Viewer,
	PublicCinemaId,
	Language,
	InternalCinemaId,
	PublicUserId,
	I18N,
} from '../../typescript-types';

import { statusHierarchy } from '../../utils';
import { ScreeningAttribute, InternalScreeningAttributeId } from '../Screening/ScreeningAttribute';
import { CINETIXX_INJECTION_SCRIPT, COMPESO_INJECTION_SCRIPT } from './injectionScripts';
import { Beacon } from '../Beacon/Beacon';

if (!cinemaCache) {
	//We use var here to hoist the movie cache up, but only redeclare it once
	//eslint-disable-next-line no-var
	var cinemaCache: Map<PublicCinemaId, Cinema> = new Map();
	var lastCinemaCacheUpdate = 0;
	var refreshingCinemaCache = null;
}

const parseDbRowToCinema = (cin: Cinema): Cinema => {
	if (cin._languages) {
		cin._languages = ((cin._languages as unknown) as string)
			.replace(/\{|\}/gm, '')
			.split(',') as Language[];
	} else {
		cin._languages = [];
	}

	//remove unessacary images
	const filterImages = (path) =>
		(path || []).map((p) => ({
			...p,
			image:
				p.image && p.image !== 'null' && p.image !== '' && p.image !== 'undefined' ? p.image : null,
		}));

	cin.pricesDisplay = filterImages(cin.pricesDisplay);
	cin.specialOffersDisplay = filterImages(cin.specialOffersDisplay);
	cin.sneaksDisplay = filterImages(cin.sneaksDisplay);
	cin.giftVouchersDisplay = filterImages(cin.giftVouchersDisplay);

	cin.location =
		cin.latitude && cin.longitude
			? {
					latitude: cin.latitude,
					longitude: cin.longitude,
			  }
			: null;

	cin.logo = cin.logo || cin.bonusLogo;
	cin.backgroundColor = cin.backgroundColor || cin.bonusBackgroundColor;
	cin.accentColor = cin.accentColor || cin.bonusAccentColor;
	cin.openingHoursDisplay = cin.openingHoursDisplay || [];
	cin._cachedAt = new Date();
	const cinema = new Cinema({ ...cin, id: Cinema.encodeDbId(cin.id) });

	return cinema;
};

export class Cinema {
	id: PublicCinemaId;
	name: string;
	city: string | null;
	hasBonusProgram?: boolean;
	onlineTicketingBaseUrl?: string;
	ticketProvider?:
		| 'COMPESO'
		| 'MARS'
		| 'KINOHELD'
		| 'TICKET_INTERNATIONAL'
		| 'CINETIXX'
		| 'CLOUDTICKET';
	accessRestrictedTo: FeatureAccessLevel;
	cinemaNameGender?: string;
	street?: string;
	zipCode?: string;
	houseNumber?: string;
	_custom_online_ticketing_disclaimer_text?: string;
	_bonus_currency_id?: number;
	_cachedAt?: Date;
	_languages: Language[];
	_cpSlug?: string;
	_links?: { label: string; url: string; section: string }[];
	_screening_attributes: InternalScreeningAttributeId[] | null;
	location?: { latitude: string; longitude: string };
	latitude?: string;
	longitude?: string;
	logo?: string;
	bonusLogo?: string;
	backgroundColor?: string;
	bonusBackgroundColor?: string;
	accentColor?: string;
	bonusAccentColor?: string;
	openingHoursDisplay?: { weekdays: string; hours: string }[];
	pricesDisplay?: { title: string; price: string; description: string; image: string }[];
	specialOffersDisplay?: { title: string; price: string; description: string; image: string }[];
	sneaksDisplay?: { title: string; price: string; description: string; image: string }[];
	giftVouchersDisplay?: { title: string; price: string; description: string; image: string }[];
	_additionalInfoSections: { title: string; description: string }[];
	claim?: string;
	headerImage?: string;
	imprint?: string;
	technologies?: string;
	history?: string;
	specialAboutUs?: string;
	currentInformation?: string;
	parkingDescription?: string;
	locationDescription?: string;
	googleMapsLink?: string;
	appleMapsLink?: string;
	phone?: string;
	facebook?: string;
	twitter?: string;
	website?: string;
	email?: string;
	instagram?: string;
	tiktok?: string;
	barrierFree?: boolean;
	barrierFreeText?: string;
	hearingImpaired?: boolean;
	hearingImpairedText?: string;
	blind?: boolean;
	blindText?: string;
	hasTrailerRating?: boolean;
	reservationHotline?: string;
	onlineReservationBaseUrl?: string;
	youtube?: string;
	_trailer_autoplay?: boolean | null;
	externalCinemaId?: string;

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	constructor(data: any) {
		Object.assign(this, data);
	}
	static encodeDbId(dbId: InternalCinemaId): PublicCinemaId {
		return encodeDbId('Cinema', dbId as number);
	}
	static decodePublicId(id: PublicCinemaId): InternalCinemaId {
		return decodePublicId('Cinema', id as string);
	}

	static async refreshCache(force = false) {
		if (force || Date.now() - lastCinemaCacheUpdate > 5 * 60 * 1000) {
			if (!refreshingCinemaCache || force) {
				refreshingCinemaCache = (async () => {
					if (force) {
						await db.query(sql`REFRESH MATERIALIZED VIEW pub.cinema_info;`);
					}
					const res = await db.queryWithCache(sql`SELECT * FROM pub.cinema_info`, {
						ttlMinutes: force ? 0 : 5,
					}); // if force is passed in we want to query directly from the database instead of the redis cache
					res.rows.forEach((r) => {
						const cinema = parseDbRowToCinema(r);
						cinemaCache.set(cinema.id, cinema);
					});
					lastCinemaCacheUpdate = Date.now();
				})();
			}
			try {
				await refreshingCinemaCache;
				refreshingCinemaCache = null;
			} catch (e) {
				refreshingCinemaCache = null;
				console.log('Error refreshing cinema cache', e);
				throw e;
			}
		}
	}
	static async gen(viewer: Viewer, id: PublicCinemaId) {
		try {
			await this.refreshCache();
		} catch (e) {
			//retry once
			await this.refreshCache();
		}
		if (!cinemaCache.has(id)) {
			await this.refreshCache(true);
		}
		return cinemaCache.get(id);
	}
	static async allCinemas(viewer: Viewer, ids: PublicCinemaId[], language: Language) {
		const dbIds = ids ? ids.map((id) => Cinema.decodePublicId(id)) : undefined;
		const res = await db.queryWithCache(sql`
		SELECT 
			c.id 
		FROM 
			priv.p_cinema c
		LEFT JOIN 
			priv.p_cinema_app_brand brand ON brand.cinema_id = c.id
		WHERE
			(
				${language} = 'de' 
				OR 
				language = ${language}
				)
			AND (
				c.id = ANY (${dbIds}) 
				OR 
				${dbIds === undefined} IS TRUE
				)
		ORDER BY 
			(address).city 
		`);

		if (!res.rows.length) {
			return [];
		}

		const uniqueIds: InternalCinemaId[] = res.rows
			.map((r) => Cinema.encodeDbId(r.id))
			.filter((id, idx, arr) => arr.indexOf(id) === idx);

		const cinemas = await Promise.all(uniqueIds.map((id) => Cinema.gen(viewer, id)));

		let testingStatus: FeatureAccessLevel = FeatureAccessLevel.PRODUCTION;
		if (viewer.hasUserId) {
			const user = await User.gen(viewer, viewer.userId);
			testingStatus = user.testingStatus;
		}

		return cinemas.filter((cinema) => {
			return cinema && statusHierarchy[cinema.accessRestrictedTo] <= statusHierarchy[testingStatus];
		});
	}

	infoLinks() {
		if (!this._links) {
			return null;
		}
		return this._links.map(({ label, url }) => ({ name: label, url }));
	}
	additionalInfoSections() {
		if (!this._additionalInfoSections) {
			return null;
		}
		return this._additionalInfoSections;
	}

	async onlineTicketingDisclaimer(_: unknown, { i18n }: { i18n: I18N }) {
		if (this._custom_online_ticketing_disclaimer_text) {
			return this._custom_online_ticketing_disclaimer_text;

			return this._custom_online_ticketing_disclaimer_text;
		}
		if (!this.hasBonusProgram) {
			return i18n.t('BookingView.onlineTicketingDisclaimerDialog.description');
		}

		return i18n.t('BookingView.onlineTicketingDisclaimerDialog.description', {
			context: !this.onlineTicketingBaseUrl ? 'noOnlineTicketing' : 'cannotCollectPoints',
		});
	}
	async bonusProgram(_: unknown, { viewer }: { viewer: Viewer }) {
		return this._bonus_currency_id && this.hasBonusProgram
			? await BonusProgram.gen(viewer, BonusProgram.encodeDbId(this._bonus_currency_id))
			: null;
	}

	async clusters() {
		const res = await db.query<{ name: string }>(sql`
			SELECT 
				cc.name
			FROM 
				priv.p_cinema_cluster_cinema
			JOIN 
				priv.p_cinema_cluster cc ON cc.id = cinema_cluster_id
			WHERE 
				cinema_id = ${Cinema.decodePublicId(this.id)}
		`);

		return res.rows.map((r) => r.name);
	}

	async movies({ futureOnly = true }: { futureOnly: boolean }, { viewer }: { viewer: Viewer }) {
		let query;
		if (futureOnly) {
			query = `
				SELECT 
					m.id
				FROM 
					pub.movies m
				JOIN 
					(
						SELECT 
							movie_id, 
							min(datetime) as next_show 
						FROM 
							pub.showtimes
						WHERE 
							cinema_id = $1 
							AND 
							datetime > (now() - interval '1 hour')
						GROUP BY 
							movie_id
						) as s ON s.movie_id= m.id 
				ORDER BY 
					next_show ;
`;
		} else {
			query = `
			SELECT 
				DISTINCT movie_id as id
			FROM 
				pub.showtimes 
			WHERE 
				cinema_id = $1
			`;
		}
		const res = await db.query(query, [Cinema.decodePublicId(this.id)]);
		return await Movie.genMult(
			viewer,
			res.rows.map((r) => Movie.encodeDbId(r.id))
		);
	}
	async products({ categories }: { categories: ['TICKET'] }) {
		let query = `
		SELECT 
			id,
			name,
			bonus_points as "bonusPoints",
			category,
			subtext
		FROM 
			priv.p_cinema_product 
		WHERE 
			cinema_id = $1
		`;
		const sqlArgs = [Cinema.decodePublicId(this.id)];
		if (categories) {
			query += ' AND category = ANY ($2);';
			sqlArgs.push(categories);
		}
		return (await db.query(query, sqlArgs)).rows;
	}

	async posTokens(_: unknown, { viewer }: { viewer: Viewer }) {
		if (!(await viewer.privileges()).accessRightBonusProgram) {
			throw new ForbiddenError('You dont have access rights for this query');
		}
		const authorizedCinemas = await viewer.adminForCinemas();
		let query = '';

		if (viewer.isRoot) {
			query = 'SELECT auth_token FROM priv.p_pos_device WHERE cinema_id = $1 AND active = true';
		} else if (authorizedCinemas.includes(this.id) && this.hasBonusProgram) {
			query =
				'SELECT auth_token FROM priv.p_pos_device WHERE cinema_id = $1 AND active = true AND is_support = false';
		} else {
			throw new ForbiddenError('You do not have access rights for the posToken of this cinema');
		}

		const res = await db.query(query, [Cinema.decodePublicId(this.id)]);
		return res.rows.map((r) => r.auth_token);
	}

	async userCustomership(
		{ userId }: { userId: PublicUserId },
		{ viewer, language }: { viewer: Viewer; language: Language }
	) {
		// if no userId is provided it is inferred from the viewer
		userId = userId || (viewer.hasUserId && viewer.userId);
		if (!userId) {
			throw new IncompleteArgumentsError('Must provide userId or valid authentication');
		} else {
			const user = await User.gen(viewer, userId);
			const cinemaCustomerships = await user.cinemaCustomerships(undefined, { viewer, language });
			return cinemaCustomerships.find(({ cinema }) => cinema.id === this.id) || null;
		}
	}

	async moviesWithLastChanceInfo(
		{ includeOutdated }: { includeOutdated: boolean },
		{ viewer }: { viewer: Viewer }
	) {
		const cinemaIdDb = Cinema.decodePublicId(this.id);
		const res = await db.query(sql`
			WITH 
				last_known_showtimes as (
					SELECT DISTINCT ON(movie_id) 
						id, 
						movie_id 
					FROM 
						priv.p_showtime 
					WHERE 
						cinema_id = ${cinemaIdDb}
						AND 
						(${includeOutdated}=true OR datetime > now()) 
					ORDER BY 
						movie_id, 
						datetime DESC),
			 
				marked_as_last_chance as (
					SELECT 
						s.id, 
						s.movie_id
					FROM 
						priv.p_showtime s
					WHERE 
						last_chance=true 
						AND 
						cinema_id = ${cinemaIdDb} 
						AND 
						(${includeOutdated} = true OR datetime > now()) 
			)

		SELECT 
			s.movie_id, 
			s.id as last_known_screening_id, 
			ls.id as "marked_screening_id"
		FROM 
			last_known_showtimes s
		FULL OUTER JOIN 
			marked_as_last_chance ls  ON s.movie_id = ls.movie_id
		`);
		// Generate all needed screenings in batch to prevent 1000's of db roundtrips
		const neededScreeningIdsDb = uniq([
			...res.rows.map((r) => r.marked_screening_id).filter((s) => s !== null),
			...res.rows.map((r) => r.last_known_screening_id).filter((s) => s !== null),
		]);

		const allScreenings = await Screening.genMult(
			viewer,
			neededScreeningIdsDb.map((id) => Screening.encodeDbId(id))
		);
		const screeningLookup = {};
		allScreenings.forEach((screening) => {
			screeningLookup[(Screening.decodePublicId(screening.id) as unknown) as number] = screening;
		});

		const finalResults = [];
		let previousIterationMovieId;
		const movieIds = uniq(res.rows.map((r) => Movie.encodeDbId(r.movie_id)));
		const movies: { [keys: number]: Movie } = {};
		(await Movie.genMult(viewer, movieIds)).forEach((movie) => {
			movies[<number>Movie.decodePublicId(movie.id)] = movie;
		});
		for (let i = 0; i < res.rows.length; i++) {
			const data = res.rows[i];
			if (data.movie_id !== previousIterationMovieId) {
				finalResults.push({
					movie: movies[data.movie_id],
					lastKnownScreening: screeningLookup[data.last_known_screening_id],
					screeningsMarkedAsLastChance: data.marked_screening_id
						? [screeningLookup[data.marked_screening_id]]
						: [],
				});
			} else {
				//Movie entry already existing, just add this row to the markedScreenings
				if (data.marked_screening_id) {
					finalResults[finalResults.length - 1].screeningsMarkedAsLastChance.push(
						screeningLookup[data.marked_screening_id]
					);
				}
			}
			previousIterationMovieId = data.movie_id;
		}
		return finalResults;
	}

	async hasDynamicPricing() {
		const res = await db.query(sql`
		SELECT 
			b2b.pricing_scheme
		FROM 
			priv.p_cinema cin
		JOIN 
			priv.p_cinema_operating_company b2b ON cin.cinema_operating_company_id = b2b.id
		WHERE 
			cin.id = ${Cinema.decodePublicId(this.id)}
		`);
		const hasDynamicPricing =
			res.rows[0] && res.rows[0].pricing_scheme && res.rows[0].pricing_scheme === 'Dynamic'
				? true
				: false;
		return hasDynamicPricing;
	}
	brands = () => {
		return this._languages
			.map((el) => el.split('-'))
			.filter((el) => el.length === 2)
			.map((el) => el[1].toUpperCase())
			.filter((el, idx, arr) => {
				return arr.indexOf(el) === idx; // Filter out duplicates (e.g. de-cinuru and en-cinuru)
			});
	};
	async currentScreeningAttributes(_: unknown, { i18n, viewer }: { i18n: I18N; viewer: Viewer }) {
		if (!this._screening_attributes) {
			return [];
		}
		const atts = await Promise.all(
			this._screening_attributes.map(
				async (id) =>
					await ScreeningAttribute.gen(ScreeningAttribute.encodeDbId(id), { i18n, viewer })
			)
		);
		return atts;
	}

	async beacons() {
		return await Beacon.genByCinema(this.id);
	}

	injectionScript = () => {
		return this.ticketProvider === 'COMPESO'
			? COMPESO_INJECTION_SCRIPT
			: this.ticketProvider === 'CINETIXX'
			? CINETIXX_INJECTION_SCRIPT
			: null;
	};
}
