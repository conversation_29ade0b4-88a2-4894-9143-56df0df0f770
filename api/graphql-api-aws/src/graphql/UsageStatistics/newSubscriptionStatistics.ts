import {
	addDays,
	addMonths,
	addWeeks,
	endOfDay,
	endOfMonth,
	startOfMonth,
	startOfWeek,
	add,
	getDaysInMonth,
} from 'date-fns';
import { Context } from '../../typescript-types';
import { db, sql } from '../../utils';
import { ensureIsAdmin } from '../../utils/auth';
import { Cinema } from '../Cinema/Cinema';
import { once } from 'lodash';

type NewSubscriptionStatisticsArgs = {
	cinemaIds: string[];
};

enum AverageDataType {
	MOVIE = 'MOVIE',
	SUBSCRIPTION = 'SUBSCRIPTION',
	TICKET = 'TICKET',
}

enum TimePeriod {
	DAY = 'DAY',
	WEEK = 'WEEK',
	MONTH = 'MONTH',
}

type AverageDataArgs = {
	dataType: AverageDataType;
	since: Date;
	groupBy: TimePeriod;
};

const DEFAULT_MONTHS_LOOKBACK = 3;
const DEFAULT_DAYS_LOOKBACK = 9;
const DEFAULT_WEEKS_LOOKBACK = 7;

const roundToMidday = (date: Date) => {
	const ret = new Date(date);
	ret.setHours(12);

	return ret;
};

export const newSubscriptionStatistics = (
	_,
	{ cinemaIds }: NewSubscriptionStatisticsArgs,
	ctx: Context
) => {
	const cinemaDbIds = cinemaIds?.map((i) => Cinema.decodePublicId(i)) || [];
	ensureIsAdmin(ctx.viewer);

	return {
		numberOfUsers: async (): Promise<number> => {
			const result = await db.queryWithCache(sql`
				SELECT count(id)
				FROM analytics.valid_users
				wHERE (${!cinemaDbIds.length} OR id IN (SELECT DISTINCT user_id FROM priv.p_user_cinema WHERE cinema_id = ANY(${cinemaDbIds})))
			`);
			return result.rows[0]?.count || 0;
		},
		numberOfActiveSubscriptions: async (): Promise<number> => {
			const result = await db.queryWithCache(sql`
				SELECT count(id)
				FROM priv.p_subscription 
				WHERE payed_until >= NOW() AND (cancellation_effective_at IS NULL OR cancellation_effective_at > NOW())
					AND (${!cinemaDbIds.length} OR user_id IN (SELECT DISTINCT user_id FROM priv.p_user_cinema WHERE cinema_id = ANY(${cinemaDbIds})))
			`);
			return result.rows[0]?.count || 0;
		},
		averageData: async ({ dataType, groupBy }: AverageDataArgs) => {
			const now = endOfDay(new Date());

			const maximumDate = groupBy === TimePeriod.MONTH ? endOfMonth(addMonths(now, -1)) : now;

			const minimumDate = roundToMidday(
				groupBy === TimePeriod.DAY
					? addDays(now, -DEFAULT_DAYS_LOOKBACK)
					: groupBy === TimePeriod.WEEK
					? startOfWeek(addWeeks(now, -DEFAULT_WEEKS_LOOKBACK), { weekStartsOn: 4 })
					: startOfMonth(addMonths(now, -DEFAULT_MONTHS_LOOKBACK))
			);

			const normalizationScale = (date: Date) => {
				const daysInMonth = getDaysInMonth(date);

				return groupBy === TimePeriod.DAY
					? 30.4
					: groupBy === TimePeriod.WEEK
					? 30.4 / 7
					: 30.4 / daysInMonth;
			};

			const allTicketsQuery = once(async () =>
				(
					await db.queryWithCache<{ id: number; bought_at: Date }>(sql`
							SELECT id,bought_at
							FROM priv.p_ticket
							WHERE (${!cinemaDbIds.length} OR cinema_id = ANY(${cinemaDbIds}))
							AND status != 'REFUNDED'
							AND is_subscription_ticket IS TRUE
							AND bought_at >= ${minimumDate} AND bought_at < ${maximumDate}
					`)
				).rows.map((r) => ({ id: r.id, boughtAt: r.bought_at }))
			);

			const dateSeriesQuery = once(
				async () =>
					(
						await db.query<{ date: Date }>(
							`
							SELECT DATE_TRUNC('DAY', generate_series($1, $2, INTERVAL '1 ${groupBy}') at time zone 'UTC') at time zone 'UTC' as "date"
				`,
							[minimumDate, maximumDate]
						)
					).rows
			);

			const activeSubscriptionsQuery = once(async () =>
				(
					await db.queryWithCache<{ id: number; valid_from: Date; payed_until: Date }>(sql`
				
							SELECT id,valid_from, payed_until
							FROM priv.p_subscription
							WHERE valid_from < ${maximumDate}
								AND payed_until > ${minimumDate}
								AND (
											cancellation_effective_at IS NULL OR cancellation_effective_at > ${minimumDate}
								)
								AND (
									${!cinemaDbIds.length} OR user_id IN (
										SELECT DISTINCT user_id 
										FROM priv.p_ticket 
										WHERE cinema_id = ANY(${cinemaDbIds})
									)
								)
				`)
				).rows.map((r) => ({ id: r.id, validFrom: r.valid_from, payedUntil: r.payed_until }))
			);

			const visitsQuery = once(
				async () =>
					(
						await db.queryWithCache<{ date: Date }>(sql`
							SELECT DATE_TRUNC('DAY', bought_at) as "date"
							FROM priv.p_ticket
							WHERE (${!cinemaDbIds.length} OR cinema_id = ANY(${cinemaDbIds}))
							AND is_subscription_ticket IS TRUE
							AND movie_id IS NOT NULL 
							AND bought_at >= ${minimumDate} 
							AND bought_at < ${maximumDate}
							AND status != 'REFUNDED'
				`)
					).rows
			);

			const addInterval = (date: Date) => add(date, { [`${groupBy.toLowerCase()}s`]: 1 });

			switch (dataType) {
				case AverageDataType.TICKET: {
					const dateSeries = await dateSeriesQuery();
					const activeSubscriptions = await activeSubscriptionsQuery();
					const tickets = await allTicketsQuery();

					return dateSeries.map(({ date }) => {
						const ticketsCount = tickets.filter(
							(t) => t.boughtAt < addInterval(date) && t.boughtAt >= date
						).length;
						const subsCount = activeSubscriptions.filter(
							(s) => s.validFrom < addInterval(date) && s.payedUntil >= date
						).length;

						return {
							date,
							count: (ticketsCount / (subsCount || 1)) * normalizationScale(date),
						};
					});
				}
				case AverageDataType.MOVIE: {
					const dateSeries = await dateSeriesQuery();
					const visits = await visitsQuery();

					return dateSeries.map(({ date }) => {
						return {
							date,
							count: visits.filter((v) => v.date < addInterval(date) && v.date >= date).length,
						};
					});
				}
				case AverageDataType.SUBSCRIPTION: {
					const subscriptions = await activeSubscriptionsQuery();
					const dateSeries = await dateSeriesQuery();

					return dateSeries.map(({ date }) => ({
						date,
						count: subscriptions.filter((s) => s.validFrom <= date && s.payedUntil > date).length,
						newCount: subscriptions.filter(
							(s) => s.validFrom >= date && s.validFrom < addInterval(date)
						).length,
					}));
				}
				default:
					throw new Error('unreachable');
			}
		},
	};
};
