import { gql } from 'apollo-server-core';

export const typeDef = gql`
	extend type Query {
		usageStatistics(cinemaIds: [ID!]): UsageStatistics
		movieStatistics: [MovieStatistics!]!
		subscriptionsStatistics(from: Date!, to: Date!): SubscriptionStatisticsResponse
		newSubscriptionStatistics(cinemaIds: [ID!]): NewSubscriptionStatisticsResponse!
		payoutInfo(from: Date!, to: Date!): [CinemaPayoutInfo!]!
		missedPayouts(filter: PayoutMissedFilter!): [MissedPayoutInfo!]!
	}

	type MissedPayoutInfo {
		user: User
		subscription: Subscription!
	}

	input PayoutMissedFilter {
		from: Date
		to: Date
	}

	type SubscriptionStatisticsResponse {
		numberOfUsers: Int!
		numberOfActiveSubscriptions: Int!
		numberOfSubscriptionTickets: Int!
		numberOfNewSubscriptions: Int!
		canceledSubscriptions: Int!
		numberOfUsersPerTier: [TierStatistics!]!
		visitFrequency: VisitFrequency
		seenMovies: [SeenMovies!]!
	}

	type AveragePerDateStatistic {
		date: Date!
		count: Float!
		newCount: Float
	}

	enum StatisticDataType {
		MOVIE
		SUBSCRIPTION
		TICKET
	}

	enum StatisticGroupBy {
		DAY
		WEEK
		MONTH
	}

	type NewSubscriptionStatisticsResponse {
		numberOfUsers: Int!
		numberOfActiveSubscriptions: Int!
		averageData(
			dataType: StatisticDataType!
			groupBy: StatisticGroupBy!
		): [AveragePerDateStatistic!]!
	}

	type TierStatistics {
		tierName: String!
		numActiveSubscribers: Int!
	}

	type VisitFrequency {
		zero: Int!
		one: Int!
		two: Int!
		three: Int!
		four: Int!
		five: Int!
		sixToTen: Int!
		elevenToTwenty: Int!
		twentyPlus: Int!
	}

	type SeenMovies {
		movie: Movie!
		numVisits: Int!
	}
	type UsageStatistics {
		id: ID!
		sentMessagesInInterval(from: Date, to: Date): SentMessages
		installationsInInterval(from: Date, to: Date): Int
		watchlistUsedInInterval(from: Date, to: Date): Int
		activeUsersInInterval(from: Date, to: Date): Int
	}

	type CinemaPayoutInfo {
		cinema: Cinema!
		numOfSubscriptionTickets: Int!
		numAdditionalTickets: Int!
	}

	type MovieStatistics {
		id: ID!
		movie: Movie!
		numWatchlistings: Int!
		numInteractions: Int!
	}

	type SentMessages {
		numberOfCountedMessages: Int
		numberOfFreeMessages: Int
	}
`;
