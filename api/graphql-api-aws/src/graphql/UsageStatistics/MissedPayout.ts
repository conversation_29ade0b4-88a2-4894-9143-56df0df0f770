import { ca } from 'date-fns/locale';
import { Context } from '../../typescript-types';
import { db, sql } from '../../utils';
import { ensureIsAdmin } from '../../utils/auth';
import { Subscription } from '../Subscription/Subscription';
import { User } from '../User/User';

type MissedPayout = {
	userId: string;
	paidUntil: Date;
	subscriptionId: string;
};

export async function missedPayouts(_, { filter: { from, to } }, ctx: Context) {
	ensureIsAdmin(ctx.viewer);

	//We join the user to know if the user still exists.
	const { rows } = await db.query<MissedPayout>(
		sql`SELECT s.id as "subscriptionId", u.id as "userId"
            FROM priv.p_subscription s 
						LEFT JOIN priv.p_user u ON s.user_id = u.id
            WHERE blocked_reason = 'PAYMENT_FAILED'
								AND payed_until::date BETWEEN ${from} AND ${to}
                                           ORDER BY payed_until DESC
        `
	);

	return rows.map((r) => ({
		user() {
			if (!r.userId) {
				return null;
			}
			return User.gen(ctx.viewer, User.encodeDbId(r.userId));
		},
		subscription() {
			return Subscription.gen(Subscription.encodeDbId(r.subscriptionId));
		},
	}));
}
