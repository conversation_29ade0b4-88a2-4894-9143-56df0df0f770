import { gql } from 'apollo-server-core';

export const typeDef = gql`
	extend type Query {
		subscriptionTiers(ids: [ID]): [SubscriptionTier!]
	}
	type SubscriptionTier {
		id: ID!
		name: String!
		description: String!
		details: String
		paypalBillingPlanId: String!
		billingCycleConfigs: [PaypalBillingCycleConfig!]!
		isAutoRenewal: Boolean!
	}

	type PaypalBillingCycleConfig {
		sequence: Int!
		total_cycles: Int!
		frequency: PaypalFrequency!
		tenure_type: PaypalTenureType!
		pricing_scheme: PaypalPricingScheme!
	}

	type PaypalFrequency {
		interval_unit: String!
		interval_count: Int!
	}

	enum PaypalTenureType {
		TRIAL
		REGULAR
	}

	type PaypalPricingScheme {
		fixed_price: PaypalFixedPrice!
	}

	type PaypalFixedPrice {
		value: String!
		currency_code: String!
	}
`;
