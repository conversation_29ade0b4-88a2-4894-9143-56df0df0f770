import { difference } from 'lodash';
import {
	Context,
	InternalCinemaClusterId,
	PublicCinemaClusterId,
	Viewer,
} from '../../typescript-types';
import { db, decodePublicId, encodeDbId, sql } from '../../utils';
import { ensureIsAdmin, ensureIsLoggedInAndNotAnonymous } from '../../utils/auth';
import { Cinema } from '../Cinema/Cinema';

export type CinemaClusterInput = {
	name: string;
	position: number;
	cinemaIds: string[];
};

export class CinemaCluster {
	id: PublicCinemaClusterId;
	_dbId: InternalCinemaClusterId;
	name: string;
	position: number;

	constructor(data: CinemaCluster) {
		Object.assign(this, { ...data, id: CinemaCluster.encodeDbId(data.id), _dbId: data.id });
	}

	static encodeDbId(dbId: InternalCinemaClusterId): PublicCinemaClusterId {
		return encodeDbId('CinemaCluster', dbId as number);
	}

	static decodePublicId(id: PublicCinemaClusterId): InternalCinemaClusterId {
		return decodePublicId('CinemaCluster', id as string);
	}

	static async gen(viewer: Viewer, id: PublicCinemaClusterId) {
		return (await this.genMult(viewer, [id]))[0];
	}

	static async genMult(viewer: Viewer, ids?: PublicCinemaClusterId[]): Promise<CinemaCluster[]> {
		ensureIsLoggedInAndNotAnonymous(viewer);

		if (ids && !ids.length) {
			return [];
		}

		const dbIds = ids?.map((id) => this.decodePublicId(id));
		const cinemaCluster = await db.query(sql`
            SELECT id,name,position 
            FROM priv.p_cinema_cluster 
            WHERE ${!dbIds} OR id = ANY(${dbIds}) 
            ORDER BY position ASC
        `);

		return cinemaCluster.rows.map((row) => new CinemaCluster(row));
	}

	static async createCinemaCluster(
		data: CinemaClusterInput,
		viewer: Viewer
	): Promise<CinemaCluster> {
		ensureIsAdmin(viewer);

		const clusterId = await db.withTransaction(async (client) => {
			const res = await client.query(sql`
            INSERT INTO priv.p_cinema_cluster (name, position) VALUES (${data.name}, ${data.position}) RETURNING id
        `);

			const cinemaClusterId = res.rows[0].id;

			const dbInternalCinemaIds = data.cinemaIds.map((c) => Cinema.decodePublicId(c));

			if (dbInternalCinemaIds.length) {
				await client.query(
					`
                    INSERT INTO priv.p_cinema_cluster_cinema (cinema_cluster_id, cinema_id) VALUES 
                    ${dbInternalCinemaIds.map((_, index) => `($1, $${index + 2})`).join(',\n')}
                    `,
					[cinemaClusterId, ...dbInternalCinemaIds]
				);
			}

			return res.rows[0].id;
		});

		return await this.gen(viewer, CinemaCluster.encodeDbId(clusterId));
	}

	static async updateCinemaCluster(
		publicClusterId: PublicCinemaClusterId,
		data: CinemaClusterInput,
		viewer: Viewer
	): Promise<CinemaCluster> {
		ensureIsAdmin(viewer);

		const internalClusterId = this.decodePublicId(publicClusterId);

		await db.withTransaction(async (client) => {
			await client.query(sql`
                UPDATE priv.p_cinema_cluster SET name = ${data.name}, position = ${data.position} WHERE id = ${internalClusterId}
            `);

			const currentCinemaIds = (
				await client.query(sql`
                SELECT cinema_id FROM priv.p_cinema_cluster_cinema WHERE cinema_cluster_id = ${internalClusterId}
            `)
			).rows.map((r) => r.cinema_id);

			const newInternalCinemaIds = data.cinemaIds.map((id) => Cinema.decodePublicId(id));

			const addedCinemas = difference(newInternalCinemaIds, currentCinemaIds);
			const removedCinemas = difference(currentCinemaIds, newInternalCinemaIds);

			if (addedCinemas.length) {
				//todo use insertmultiplerows that hasn't entered yet
				await client.query(
					`
                    INSERT INTO priv.p_cinema_cluster_cinema (cinema_cluster_id, cinema_id) VALUES 
                    ${addedCinemas.map((_, index) => `($1, $${index + 2})`).join(',\n')}
                    `,
					[internalClusterId, ...addedCinemas]
				);
			}

			if (removedCinemas.length) {
				await client.query(
					sql`DELETE FROM priv.p_cinema_cluster_cinema WHERE cinema_cluster_id = ${internalClusterId} AND cinema_id = ANY(${removedCinemas})`
				);
			}
		});

		return await this.gen(viewer, publicClusterId);
	}

	static async deleteCinemaClusters(viewer: Viewer, ids: PublicCinemaClusterId[]): Promise<number> {
		ensureIsAdmin(viewer);

		if (!ids.length) {
			return 0;
		}

		try {
			return await db.withTransaction(async (client) => {
				const { rowCount } = await client.query(sql`
                DELETE FROM priv.p_cinema_cluster WHERE id = ANY(${ids.map((id) =>
									this.decodePublicId(id)
								)})
            `);

				if (rowCount === 0) {
					throw new Error('Cinema cluster not found');
				}

				await client.query(sql`
                    DELETE FROM priv.p_cinema_cluster_cinema WHERE cinema_cluster_id = ANY(${ids.map(
											(id) => this.decodePublicId(id)
										)})

    `);
				return rowCount;
			});

			//eslint-disable-next-line no-catch-all/no-catch-all
		} catch {
			return 0;
		}
	}

	async cinemas(_, ctx: Context) {
		const res = await db.query<{ cinema_id: number }>(sql`
            SELECT cinema_id FROM priv.p_cinema_cluster_cinema WHERE cinema_cluster_id = ${this._dbId}
        `);

		return res.rows?.map((r) => Cinema.gen(ctx.viewer, Cinema.encodeDbId(r.cinema_id))) || [];
	}
}
