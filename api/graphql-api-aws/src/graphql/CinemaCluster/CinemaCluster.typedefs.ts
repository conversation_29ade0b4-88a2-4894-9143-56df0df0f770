import { gql } from 'apollo-server-core';

export const typeDef = gql`
	extend type Mutation {
		updateCinemaCluster(id: ID!, data: CinemaClusterData!): CinemaCluster!

		createCinemaCluster(data: CinemaClusterData!): CinemaCluster!

		deleteCinemaClusters(ids: [ID!]!): Int!
	}

	extend type Query {
		cinemaCluster(id: ID!): CinemaCluster!

		cinemaClusters: [CinemaCluster!]!
	}

	type CinemaCluster {
		id: ID!
		name: String!
		position: Int!
		cinemas: [Cinema!]!
	}

	input CinemaClusterData {
		position: Int!
		name: String!
		cinemaIds: [ID!]!
	}
`;
