import { Context } from '../../typescript-types';
import { CinemaCluster, CinemaClusterInput } from './CinemaCluster';

export const cinemaCluster = (_, { id }: { id: string }, ctx: Context) =>
	CinemaCluster.gen(ctx.viewer, id);

export const cinemaClusters = (_, __, ctx: Context) => CinemaCluster.genMult(ctx.viewer);

export const createCinemaCluster = (_, { data }: { data: CinemaClusterInput }, ctx: Context) =>
	CinemaCluster.createCinemaCluster(data, ctx.viewer);

export const updateCinemaCluster = (
	_,
	{ id, data }: { id: string; data: CinemaClusterInput },
	ctx: Context
) => CinemaCluster.updateCinemaCluster(id, data, ctx.viewer);

export const deleteCinemaClusters = (_, { ids }: { ids: string[] }, ctx: Context) =>
	CinemaCluster.deleteCinemaClusters(ctx.viewer, ids);
