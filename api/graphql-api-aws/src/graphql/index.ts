export { AudioFingerprintingConfig } from './AudioFingerprintingConfig/AudioFingerprintingConfig';
export { CinemaOperatingCompany } from './CinemaOperatingCompany/CinemaOperatingCompany';
export { BonusCurrency } from './BonusProgram/BonusCurrency';
export { BonusProgram } from './BonusProgram/BonusProgram';
export { BonusProgramMembership } from './BonusProgramMembership/BonusProgramMembership';
export { BonusProgramStatistic } from './BonusProgramStatistic/BonusProgramStatistic';
export { Campaign } from './Campaign/Campaign';
export { TargetGroup } from './TargetGroup/TargetGroup';
export { CastOrCrewMember } from './CastOrCrewMember/CastOrCrewMember';
export { Cinema } from './Cinema/Cinema';
export { ErrorStatistics } from './ErrorStatistic/ErrorStatistics';
export { InterestRating } from './InterestRating/InterestRating';
export { Movie } from './Movie/Movie';
export { Image } from './Image/Image';

export { Trailer } from './Trailer/Trailer';
export { NewsItem } from './NewsItem/NewsItem';
export { Notification } from './Notification/Notification';
export { NotificationPreference } from './NotificationPreference/NotificationPreference';
export { ReviewRating } from './ReviewRating/ReviewRating';
export { QuizReviewRating } from './ReviewRating/QuizReviewRating';
export { SeenInCinema } from './SeenInCinema/SeenInCinema';
export { Screening } from './Screening/Screening';
export { Ticket } from './Ticket/Ticket';
export { MovieList } from './MovieList/MovieList';
export { SoonInCinemaItem } from './SoonInCinemaItem/SoonInCinemaItem';
export { UsageStatistics } from './UsageStatistics/UsageStatistics';
export { User } from './User/User';
export { UserGroupFilter } from './UserGroupFilter/UserGroupFilter';
export { UserGroupFilterOnReferencedMovie } from './UserGroupFilterOnReferencedMovie/UserGroupFilterOnReferencedMovie';
export { VoucherClass } from './VoucherClass/VoucherClass';
export { VoucherInstance } from './VoucherInstance/VoucherInstance';
export { Subscription } from './Subscription/Subscription';
export { BookingProcess } from './BookingProcess/BookingProcess';
import {
	AudioFingerprintingConfig,
	BonusProgram,
	Campaign,
	Cinema,
	ErrorStatistics,
	Movie,
	NewsItem,
	Screening,
	MovieList,
	UsageStatistics,
	User,
} from '.';
import { typeDef as OnboardingContentTypeDef } from './OnboardingContent/OnboardingContent.typedef';
import { onboardingContent } from './OnboardingContent/onboardingContent';
import { db, sql, ForbiddenError } from '../utils';
import { typeDef as AppTypeDefs } from './App/App.typedefs';
import { upsertApp } from './App/upsertApp';
import { typeDef as AppReleaseTypeDefs } from './AppRelease/AppRelease.typedefs';
import { appReleases } from './AppRelease/appReleases';
import { typeDef as AudioFingerprintingConfigTypeDefs } from './AudioFingerprintingConfig/AudioFingerprintingConfig.typedefs';
import { typeDef as CinemaOperatingCompanyTypeDefs } from './CinemaOperatingCompany/CinemaOperatingCompany.typedefs';
import { typeDef as BonusProgramTypeDefs } from './BonusProgram/BonusProgram.typedefs';
import { updateBonusProgram } from './BonusProgram/updateBonusProgram';
import { acceptBonusProgramTerms } from './BonusProgramMembership/acceptBonusProgramTerms';
import { typeDef as BonusProgramMembershipTypeDefs } from './BonusProgramMembership/BonusProgramMembership.typedefs';
import { bookCinemaTicketPoints } from './BonusProgramMembership/bookCinemaTicketPoints';
import { bookCoupon } from './BonusProgramMembership/bookCoupon';
import { joinBonusProgram } from './BonusProgramMembership/joinBonusProgram';
import { processScannedQrCode } from './BonusProgramMembership/processScannedQrCode';
import { claimOrDiscardProvisionalBonusPointBooking } from './BonusProgramMembership/claimOrDiscardProvisionalBonusPointBooking';
import { typeDef as BonusProgramStatisticTypeDefs } from './BonusProgramStatistic/BonusProgramStatistic.typedefs';
import { typeDef as CampaignTypeDefs } from './Campaign/Campaign.typedefs';
import { typeDef as CinemaClusterTypeDefs } from './CinemaCluster/CinemaCluster.typedefs';
import {
	createCinemaCluster,
	updateCinemaCluster,
	deleteCinemaClusters,
	cinemaCluster,
	cinemaClusters,
} from './CinemaCluster/queries';
import { createCampaign } from './Campaign/createCampaign';
import { deleteCampaigns } from './Campaign/deleteCampaigns';
import { updateCampaignsStatus } from './Campaign/updateCampaignsStatus';
import { editCampaign } from './Campaign/editCampaign';
import { estimateCampaignSize } from './Campaign/estimateCampaignSize';
import { setCampaignStatus } from './Campaign/setCampaignStatus';

import { TargetGroup } from './TargetGroup/TargetGroup';
import { editTargetGroup } from './TargetGroup/editTargetGroup';
import { deleteTargetGroups } from './TargetGroup/deleteTargetGroups';
import { createTargetGroup } from './TargetGroup/createTargetGroup';
import { updateTargetGroupsStatus } from './TargetGroup/updateTargetGroupsStatus';

import { typeDef as TargetGroupTypeDef } from './TargetGroup/TargetGroup.typedefs';

import { updateCinemaOperatingCompanyTargetGroupFilterConfig } from './CinemaOperatingCompany/updateCinemaOperatingCompanyTargetGroupFilterConfig';

import { allVoucherClassesOfCinemaOperatingCompany } from './VoucherClass/allVoucherClassesOfCinemaOperatingCompany';
import { getAvailableVoucherClasses } from './VoucherClass/getAvailableVoucherClasses';
import { allPerks } from './Perk/allPerks';
import { allAchievements } from './Achievement/allAchievements';
import { typeDef as PerkTypeDefs } from './Perk/Perk.typedefs';
import { typeDef as AchievementTypeDefs } from './Achievement/Achievement.typedefs';
import { allGenres } from './Genre/allGenres';
import { typeDef as GenreTypeDefs } from './Genre/Genre.typedefs';
import { searchCastOrCrewMember } from './CastOrCrewMember/searchCastOrCrewMember';
import { typeDef as CastOrCrewMemberTypeDefs } from './CastOrCrewMember/CastOrCrewMember.typedefs';
import { typeDef as CinemaTypeDefs } from './Cinema/Cinema.typedefs';
import { typeDef as CompanyTypedefs } from './Company/Company.typedefs';
import { cinemas } from './Cinema/cinemas';
import { createCinema } from './Cinema/createCinema';
import { currentCinema } from './Cinema/currentCinema';
import { updateCinema } from './Cinema/updateCinema';
import { typeDef as ContactFeedbackTypeDefs } from './Contact-Feedback/Contact-Feedback.typedefs';
import { submitContactForm, suggestCinema } from './Contact-Feedback/contactFormMutations';
import { createIntercomLead } from './Contact-Feedback/createIntercomLead';
import { submitAppFeedback } from './Contact-Feedback/submitAppFeedback';
import { typeDef as ErrorStatisticTypeDefs } from './ErrorStatistic/ErrorStatistic.typedefs';
import { geocode, reverseGeocode, locationForZipCode } from './Geocode/geocode';
import { searchMovie } from './Movie/searchMovie';
import { typeDef as GeocodeTypeDefs } from './Geocode/Geocode.typedefs';
import { typeDef as InterestRatingTypeDefs } from './InterestRating/InterestRating.typedefs';
import { upsertInterestRating } from './InterestRating/upsertInterestRating';
import { typeDef as ListOfMoviesTypeDefs } from './ListOfMovies/ListOfMovies.typedefs';
import { typeDef as LogItemTypeDefs } from './LogItem/LogItem.typedefs';
import { logItems } from './LogItem/logItems';
import { typeDef as MovieTypeDefs } from './Movie/Movie.typedefs';
import { typeDef as TrailerTypeDefs } from './Trailer/Trailer.typedefs';
import { screenedMovies } from './Movie/screenedMovies';
import { typeDef as NewsItemTypeDefs } from './NewsItem/NewsItem.typedefs';
import { upsertNewsItem } from './NewsItem/upsertNewsItem';
import { typeDef as NotificationTypeDefs } from './Notification/Notification.typedefs';
import { typeDef as NotificationPreferenceTypeDefs } from './NotificationPreference/NotificationPreference.typedefs';
import { typeDef as ExternalNewsletterPreferenceTypeDefs } from './ExternalNewsletterPreference/ExternalNewsletterPreference.typedef';
import { updateNotificationPreference } from './NotificationPreference/updateNotificationPreference';
import { updateExternalNewsletterPreference } from './ExternalNewsletterPreference/externalNewsletterPreference';
import { typeDef as BookingProcessTypeDefs } from './BookingProcess/BookingProcess.typedef';
import { startBookingProcess } from './BookingProcess/startBookingProcess';
import { selectSeats } from './BookingProcess/selectSeats';
import { submitBookingProcess } from './BookingProcess/submitBookingProcess';
import { selectSeatPricingCategories } from './BookingProcess/selectSeatPricingCategories';
import { cancelBookingProcess } from './BookingProcess/cancelBookingProcess';
import { selectSeatingAreaPricingCategories } from './BookingProcess/selectSeatingAreaPricingCategories';
import {
	externalUrl,
	appDeepLink,
	typeDef as ExternalShareLinkTypeDefs,
} from './ExternalShareLink/externalShareLink';
import { deleteReviewRating, deleteReviewRating_2 } from './ReviewRating/deleteReviewRating';
import { typeDef as ReviewRatingTypeDefs } from './ReviewRating/ReviewRating.typedefs';
import { upsertReviewRating } from './ReviewRating/upsertReviewRating';
import { upsertQuizReviewRating } from './ReviewRating/upsertQuizReviewRating';
import { deleteSeenInCinema } from './SeenInCinema/deleteSeenInCinema';
import { typeDef as SeenInCinemaTypeDefs } from './SeenInCinema/SeenInCinema.typedefs';
import { upsertSeenInCinema } from './SeenInCinema/upsertSeenInCinema';

import { createOnlineTicketingOutlinkId } from './Screening/createOnlineTicketingOutlinkId';
import { typeDef as ScreeningTypeDefs } from './Screening/Screening.typedefs';
import { typeDef as TicketTypeDefs } from './Ticket/Ticket.typedefs';
import { typeDef as OrderTypeDefs } from './Order/Order.typedefs';
import { typeDef as MovieListTypeDefs } from './MovieList/MovieList.typedefs';
import { typeDef as dismissExitPollTypeDefs } from './Misc/dismissExitPoll.typedefs';
import { typeDef as ServiceStatusTypeDefs } from './Misc/getServiceStatus.typedefs';
import { typeDef as SDKInputTypeDefs } from './SDKInput/SDKInput.typedef';
import { typeDef as SubscribeToNewsletterTypeDefs } from './NewsletterSubscription/NewsletterSubscription.typedefs';
import { typeDef as recommendationQuizTypeDefs } from './RecommendationQuiz/recommendationQuiz.typedefs';

import { typeDef as subscriptionTypedefs } from './Subscription/Subscription.typedefs';
import { typeDef as SubscriptionTierTypeDefs } from './SubscriptionTier/SubscriptionTier.typedefs';

import { Image } from './Image/Image';
import { saveImagePath } from './Image/saveImagePath';
import { deleteImage } from './Image/deleteImage';
import { typeDef as ImageTypeDefs } from './Image/Image.typedefs';

import { typeDef as emailComponentTypeDef } from './EmailEditor/EmailEditor.typedefs';
import { emailComponent } from './EmailEditor/EmailComponent';
import { sendTestEmail } from './EmailEditor/SendTestEmail';
import { AppLanguage } from './AppLanguage/AppLanguage';
import { typeDef as appLanguageTypeDef } from './AppLanguage/AppLanguage.typedefs';

import { updateScreening } from './Screening/updateScreening';
import { allScreeningAttributes } from './Screening/allScreeningAttributes';
import {
	createSoonInCinemaItem,
	deleteSoonInCinemaItem,
	updateSoonInCinemaItem,
} from './SoonInCinemaItem/crudSoonInCinemaItem';
import { typeDef as SoonInCinemaItemTypeDefs } from './SoonInCinemaItem/SoonInCinemaItem.typedefs';
import { soonInCinemaItems } from './SoonInCinemaItem/soonInCinemaItems';
import { typeDef as UsageStatisticsTypeDefs } from './UsageStatistics/UsageStatistics.typedefs';
import { subscriptionsStatistics } from './UsageStatistics/subscriptionsStatistics';
import {
	acceptAppTerms,
	allowDataUsage,
	allowAppTracking,
	optInToMarketingEmails,
} from './User/acceptAppTerms';
import { adminUsers } from './User/adminUsers';
import {
	updateUser,
	updateUserSubscription,
	updateEmail,
	updateUserProfile,
	resendActivationEmail,
	sendResetPasswordEmail,
	setInitialRatedMovies,
} from './User/updateUser';
import { increaseUserTestingStatus } from './User/increaseUserTestingStatus';
import { rememberSkippedOnboardingStep } from './User/rememberSkippedOnboardingStep';
import {
	createAnonymousUser,
	login,
	socialLogin,
	addLogin,
	loginPOS,
	refreshLogin,
	verifyEmail,
	updatePassword,
	logout,
	requestLoginCreation,
	requestEmailChange,
	resendEmailConfirmation,
	confirmEmail,
	requestPasswordReset,
	changePassword,
	testing_getConfirmationCode,
} from './User/login';
import { searchUsers, searchUsersTotal } from './User/searchUsers';
import { deselectCinema, selectCinema, selectCinemas } from './User/selectCinemas';
import { updateUserAdminStatus } from './User/updateUserAdminStatus';
import { typeDef as UserTypeDefs } from './User/User.typedefs';
import { userByQr } from './User/userByQr';
import { editUserGroupFilter } from './UserGroupFilter/editUserGroupFilter';
import { typeDef as UserGroupFilterTypeDefs } from './UserGroupFilter/UserGroupFilter.typedefs';
import { editUserGroupFilterOnReferencedMovie } from './UserGroupFilterOnReferencedMovie/editUserGroupFilterOnReferencedMovie';
import { typeDef as UserGroupFilterOnReferencedMovieTypeDefs } from './UserGroupFilterOnReferencedMovie/UserGroupFilterOnReferencedMovie.typedefs';
import { voucherCatalog } from './VoucherClass/voucherCatalog';
import { typeDef as VoucherClassTypeDefs } from './VoucherClass/VoucherClass.typedefs';
import { buyAndRedeemVoucher } from './VoucherInstance/buyAndRedeemVoucher';
import { buyVoucher } from './VoucherInstance/buyVoucher';
import { createVoucherInstances } from './VoucherInstance/createVoucherInstances';
import { redeemVoucher } from './VoucherInstance/redeemVoucher';
import { typeDef as VoucherInstanceTypeDefs } from './VoucherInstance/VoucherInstance.typedefs';
import { voucherInstanceByQR } from './VoucherInstance/voucherInstanceByQR';
import { voucherInstancesByOrderNumber } from './VoucherInstance/voucherInstancesByOrderNumber';
import { typeDef as contentSectionListTypeDefs } from './ContentSectionList/ContentSectionList.typedefs';

import { typeDef as LinkedAccountTypeDefs } from './LinkedAccount/LinkedAccounts.typedefs';
import { typeDef as InvoiceTypeDefs } from './Invoice/Invoice.typedefs';
import { gql } from 'apollo-server-core';
import { typeDef as RestAPITypeDefs } from './RestAPI/RestAPI.typedefs';
import { storeEikonaPreshowData } from './RestAPI/StoreEikonaPreshowData';
import { similarMovies } from './Movie/similarMovies';
import { GraphQLDate, GraphQLDateTime /*, GraphQLTime*/ } from 'graphql-iso-date';

import { sendNotifications, sendTestNotification } from './NotificationSender/sendNotifications';

import { typeDef as sendNotificationTypeDefs } from './NotificationSender/NotificationSender.typedefs';
import {
	ContentSectionList,
	contentSectionListByNameAndCinemas,
	PublicContentSectionListId,
} from './ContentSectionList/ContentSectionList';

import { typeDef as HeroPlayerTypeDefs } from './HeroPlayer/HeroPlayer.typedefs';
import { filmSeriesByCinemas } from './MovieList/filmSeriesByCinemas';
import { NewsListSection, PublicNewsListSectionId } from './ContentSectionList/NewsListSection';

import { typeDef as getImdbIdsTypeDefs } from './Misc/getImdbIds.typedefs';
import { getImdbIds } from './Misc/getImdbIds';
import { dismissExitPoll } from './Misc/dismissExitPoll';
import { getServiceStatus } from './Misc/getServiceStatus';

import {
	Context,
	PublicMovieId,
	PublicCinemaId,
	PublicUserId,
	PublicScreeningId,
	PublicMovieListId,
	PublicBonusProgramId,
	PublicCampaignId,
	PublicNewsItemId,
	PublicNotificationId,
	I18N,
	Language,
	PublicTargetGroupId,
	PublicCinemaOperatingCompanyId,
	PublicSubscriptionTierId,
	PublicBeaconId,
} from '../typescript-types';
import { Ticket, PublicTicketId } from './Ticket/Ticket';
import { Order, PublicOrderId } from './Order/Order';

import {
	deleteUser,
	deleteCineplexUser,
	testing_forceDeleteUser,
	leaveBonusProgram,
} from './User/deleteUser';
import { Notification } from './Notification/Notification';

import { addInvitedBy } from './BonusProgramMembership/addInvitedBy';
import { checkVoucherCode } from './BonusProgramMembership/checkVoucherCode';
import { checkVoucherCodes } from './BonusProgramMembership/checkVoucherCodes';
import { storeSDKLogin } from './SDKInput/storeSDKLogin';
import { storeConsent } from './SDKInput/storeConsent';

import { storeSeatPlan } from './ConfirmTicketPurchase/storeSeatPlan';
import { recordTicketPurchase } from './ConfirmTicketPurchase/recordTicketPurchase';

import { confirmOnlineTicketingBooking } from './ConfirmTicketPurchase/confirmOnlineTicketingBooking';

import { typeDef as ConfirmTicketProviderPurchaseTypeDefs } from './ConfirmTicketPurchase/ConfirmTicketPurchase.typedefs';
import { getMovieStatistics } from './UsageStatistics/MovieStatistics';
import { payoutInfo } from './UsageStatistics/payoutInfo';
import { subscribeToNewsletter } from './NewsletterSubscription/SubscribeToNewsletter';
import { subscribe } from './Subscription/subscribe';
import { validateVoucherCode } from './Subscription/validateVoucherCode';
import { requestOnlineTicketingVouchers as logUserScreeningInterests } from './Subscription/requestOnlineTicketingVouchers'; // we rename this to disguise the real functionality
import { reportInvalidOnlineTicketingVoucher as reportScreeningError } from './Subscription/reportInvalidOnlineTicketingVoucher'; // we rename this to disguise the real functionality
import { reportApprovedSubscription } from './Subscription/reportApprovedSubscription';

import { moviesToRateToInitializeRecommender } from './RecommendationQuiz/moviesToRateToInitializeRecommender';
import { editCampaignEmail } from './Campaign/editCampaignEmail';

import { SubscriptionTier } from './SubscriptionTier/SubscriptionTier';
import { unsubscribe } from './Subscription/unsubscribe';
import { createUserTokenForSubscriptionValidation } from './Subscription/createUserTokenForSubscriptionValidation';
import { requestLinkAccount } from './LinkedAccount/requestLinkAccount';
import { deleteLinkedAccount } from './LinkedAccount/deleteLinkedAccount';
import { declineLinkAccount } from './LinkedAccount/declineLinkAccount';
import { acceptLinkAccount } from './LinkedAccount/acceptLinkAccount';

import { capturePaypalOrderAndCreateTickets } from './Paypal/capturePaypalOrderAndCreateTickets';

import screeningAuditorium from './BookingProcess/screeningAuditorium';

import { typeDef as PaypalTypeDefs } from './Paypal/Paypal.typedefs';
import { Invoice } from './Invoice/Invoice';
import { createGoogleWalletBonusCard } from './BonusProgramMembership/createGoogleWalletBonusCard';
import { validateSubscriptionUsers } from './Subscription/validateSubscriptionUsers';
import { syncTicket } from './ConfirmTicketPurchase/syncTicket';
import { getOnlineTicketingBooking } from './ConfirmTicketPurchase/getOnlineTicketingBooking';
import { updateVoucherInstance } from './VoucherInstance/updateVoucherInstance';
import { rejectBonusProgramTerms } from './BonusProgramMembership/rejectBonusProgramTerms';
import { companies, company } from './Company/companies';
import { Company } from './Company/Company';
import {
	createCinemaOperatingCompany,
	updateCinemaOperatingCompany,
} from './CinemaOperatingCompany/queries';

import { typeDef as BeaconTypeDefs } from './Beacon/Beacon.typedefs';
import { createBeacon } from './Beacon/createBeacon';
import { Beacon } from './Beacon/Beacon';
import { updateBeacon } from './Beacon/updateBeacon';
import { deleteBeacon } from './Beacon/deleteBeacon';
import { missedPayouts } from './UsageStatistics/MissedPayout';
import { newSubscriptionStatistics } from './UsageStatistics/newSubscriptionStatistics';
import {
	incomingForeignMovieIdentifiers,
	MASTER_MOVIE_DB_ID,
	updateForeignMovieMapping,
} from './Movie/foreignMoviesAssignment';

const globalSchema = gql`
	scalar Json
	scalar DateTime
	scalar Date
	scalar Time
	directive @cost(value: Int) on FIELD_DEFINITION
	directive @costFactor(value: Int) on FIELD_DEFINITION

	type Query {
		test(inputVal: String): String
	}
	type Mutation {
		a(id: ID!): String
	}
`;

export const typeDefs = [
	globalSchema,
	OnboardingContentTypeDef,
	AppTypeDefs,
	AppReleaseTypeDefs,
	AudioFingerprintingConfigTypeDefs,
	CinemaOperatingCompanyTypeDefs,
	BonusProgramTypeDefs,
	BonusProgramMembershipTypeDefs,
	BonusProgramStatisticTypeDefs,
	CampaignTypeDefs,
	CastOrCrewMemberTypeDefs,
	CinemaTypeDefs,
	CompanyTypedefs,
	ContactFeedbackTypeDefs,
	ErrorStatisticTypeDefs,
	GeocodeTypeDefs,
	InterestRatingTypeDefs,
	ListOfMoviesTypeDefs,
	LogItemTypeDefs,
	MovieTypeDefs,
	TrailerTypeDefs,
	NewsItemTypeDefs,
	NotificationTypeDefs,
	NotificationPreferenceTypeDefs,
	ExternalNewsletterPreferenceTypeDefs,
	BookingProcessTypeDefs,
	ExternalShareLinkTypeDefs,
	ReviewRatingTypeDefs,
	ScreeningTypeDefs,
	TicketTypeDefs,
	OrderTypeDefs,
	MovieListTypeDefs,
	SoonInCinemaItemTypeDefs,
	UsageStatisticsTypeDefs,
	UserTypeDefs,
	UserGroupFilterTypeDefs,
	UserGroupFilterOnReferencedMovieTypeDefs,
	VoucherClassTypeDefs,
	VoucherInstanceTypeDefs,
	RestAPITypeDefs,
	sendNotificationTypeDefs,
	contentSectionListTypeDefs,
	HeroPlayerTypeDefs,
	getImdbIdsTypeDefs,
	dismissExitPollTypeDefs,
	ServiceStatusTypeDefs,
	ConfirmTicketProviderPurchaseTypeDefs,
	SDKInputTypeDefs,
	SeenInCinemaTypeDefs,
	SubscribeToNewsletterTypeDefs,
	recommendationQuizTypeDefs,
	emailComponentTypeDef,
	appLanguageTypeDef,
	TargetGroupTypeDef,
	GenreTypeDefs,
	PerkTypeDefs,
	AchievementTypeDefs,
	ImageTypeDefs,
	subscriptionTypedefs,
	SubscriptionTierTypeDefs,
	LinkedAccountTypeDefs,
	PaypalTypeDefs,
	InvoiceTypeDefs,
	BeaconTypeDefs,
	CinemaClusterTypeDefs,
];

export const scalarResolvers = {
	DateTime: GraphQLDateTime,
	Date: GraphQLDate,
	// TODO: decide wether we want to use this, it transforms times into Date object with date set to today, Time scalar is only used in campaign currentyl
	// Time: GraphQLTime,
};

export const queryResolvers = {
	testing_getConfirmationCode,
	testing_forceDeleteUser,
	onboardingContent,
	currentCinema,
	estimateCampaignSize,
	screenedMovies,
	userByQr,
	cinemas,
	cinemaCluster,
	cinemaClusters,
	company,
	companies,
	voucherCatalog,
	geocode,
	searchMovie,
	reverseGeocode,
	locationForZipCode,
	adminUsers,
	searchUsers,
	searchUsersTotal,
	soonInCinemaItems,
	appReleases,
	getImdbIds,
	externalUrl,
	appDeepLink,
	emailComponent,
	newSubscriptionStatistics,
	movieStatistics: getMovieStatistics,
	currentUser: async (_, __, { viewer }: Context) => {
		console.log('CurrentUser queried');
		return await User.gen(viewer, viewer.userId);
	},
	currentPOS: (_, __, { viewer }: Context) => {
		return viewer.posApp;
	},
	userById: async (_, { id }: { id: PublicUserId }, { viewer }: Context) => {
		return await User.gen(viewer, id);
	},
	cinema: async (_, { id }: { id: PublicCinemaId }, { viewer }: Context) => {
		return await Cinema.gen(viewer, id);
	},
	movies: async (_, { ids }: { ids: PublicMovieId[] }, { viewer }: Context) => {
		return await Movie.genMult(viewer, ids);
	},
	movie: async (_, { id }: { id: PublicMovieId }, { viewer }: Context) => {
		return await Movie.gen(viewer, id);
	},
	appLanguage: async (_, { language }: { language: Language }) => {
		return await AppLanguage.gen(language);
	},
	screenings: async (
		_,
		args: {
			movieIds?: PublicMovieId[];
			cinemaIds?: PublicCinemaId[];
			after?: Date;
			before?: Date;
		},
		{ viewer }
	) => {
		return await Screening.allScreenings(viewer, args);
	},
	screening: async (_, { id }: { id: PublicScreeningId }, { viewer }: Context) => {
		return await Screening.gen(viewer, id);
	},
	ticket: async (_, { id }: { id: PublicTicketId }, { viewer }: Context) => {
		return await Ticket.gen(viewer, id);
	},
	order: async (_, { id }: { id: PublicOrderId }, { viewer }: Context) => {
		return await Order.gen(viewer, id);
	},
	movieList: async (_, { id }: { id: PublicMovieListId }, { viewer, language }: Context) => {
		return await MovieList.gen(viewer, id, language);
	},
	allCinemaFilmSeries: async (
		_,
		{ cinemaIds, before }: { cinemaIds: PublicCinemaId[]; before: Date },
		{ viewer, language }: Context
	) => {
		return await filmSeriesByCinemas(viewer, language, cinemaIds, before);
	},
	bonusPrograms: async (_, { ids }: { ids: PublicBonusProgramId[] }, { viewer }: Context) => {
		return await BonusProgram.genMult(viewer, ids);
	},
	bonusProgram: async (_, { id }: { id: PublicBonusProgramId }, { viewer }: Context) => {
		return await BonusProgram.gen(viewer, id);
	},
	audioFingerprintingConfig: async () => {
		return AudioFingerprintingConfig;
	},
	usageStatistics: async (
		_,
		{ cinemaIds }: { cinemaIds: PublicCinemaId[] },
		{ viewer }: Context
	) => {
		return UsageStatistics.gen(viewer, cinemaIds);
	},
	errorStatistics: async (_, { pastDays }: { pastDays: number }, { viewer }: Context) => {
		return ErrorStatistics.gen(viewer, pastDays);
	},
	allMovies: async (
		_,
		{ isFromMasterFilmDB = false }: { isFromMasterFilmDB: boolean },
		ctx: Context
	) => {
		const res = await db.queryWithCache(sql`
					SELECT 
					id 
					FROM 
					pub.movies m
					WHERE ${!isFromMasterFilmDB} OR m.id < ${MASTER_MOVIE_DB_ID}
					`);
		return await Movie.genMult(
			ctx.viewer,
			res.rows.map((r) => Movie.encodeDbId(r.id))
		);
	},
	incomingForeignMovieIdentifiers,
	voucherInstanceByQR,
	voucherInstancesByOrderNumber,
	campaignsByCinemas: async (
		_,
		{ cinemaIds, newCampaigns }: { cinemaIds: PublicCinemaId[]; newCampaigns: boolean },
		{ viewer }: Context
	) => {
		const campaigns = await Campaign.campaignsByCinemas(viewer, cinemaIds, newCampaigns);
		return { campaigns };
	},
	campaign: async (_, { id }: { id: PublicCampaignId }, { viewer }: Context) => {
		return await Campaign.gen(viewer, id);
	},
	allTargetGroupsByCinemaOperatingCompanies: async (
		_,
		{ cinemaOperatingCompanyIds }: { cinemaOperatingCompanyIds: PublicCinemaOperatingCompanyId[] },
		{ viewer, i18n }: Context
	) => {
		const targetGroups = await TargetGroup.allTargetGroupsByCinemaOperatingCompanies(
			viewer,
			cinemaOperatingCompanyIds,
			{ i18n }
		);
		return { targetGroups };
	},
	targetGroup: async (_, { id }: { id: PublicTargetGroupId }, { viewer, i18n }: Context) => {
		return await TargetGroup.gen(viewer, id, { i18n });
	},
	newsItem: async (_, { id }: { id: PublicNewsItemId }, { viewer }: Context) => {
		return await NewsItem.gen(viewer, id);
	},
	notification: async (_, { id }: { id: PublicNotificationId }, { viewer, language }: Context) => {
		return await Notification.gen(viewer, id, language);
	},
	similarMovies,
	contentSectionList: async (_, { id }: { id: PublicContentSectionListId }, context: Context) => {
		console.log('Queried ContentSectionList', id);
		return await ContentSectionList.gen(null, id, context);
	},
	contentSectionListByNameAndCinemas,
	discoverContent: async (_, __, context: Context) => {
		return await ContentSectionList.gen(
			null,
			('Q29udGVudFNlY3Rpb25MaXN0OjE=' as unknown) as PublicContentSectionListId,
			context
		);
	},
	newsListSection: async (
		_,
		{ id }: { id: PublicNewsListSectionId },
		{ i18n, viewer }: Context
	) => {
		return await NewsListSection.gen({ id }, { i18n, viewer });
	},
	test: async (_, __, { i18n }: { i18n: I18N }) => {
		return i18n.t('Global.appName');
	},
	allImages: async (_, __, { viewer }: Context) => {
		return await Image.allImages(viewer);
	},
	checkVoucherCode,
	checkVoucherCodes,
	moviesToRateToInitializeRecommender,
	allGenres,
	allPerks,
	allAchievements,
	allVoucherClassesOfCinemaOperatingCompany,
	getAvailableVoucherClasses,
	searchCastOrCrewMember,
	allScreeningAttributes,
	subscriptionTiers: async (_, { ids }: { ids?: PublicSubscriptionTierId[] }, ctx: Context) => {
		return await SubscriptionTier.genMult(ctx.viewer, ids, ctx.brand === 'CINFINITY');
	},
	screeningAuditorium,
	subscriptionsStatistics,
	payoutInfo,
	missedPayouts,
	invoice: async (_, { id }, context: Context) => {
		return await Invoice.gen(id, context.viewer);
	},
	getServiceStatus,
	validateSubscriptionUsers,
	getOnlineTicketingBooking,
	beaconById: async (_, { id }: { id: PublicBeaconId }, { viewer }: Context) => {
		if (!viewer.isRoot && !viewer.isAdmin) {
			return new ForbiddenError('You must be logged in as an admin');
		}
		return await Beacon.gen(viewer, id);
	},
	beacons: async (_, __, { viewer }: Context) => {
		if (!viewer.isRoot && !viewer.isAdmin) {
			return new ForbiddenError('You must be logged in as an admin');
		}
		return await Beacon.genMult();
	},
};

export const mutationResolvers = {
	createAnonymousUser,
	requestLoginCreation,
	requestEmailChange,
	resendEmailConfirmation,
	confirmEmail,
	requestPasswordReset,
	changePassword,
	addLogin,
	login,
	cancelTicket: Ticket.cancelTicket,
	updateTicket: Ticket.updateTicket,
	openTicket: Ticket.openTicket,
	registerBeaconScanned: Beacon.registerBeaconScanned,
	cancelOrder: Order.cancelOrder,
	startOrderPreparation: Order.startOrderPreparation,
	updateEmail,
	updateUserSubscription,
	updateUserProfile,
	updateUser,
	socialLogin,
	loginPOS,
	refreshLogin,
	logout,
	updatePassword,
	selectCinemas,
	selectCinema,
	deselectCinema,
	createCinemaCluster,
	updateCinemaCluster,
	deleteCinemaClusters,
	bookCoupon,
	claimOrDiscardProvisionalBonusPointBooking,
	buyVoucher,
	createVoucherInstances,
	upsertReviewRating,
	upsertQuizReviewRating,
	deleteReviewRating,
	deleteReviewRating_2,
	upsertSeenInCinema,
	deleteSeenInCinema,
	upsertInterestRating,
	createOnlineTicketingOutlinkId,
	acceptBonusProgramTerms,
	rejectBonusProgramTerms,
	acceptCinuruTerms: acceptAppTerms,
	acceptAppTerms,
	allowDataUsage,
	optInToMarketingEmails,
	allowAppTracking,
	increaseUserTestingStatus,
	rememberSkippedOnboardingStep,
	logItems,
	bookCinemaTicketPoints,
	redeemVoucher,
	buyAndRedeemVoucher,
	upsertApp,
	updateNotificationPreference,
	updateExternalNewsletterPreference,
	startBookingProcess,
	selectSeats,
	submitBookingProcess,
	selectSeatPricingCategories,
	selectSeatingAreaPricingCategories,
	processScannedQrCode,
	cancelBookingProcess,
	suggestCinema,
	createCompany: Company.createCompany,
	editCompany: Company.editCompany,
	deleteCompanies: Company.deleteCompanies,
	submitContactForm,
	createCampaign,
	editCampaign,
	setCampaignStatus,
	deleteCampaigns,
	updateCampaignsStatus,
	editUserGroupFilter,
	editUserGroupFilterOnReferencedMovie,
	updateForeignMovieMapping,
	upsertNewsItem,
	verifyEmail,
	sendResetPasswordEmail,
	resendActivationEmail,
	setInitialRatedMovies,
	updateCinema,
	createCinema,
	updateUserAdminStatus,
	createIntercomLead,
	updateBonusProgram,
	createSoonInCinemaItem,
	updateSoonInCinemaItem,
	deleteSoonInCinemaItem,
	updateScreening,
	joinBonusProgram,
	submitAppFeedback,
	storeEikonaPreshowData,
	sendNotifications,
	sendTestNotification,
	deleteUser,
	deleteCineplexUser,
	leaveBonusProgram,
	addInvitedBy,
	dismissExitPoll,
	storeSeatPlan,
	recordTicketPurchase,
	confirmOnlineTicketingBooking,
	storeSDKLogin,
	storeConsent,
	subscribeToNewsletter,
	editCampaignEmail,
	sendTestEmail,
	createTargetGroup,
	editTargetGroup,
	deleteTargetGroups,
	updateCinemaOperatingCompanyTargetGroupFilterConfig,
	updateCinemaOperatingCompany,
	createCinemaOperatingCompany,
	saveImagePath,
	deleteImage,
	updateTargetGroupsStatus,
	subscribe,
	reportApprovedSubscription,
	logUserScreeningInterests,
	reportScreeningError,
	unsubscribe,
	createUserTokenForSubscriptionValidation,
	requestLinkAccount,
	deleteLinkedAccount,
	declineLinkAccount,
	acceptLinkAccount,
	capturePaypalOrderAndCreateTickets,
	createGoogleWalletBonusCard,
	syncTicket,
	validateVoucherCode,
	updateVoucherInstance,
	createBeacon,
	updateBeacon,
	deleteBeacon,
};
