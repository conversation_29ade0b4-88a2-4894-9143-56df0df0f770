import { addWeeks, addYears, format } from 'date-fns';
import crypto from 'crypto';

import { sendVoucher } from './sendVoucher';
import { fourMonthsImage, twelveMonthsImage } from './images';

import { db, sendEmail, sendErrorMail, sql } from '../../utils';
import { createVDSXml } from './createVDSXml';
import fetch from 'node-fetch';
import { generateQrCode } from '../../utils/generateQrCode';

export const processShopifyOrders = async () => {
	// Load data from db
	const res = await db.query(sql`
    SELECT id, header, body, status
    FROM priv.shopify_webhook_call
    WHERE status = 'NEW'
    LIMIT 10
  `);

	for (const row of res.rows) {
		try {
			console.log('processing Order', row.id);
			await db.query(sql`
        UPDATE priv.shopify_webhook_call
        SET status = 'PENDING'
        WHERE id = ${row.id}
        `);
			const order = JSON.parse(row.body);
			console.log('body', order);
			console.log('line_items', order.line_items);

			//Extract needed Data
			type SKUS = '12M-Shipping' | '4M-Shipping' | '12M-digital' | '4M-digital';
			const email = order.email;
			const products: { sku: SKUS; price: string; variant_id: string }[] = [];
			order.line_items.forEach((item) => {
				for (let i = 0; i < item.quantity; i++) {
					products.push({ sku: item.sku as SKUS, price: item.price, variant_id: item.variant_id });
				}
			});
			const firstName = order.customer.first_name;
			const validUntilDate = addWeeks(addYears(new Date(), 3), 1);
			const validUntil = `Gültig bis: ${format(validUntilDate, 'dd.MM.yyyy')}`;

			let vdsVouchers = [];
			//TODO Insert Voucher into db
			for (const product of products) {
				let sendEmailVoucher = false;
				let sendVoucherToVDS = false;
				let voucherTypDbId = 0;
				let voucherPrefix = '';
				let image;
				switch (product.sku) {
					case '4M-digital':
						image = fourMonthsImage;
						sendEmailVoucher = true;
						sendVoucherToVDS = false;
						voucherTypDbId = 1;
						voucherPrefix = 'a';
						break;
					case '12M-digital':
						image = twelveMonthsImage;
						sendEmailVoucher = true;
						sendVoucherToVDS = false;
						voucherTypDbId = 2;
						voucherPrefix = 'b';
						break;
					case '4M-Shipping':
						sendVoucherToVDS = true;
						sendEmailVoucher = false;
						voucherTypDbId = 1;
						voucherPrefix = 'c';

						break;
					case '12M-Shipping':
						sendVoucherToVDS = true;
						sendEmailVoucher = false;
						voucherTypDbId = 2;
						voucherPrefix = 'd';
						break;
					default:
						console.error('Unknown SKU', product);
						await sendErrorMail(
							'Unknown SKU in Shopify webhook',
							`SKU: ${product}, Body: ${JSON.stringify(order)}`
						);
				}

				// Create voucher
				const qrCodeData = generateQrCode(voucherPrefix);

				await db.query(sql`
			INSERT INTO priv.p_voucher (voucher_type_id, qr_code, valid, created_datetime, valid_until, order_number ) VALUES
			(${voucherTypDbId},${qrCodeData}, true, now(), ${validUntilDate}, ${order.order_number})`);

				// Send Voucher via Mail

				if (sendEmailVoucher) {
					await sendVoucher({
						email,
						firstName,
						qrCodeData,
						validUntil,
						image,
					});
				}
				if (sendVoucherToVDS) {
					vdsVouchers.push({
						sku: product.variant_id,
						quantity: 1,
						price: product.price,
						qrCode: qrCodeData,
					});
				}
			}
			if (vdsVouchers.length > 0) {
				const vdsXml = createVDSXml({
					orderId: order.order_number,
					orderDate: new Date(order.created_at),
					customerId: order.user_id || order.contact_email,
					express: order.shipping_lines[0].code === 'Express',
					billingAddress: {
						firstName: order.customer.first_name,
						lastname: order.customer.last_name,
						streetWithNumber: order.customer.default_address.address1,
						addressRemark: order.customer.default_address.address2,
						zip: order.customer.default_address.zip,
						city: order.customer.default_address.city,
						state: order.customer.default_address.province_code,
						country: order.customer.default_address.country_code,
						phone: order.customer.default_address.phone,
						email: order.contact_email,
					},
					shippingAddress: {
						firstName: order.shipping_address.first_name,
						lastname: order.shipping_address.last_name,
						streetWithNumber: order.shipping_address.address1,
						addressRemark: order.shipping_address.address2,
						zip: order.shipping_address.zip,
						city: order.shipping_address.city,
						state: order.shipping_address.province_code,
						country: order.shipping_address.country_code,
						phone: order.shipping_address.phone,
						email: order.contact_email,
					},
					products: vdsVouchers,
				});
				console.log('VDS XML', vdsXml);
				await sendErrorMail('VDS XML', vdsXml);

				//https://px05.vds-herzberg.de:451/pixiCFN/order_import.php
				const result = await fetch('https://px05.vds-herzberg.de:451/pixiCFN/', {
					method: 'POST',
					body: vdsXml,
					headers: {
						'Content-Type': 'application/xml',
						'Authorization': `Basic ${process.env.VDS_AUTH}`,
					},
				});
				console.log('VDS Result', result);
			}
			await db.query(sql`
        UPDATE priv.shopify_webhook_call
        SET status = 'DONE'
        WHERE id = ${row.id}
        `);
		} catch (e) {
			console.error('Error in Shopify processing', e);
			await sendErrorMail(
				'Error in Shopify processing',
				` Error: ${e}, Row: ${JSON.stringify(row)}`
			);
			await sendEmail({
				from: '<EMAIL>',
				to: '<EMAIL>',
				subject: 'Fehler bei der Übermittlung einer Shopify Bestellung an VDS',
				text: ` Fehler: ${e}, 
				 
				 Shopify Daten: ${JSON.stringify(row)}
				 `,
			});
		}
	}
};
