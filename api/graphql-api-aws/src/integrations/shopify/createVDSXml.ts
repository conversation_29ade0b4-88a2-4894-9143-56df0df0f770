import { format } from 'date-fns';

export type VDSAdress = {
	firstName: string;
	lastname: string;
	streetWithNumber: string;
	addressRemark: string;
	zip: string;
	city: string;
	state: string;
	country: string;
	phone: string;
	email: string;
};
export type VDSProduct = {
	sku: string;
	quantity: number;
	price: number;
	qrCode: string;
};

export const createVDSXml = ({
	orderId,
	orderDate,
	customerId,
	billingAddress,
	shippingAddress,
	products,
	express,
}: {
	orderId: string;
	orderDate: Date;
	customerId: string;
	billingAddress: VDSAdress;
	shippingAddress: VDSAdress;
	products: VDSProduct[];
	express: boolean;
}) => {
	return `
<Envelope>
<Body xmlns:pix="https://px05.vds-herzberg.de:451/pixiCFN/">
<pixiImportAddXML>
<ChannelRef>8</ChannelRef>
<OperationType>ORDER</OperationType>
<XML>
<ORDER type="standard" version="1.0">
<ORDER_HEADER>
<CONTROL_INFO>
<GENERATION_DATE>${format(new Date(), 'yyyy-MM-dd HH:mm')}</GENERATION_DATE>
<GENERATOR_INFO>Cinfinity Shopify Integration</GENERATOR_INFO>
</CONTROL_INFO>
<ORDER_INFO>
<ORDER_ID>${orderId}</ORDER_ID>
<ORDER_DATE>${format(orderDate, 'yyyy-MM-dd HH:mm')}</ORDER_DATE>
<ORDER_TYPE>B2C</ORDER_TYPE>
<DATABASE>pixi_CFN</DATABASE>
<SHOPID>CFN</SHOPID>
<SHOP_NOTE/>
<ORDER_SHIPLOCK>N</ORDER_SHIPLOCK>
<BUYER_SHIPLOCK>N</BUYER_SHIPLOCK>
<TRANSPORT_REMARKS/>
<GIFT_MESSAGE/>
<REFERRER>Shopify</REFERRER>
<LOCATION/>
<ORDER_PARTIES>
<BUYER_PARTY>
<PARTY>
<SHOPID>CFN</SHOPID>
<PARTY_ID type="buyer_specific">${customerId}</PARTY_ID>
<ADDRESS>
<SAL/>
<NAME/>
<NAME2>${billingAddress.firstName}</NAME2>
<NAME3>${billingAddress.lastname}</NAME3>
<STREET>${billingAddress.streetWithNumber}</STREET>
<ADDRESS_REMARK>${billingAddress.addressRemark}</ADDRESS_REMARK>
<ZIP>${billingAddress.zip}</ZIP>
<ZIPBOX/>
<CITY>${billingAddress.city}</CITY>
<COUNTRY>${billingAddress.country}</COUNTRY>
${billingAddress.phone ? `<PHONE>${billingAddress.phone}</PHONE>` : '<PHONE/>'}
<FAX/>
<EMAIL>${billingAddress.email}</EMAIL>
</ADDRESS>
</PARTY>
</BUYER_PARTY>
<INVOICE_PARTY>
<PARTY>
<ADDRESS>
<SAL/>
<NAME/>
<NAME2>${billingAddress.firstName}</NAME2>
<NAME3>${billingAddress.lastname}</NAME3>
<STREET>${billingAddress.streetWithNumber}</STREET>
<ADDRESS_REMARK>${billingAddress.addressRemark}</ADDRESS_REMARK>
<ZIP>${billingAddress.zip}</ZIP>
<ZIPBOX/>
<CITY>${billingAddress.city}</CITY>
<STATE></STATE>
<COUNTRY>${billingAddress.country}</COUNTRY>
${billingAddress.phone ? `<PHONE>${billingAddress.phone}</PHONE>` : '<PHONE/>'}
<FAX/>
<EMAIL>${billingAddress.email}</EMAIL>
</ADDRESS>
</PARTY>
</INVOICE_PARTY>
<SHIPMENT_PARTIES>
<DELIVERY_PARTY>
<PARTY>
<ADDRESS>
<SAL/>
<NAME/>
<NAME2>${shippingAddress.firstName}</NAME2>
<NAME3>${shippingAddress.lastname}</NAME3>
<STREET>${shippingAddress.streetWithNumber}</STREET>
<ZIP>${shippingAddress.zip}</ZIP>
<ZIPBOX/>
<CITY>${shippingAddress.city}</CITY>
<STATE></STATE>
<COUNTRY>${shippingAddress.country}</COUNTRY>
${shippingAddress.phone ? `<PHONE>${shippingAddress.phone}</PHONE>` : '<PHONE/>'}
<FAX/>
<EMAIL>${shippingAddress.email}</EMAIL>
</ADDRESS>
</PARTY>
</DELIVERY_PARTY>
</SHIPMENT_PARTIES>
</ORDER_PARTIES>
<PRICE_CURRENCY>EUR</PRICE_CURRENCY>
<PAYMENT>
<SHOPIFYPAYMENTS/>
</PAYMENT>
<REMARK type="shipping">0</REMARK>
<REMARK type="shippingvendor">${express ? 'DHE' : 'DHL'}</REMARK>
<REMARK type="DISCOUNT">0</REMARK>
</ORDER_INFO>
</ORDER_HEADER>
<ORDER_ITEM_LIST>
${products
	.map(
		(product, index) => `<ORDER_ITEM>
<LINE_ITEM_ID>${index}</LINE_ITEM_ID>
<ARTICLE_ID>
<SUPPLIER_AID>${product.sku}</SUPPLIER_AID>
</ARTICLE_ID>
<QUANTITY>${product.quantity}</QUANTITY>
<ITEM_NOTE>${product.qrCode}</ITEM_NOTE>
<ARTICLE_PRICE type="udp_gross_customer">
<FULL_PRICE>${product.price}</FULL_PRICE>
<PRICE_AMOUNT>${product.price}</PRICE_AMOUNT>
<DISCOUNT_PERC>0</DISCOUNT_PERC>
</ARTICLE_PRICE>
<DF_TYPE>2</DF_TYPE>
</ORDER_ITEM>`
	)
	.join('\n')}
</ORDER_ITEM_LIST>
</ORDER>
</XML>
</pixiImportAddXML>
</Body>
</Envelope>`;
};
