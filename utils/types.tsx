// from https://github.com/microsoft/TypeScript/pull/35998 to be replaced with awaited keyword
export type Awaited<T> = T extends { then(onfulfilled: (value: infer U) => unknown): unknown }
	? U
	: T extends { then(...args: unknown[]): unknown }
	? never
	: T;

export type ID = string;
export type URL = string;
export type Email = string;

export type LogOptions = {
	source: 'APP' | 'POS' | 'ADMIN_UI' | 'OTHER' | 'NOTIFICATION_SERVICE';
	session?: string;
	appVersion?: string;
	device?: string;
	appId?: string;
	url?: string;
};
export type LogItem =
	| {
			type: 'GLOBAL_ERROR';
			value: { errorMessage: string; context: Record<string, string> };
	  }
	| {
			type: 'REPORT_ISSUE_TO_SUPPORT';
			value: string;
			datetime: string;
	  }
	| {
			type: 'HIT_ERROR_BOUNDARY';
			value: { errorMessage: string; context: Record<string, string> };
	  }
	| {
			type: 'GRAPHQL_ERROR';
			value: { errorMessage: string; context: Record<string, string> };
	  }
	| {
			type: 'IMAGE_ERROR';
			value: { errorMessage: string; context: Record<string, string> };
	  }
	| {
			type: 'APP_STARTED';
			value: undefined;
	  }
	| {
			type: 'APP_INITIALIZED';
			value: {
				appState: string;
				initTimeline: { name: string; timestamp: number; duration: number }[];
			};
	  }
	| {
			type: 'SCREEN_VIEWED';
			value: { key: string; tabKey: string; params?: Record<string, string>; link: string };
	  }
	| {
			type: 'LINK_OPENED';
			value: { link: string };
	  }
	| {
			type: 'INVALID_LINK';
			value: { link: string; reason: string };
	  }
	| {
			type: 'TICKETING_LINK_CLICKED';
			value: { screeningId: ID; tabKey: string };
	  }
	| {
			type: 'MOVIE_DETAILS_VIEWED';
			value: { movieId: ID; tabKey: string };
	  }
	| {
			type: 'TRAILER_WATCHED';
			value: { movieId: ID; durationInSeconds: number; progressInSeconds: number };
	  }
	| {
			type: 'MOVIE_RATED';
			value: {
				type: 'INTEREST_RATING' | 'REVIEW_RATING';
				value: number;
				movieId: ID;
				screenKey: string;
				link: string;
			};
	  }
	| {
			type: 'NETWORK_STATUS_CHANGED';
			value: { connected?: boolean | null };
	  }
	| {
			type: 'SETTINGS_CHANGED';
			value: {
				setting:
					| 'COLOR_SCHEME'
					| 'TAB_BAR_LABELS'
					| 'ANALYTICS_REPORTING'
					| 'CRASH_REPORTING'
					| 'SWITCH_COLOR_SHEME_ON_BRIGHTNESS'
					| 'COLOR_SHEME_BRIGHTNESS_THRESHOLD'
					| 'SCALE';
				value: number | string | boolean;
			};
	  }
	| {
			type: 'SHOWTIMES_FILTER_PRESSED';
			value: {
				type: 'DATE' | 'TYPE' | 'CINEMA' | 'ATTRIBUTE';
				key: Date | string | ID;
				value: boolean;
			};
	  }
	| {
			type: 'NOTIFICATION_RECEIVED';
			value: {
				notificationId: string;
			};
	  }
	| {
			type: 'NOTIFICATION_PRESSED';
			value: {
				messageId: string;
				title: string;
				body: string;
				link?: string;
				imageUrl?: string;
				channel?: 'CINEMA_NEWS' | 'EXIT_POLL' | 'WATCHLIST' | 'BONUS_PROGRAM';
				notificationId?: string;
			};
	  }
	| {
			type: 'NOTIFICATION_LIST_ITEM_PRESSED';
			value: {
				id: ID;
				title: string;
				body?: string | undefined;
				link?: string | undefined;
				image?: string | undefined;
				createdAt: string;
			};
	  }
	| {
			type: 'HERO_PLAYER_ELEMENT_CLICKED';
			value: { heroPlayerElementTitle: string; appPath?: string; url?: string };
	  }
	| {
			type: 'ASK_FOR_NOTIFICATION_PREFERENCE';
			value: { channel: string };
	  }
	| {
			type: 'SET_NOTIFICATION_PREFERENCE';
			value: {
				channel: string;
				pushRequested?: boolean;
				emailRequested?: boolean;
			};
	  }
	| {
			type: 'SET_EXTERNAL_NEWSLETTER_PREFERENCE';
			value: {
				externalNewsletterId: ID;
				subscribed: boolean;
			};
	  }
	| {
			type: 'PERMISSIONS_REQUEST';
			value: {
				needsToGoToSettings?: boolean;
				permissionType: 'notifications' | 'camera' | 'location';
				granted: boolean;
			};
	  }
	| {
			type: 'EXPANDED_CINEMA_DETAIL_SECTION';
			value: { sectionName: string };
	  }
	| {
			type: 'TRYING_SOCIAL_AUTH';
			value: {
				type: 'LOGIN' | 'SIGNUP';
				provider: 'FACEBOOK' | 'APPLE' | 'GOOGLE';
				screenKey: string;
			};
	  }
	| {
			type: 'SKIPPED_LOGIN';
			value: undefined;
	  }
	| {
			type: 'LOGIN_SUCCESS';
			value: { screenKey: string };
	  }
	| {
			type: 'LOGIN_FAILED';
			value: { errors: string[]; screenKey: string };
	  }
	| {
			type: 'SIGNUP_SUCCESS';
			value: { screenKey: string };
	  }
	| {
			type: 'SIGNUP_FAILED';
			value: { errors: string[]; screenKey: string };
	  }
	| {
			type: 'VERIFY_EMAIL_SUCCESS';
			value: { screenKey: string };
	  }
	| {
			type: 'VERIFY_EMAIL_FAILED';
			value: { error: string; screenKey: string };
	  }
	| {
			type: 'UPDATE_PASSWORD_SUCCESS';
			value: { screenKey: string };
	  }
	| {
			type: 'UPDATE_PASSWORD_FAILED';
			value: { error: string; screenKey: string };
	  }
	| {
			type: 'REFRESHING_AUTH_TOKEN';
			value: undefined;
	  }
	| {
			type: 'DELETE_ACCOUNT';
			value: undefined;
	  }
	| {
			type: 'DELETE_ACCOUNT_FAILED';
			value: { appId: ID; error: string };
	  }
	| {
			type: 'LOGOUT';
			value: undefined;
	  }
	| {
			type: 'LOGOUT_FAILED';
			value: { appId: ID; error: string };
	  }
	| {
			type: 'SELECTED_CINEMA';
			value: { cinemaId: ID; screenKey: string };
	  }
	| {
			type: 'SET_SELECTED_CINEMAS';
			value: { cinemaIds: ID[]; screenKey: string };
	  }
	| {
			type: 'DESELECTED_CINEMA';
			value: { cinemaId: ID; screenKey: string };
	  }
	| {
			type: 'SUBMIT_APP_FEEDBACK';
			value: { positive?: boolean; screenKey: string };
	  }
	| {
			type: 'OPEN_APP_STORE_RATING';
			value: { screenKey: string };
	  }
	| {
			type: 'CODEPUSH_UPDATE_SUCCESS';
			value: { version?: string; buildCode?: number; previousBuildCode?: number };
	  }
	| {
			type: 'CODEPUSH_UPDATE_ROLLBACK';
			value: { version?: string; buildCode?: number; previousBuildCode?: number };
	  }
	| {
			type: 'POTENTIALLY_BOUGHT_TICKET';
			value: { indicator: 'SHOWTIMES_LINK_OPENED' | 'QR_CODE_SHOWN'; showtimeId?: ID };
	  }
	| {
			type: 'BOUGHT_TICKET';
			value: { indicator: 'KINOHELD_SUCCESS_ROUTE'; showtimeId: ID; ticketUrl: string };
	  }
	| {
			type: 'INVITE_MEMBER';
			value: undefined;
	  }
	| {
			type: 'SEARCH';
			value: { query: string; resultCount: number };
	  }
	| {
			type: 'CLICKED_NEWSLETTER_LINK';
			value: any;
	  }
	| {
			type: 'FORWARD_NEWSLETTER_DATA';
			value: any;
	  }
	| {
			type: 'BUG_LOGGER';
			value: string;
	  }
	| {
			type: 'CLICKED_CARD_WITH_BUTTON';
			value: { sectionId: string; title?: string };
	  }
	| {
			type: 'GOOGLE_AD_MANAGER_ERROR';
			value: { type: 'BANNER' | 'INTERSTITIAL'; error?: string; errorMessage?: string };
	  }
	| {
			type: 'NOTIFICATIONS_OVERVIEW';
			value: string[];
	  };

export type RawLogItem = {
	type: LogItem['type'];
	datetime: Date;
	value: string;
};
