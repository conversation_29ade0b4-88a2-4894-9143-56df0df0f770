{"Navigator": {"serviceStatus": {"title_down": "Service nicht verfügbar", "title_partial": "Service eingeschränkt verfügbar", "hint": "Bitte versuche es später erneut."}}, "UpdatingView": {"loadingUpdateLabel": "Update wird geladen ...", "lookingForUpdateLabel": "Suche nach Update ...", "askToInstallNativeUpdateDialog": {"title": "Update verfügbar", "description": "Eine neuere Version der App ist im App Store verfügbar, bitte lade diese um die neusten Features und Verbesserungen zu erhalten.", "description_android": "Eine neuere Version der App ist im Play Store verfügbar, bitte lade diese um die neusten Features und Verbesserungen zu erhalten.", "dismissButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "installUpdateButtonLabel": "Jetzt updaten"}, "needToInstallNativeUpdateDialog": {"title": "Update er<PERSON><PERSON><PERSON>", "description": "Eine neuere Version der App ist im App Store verfügbar. Diese muss geladen werden, um die App weiter verwenden zu können.", "description_android": "Eine neuere Version der App ist im Play Store verfügbar. Diese muss geladen werden, um die App weiter verwenden zu können.", "installUpdateButtonLabel": "Jetzt updaten"}}, "OnboardingView": {"loginButtonLabel": "Anmelden", "nextButtonLabel": "Registrieren", "networkErrorDialog": {"title": "Netzwerk-Fehler", "description": "Bitte versuche es noch einmal."}, "onboardingItems": [{"image": "https://static.cinuru.com/public/cinfinity/slider1.jpg", "title": "Willkommen bei CINFINITY", "description": "Registriere dich kostenlos, entdecke die App, schließe dein Abo ab und genieße unendlich Kino!"}, {"image": "https://static.cinuru.com/public/cinfinity/slider2.jpg", "title": "So viel <PERSON>, wie du willst.", "description": "Registriere dich kostenlos, entdecke die App, schließe dein Abo ab und genieße unendlich Kino!"}], "onboardingImageContent": {"notifications": [{"title": "Letz<PERSON> Chance", "body": "‘{{movieTitle}}’ von deiner Merkliste läuft nur noch diese Woche im Kino"}, {"title": "Dein Film der Woche", "body": "Wir glauben ‘{{movieTitle}}’ könnte dir gefallen, basierend auf deinen vorherigen Filmbewertungen."}], "stampCardLabel": "<PERSON><PERSON>", "bonusCardUserName": "<PERSON>", "bonusCardNumber": "123456"}, "startAppButtonLabel": "Starten", "optInToSubscriptionButtonLabel": "zum $t(app:Global.appName) Abo", "optOutOfSubscriptionButtonLabel": "<PERSON>cht jetzt"}, "TermsAgreementView": {"title": "Datenschutz", "description": "Bitte bestätige, dass du die [Allgemeinen Geschäftsbedingungen](https://cinfinity.de/agb) akzeptierst und die Geltung unserer [Nutzungsbedingungen]($t(app:Global.termsLink)) und [Datenschutzerklärung]($t(app:Global.privacyLink)) zur Kenntnis genommen hast.", "description_update": "Wir haben unsere [Datenschutzerklärung]($t(app:Global.privacyLink)) an<PERSON><PERSON><PERSON>, weil neue App-Funktionen hinzugekommen sind.", "agreementButtonLabel": "OK", "agreementCheckBoxLabel": "Ich habe die Geltung zur Kenntnis genommen.", "dataUsageDescription": "Option: Die App kann die angezeigten Filme für dich personalisieren. Dazu werden deine Filmbewertungen und Nutzungsdaten verarbeitet.", "dataUsageCheckBoxLabel": "<PERSON>ch bin einverstanden mit der Datenverarbeitung.", "startAppButtonLabel": "<PERSON><PERSON>", "agreementNecessaryDialog": {"title": "Zustimmung ist erforderlich"}, "sendFeedbackButtonLabel": "Feed<PERSON> senden"}, "InitialCinemaSelectionView": {"title": "<PERSON><PERSON>", "selectSection": {"locationUsageDescription": "Mit deinem Standorts kann die App dein Kino finden. <PERSON><PERSON> Sorge wir speichern deinen Standort nicht.", "selectCinemasAutomaticallyButtonLabel": "<PERSON><PERSON>en", "selectCinemasManuallyButtonLabel": "<PERSON><PERSON>"}, "welcomeSection": {"title": "Will<PERSON>mmen im", "selectCinemasButtonLabel": "<PERSON><PERSON><PERSON> Kinos au<PERSON>wählen"}, "startAppButtonLabel": "<PERSON> Geht's", "noCinemaSelectedErrorDialog": {"title": "<PERSON>ähle deine Lieblingskinos aus"}}, "CinemaSelectionView": {"title": "<PERSON><PERSON><PERSON><PERSON>", "title_onboarding": "Wähle dein Lieblingskino aus!", "currentLocationLabel": "Aktueller Standort", "searchInputLabel": "Kino / PLZ", "searchErrorDialog": {"title": "<PERSON><PERSON>", "title_networkError": "Offline", "description_networkError": "Ohne Internetverbindung können wir den angegebenen Ort nicht lokalisieren.", "description_invalidZipcode": "Wir konnten diese Postleitzahl nicht finden.", "description_unknownCinemaNameOrCity": "‘{{query}}’ ist kein ein gültiger Kino -oder Stadtname."}}, "LoginView": {"title": "Registrieren", "title_alreadyRegistered": "Anmelden", "title_joinBonusProgram": "Bonusprogram", "title_profile": "Profil", "loginWithGoogleButtonLabel": "Mit Google anmelden", "loginWithAppleButtonLabel": "Mit Apple anmelden", "loginWithFacebookButtonLabel": "Mit Facebook anmelden", "orSeparatorLabel": "oder", "updateProfile": {"errorDialog_networkError": {"title": "Offline", "description": "<PERSON> scheinst offline zu sein, bitte versuche es erneut."}, "errorDialog_unexpectedError": {"title": "<PERSON><PERSON>", "description": "Es ist ein unerwarteter Fehler aufgetreten."}}, "nameInput": {"label": "Name / Pseudonym", "firstNameLabel": "<PERSON><PERSON><PERSON>", "lastNameLabel": "Nachname", "errorMessage_nameMissing": "Name erforderlich", "errorMessage_nameInvalid": "Name zu kurz"}, "emailInput": {"label": "Email", "errorMessage_unreachableEmailAddress": "<PERSON>ail Ad<PERSON> ist ungültig", "errorMessage_duplicateEmail": "Account mit dieser Email existiert bereits", "errorMessage_wrongEmailOrId": "Kein Account mit dieser Email registriert", "errorMessage_emailInvalid": "Muss eine gültige Email Adresse sein", "errorMessage_emailMissing": "<PERSON><PERSON>"}, "telephoneInput": {"label": "Telefon", "errorMessage_telephoneMissing": "Telefon erforderlich", "errorMessage_telephoneInvalid": "Telefon ungültig"}, "birthDateInput": {"label": "Geburtstag", "formatValue": "{{date, formatDateTime dd.MM.yyyy}}", "errorMessage_birthDateMissing": "Geburtsta<PERSON>", "errorMessage_tooYoung": "Du musst mindestens 12 Jahre alt sein"}, "genderInput": {"label": "Geschlecht", "errorMessage": "<PERSON><PERSON><PERSON>", "options": [{"value": "DIVERSE", "label": "Divers"}, {"value": "MALE", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"value": "FEMALE", "label": "<PERSON><PERSON><PERSON>"}], "errorMessage_genderMissing": "Geschlecht erforderlich"}, "passwordInput": {"label": "Passwort", "errorMessage_passwordMissing": "Passwort er<PERSON>", "errorMessage_passwordInvalid": "Passwort entspricht nicht den Vorgaben: mindestens 10 Zeichen, mindestens ein Großbuchstabe, ein <PERSON>buchstabe, eine Zahl und ein Sonderzeichen", "errorMessage_wrongPassword": "Falsches Passwort"}, "inviteCodeInput": {"description": "<PERSON><PERSON><PERSON> <PERSON> von einem Freund eingeladen?", "label": "Empfehlungscode", "errorMessage_wrongInviteCode": "Ungültiger Code"}, "addressInput": {"streetLabel": "Straße", "errorMessage_streetMissing": "Stra<PERSON> er<PERSON>lich", "errorMessage_streetInvalid": "Stra<PERSON> zu kurz", "houseNumberLabel": "Nr.", "errorMessage_houseNumberMissing": "<PERSON><PERSON> <PERSON><PERSON>", "zipCodeLabel": "<PERSON><PERSON><PERSON><PERSON>", "errorMessage_zipCodeMissing": "<PERSON><PERSON><PERSON><PERSON>", "errorMessage_zipCodeInvalid": "Post<PERSON>itz<PERSON> zu kurz", "cityLabel": "Ort", "errorMessage_cityMissing": "<PERSON><PERSON>", "errorMessage_cityInvalid": "<PERSON><PERSON> zu k<PERSON>z", "countryLabel": "Land", "errorMessage_countryMissing": "Land erforderlich", "countryOptions": [{"value": "AL", "label": "Albanien"}, {"value": "AD", "label": "Andorra"}, {"value": "AM", "label": "Armenien"}, {"value": "AT", "label": "Österreich"}, {"value": "AZ", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "BY", "label": "Weißrussland"}, {"value": "BE", "label": "Belgien"}, {"value": "BA", "label": "Bosnien und Herzegowina"}, {"value": "BG", "label": "Bulgarien"}, {"value": "HR", "label": "<PERSON><PERSON><PERSON>"}, {"value": "CY", "label": "<PERSON><PERSON><PERSON>"}, {"value": "CZ", "label": "Tschechien"}, {"value": "DK", "label": "Dänemark"}, {"value": "EE", "label": "Estland"}, {"value": "FI", "label": "Finnland"}, {"value": "FR", "label": "<PERSON><PERSON><PERSON>"}, {"value": "GE", "label": "<PERSON><PERSON>"}, {"value": "DE", "label": "Deutschland"}, {"value": "GR", "label": "Griechenland"}, {"value": "HU", "label": "<PERSON><PERSON><PERSON>"}, {"value": "IS", "label": "Island"}, {"value": "IE", "label": "Irland"}, {"value": "IT", "label": "Italien"}, {"value": "KZ", "label": "Kasachstan"}, {"value": "XK", "label": "Kosovo"}, {"value": "LV", "label": "Lettland"}, {"value": "LI", "label": "Liechtenstein"}, {"value": "LT", "label": "Li<PERSON>uen"}, {"value": "LU", "label": "Luxemburg"}, {"value": "MK", "label": "Nordmazedonien"}, {"value": "MT", "label": "Malta"}, {"value": "MD", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"value": "MC", "label": "Monaco"}, {"value": "ME", "label": "Montenegro"}, {"value": "NL", "label": "Niederlande"}, {"value": "NO", "label": "Norwegen"}, {"value": "PL", "label": "<PERSON><PERSON>"}, {"value": "PT", "label": "Portugal"}, {"value": "RO", "label": "Rumänien"}, {"value": "RU", "label": "Russland"}, {"value": "SM", "label": "San Marino"}, {"value": "RS", "label": "<PERSON><PERSON>"}, {"value": "SK", "label": "Slowakei"}, {"value": "SI", "label": "Slowenien"}, {"value": "ES", "label": "Spanien"}, {"value": "SE", "label": "Schweden"}, {"value": "CH", "label": "Schweiz"}, {"value": "TR", "label": "Türkei"}, {"value": "UA", "label": "Ukraine"}, {"value": "GB", "label": "Großbritannien"}, {"value": "VA", "label": "Vatikanstadt"}]}, "skipRegistrationButtonLabel": "Später registrieren", "resetPassword": {"buttonLabel": "Passwort vergessen?", "confirmationDialog": {"title": "Passwort zurücksetzen", "description": "Um dein Passwort zurückzusetzen, schicken wir dir eine E-Mail mit einem Bestätigungscode zu.", "dismissButtonLabel": "Abbrechen", "resetPasswordButtonLabel": "Z<PERSON>ücksetzen"}, "errorDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "title_wrongEmailOrId": "Unbekannt", "title_networkError": "Offline", "description": "Es ist ein Fehler bei der Buchung aufgetreten. Bitte warte einen Moment und schaue in deinen Buchungen nach, ob die Tickets inzwischen angezeigt werden.", "description_wrongEmailOrId": "Wir haben leider keinen Account mit der Email Adresse ‘{{email}}’ finden können.", "description_networkError": "<PERSON> scheinst offline zu sein, bitte versuche es erneut.", "dismissButtonLabel": "Abbrechen", "retryButtonLabel": "Noch<PERSON> versuchen", "contactSupportButtonLabel": "Support Kontaktieren"}}, "toggleAlreadyRegisteredButtonLabel": "Schon registriert? <1>Jetzt anmelden</1>", "toggleAlreadyRegisteredButtonLabel_alreadyRegistered": "Noch kein Account? <1>Jetzt registrieren</1>", "submitButtonLabel": "Registrieren", "submitButtonLabel_alreadyRegistered": "Anmelden", "loginFromRegistrationMessge": "Du wurdest in deinen existierenden Account angemeldet", "loginErrorDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ein unerwarteter <PERSON>hler {{error}} ist aufgetreten. Bitte kontaktiere unseren Support.", "title_userDoesNotExist": "<PERSON><PERSON> Account gefunden", "description_userDoesNotExist": "Du hast noch keinen Account registriert, bitte gehe zurück und beende zunächst das Onboarding.", "title_externalAuthFailed": "Fehler bei Anmeldung", "description_externalAuthFailed": "Du hast entweder eine falsche Email-Passwort-Kombination angegeben oder deine Email noch nicht bestätigt.", "description_socialAuthFailed": "Die Anmeldung mit {{provider}} ist fehlgeschlagen, bitte versuche es erneut oder kontaktiere unseren Support.", "title_socialAuthFailed": "Anmeldung mit {{provider}} fehlgeschlagen", "title_userInvalidOrBlocked": "Account gesperrt", "description_userInvalidOrBlocked": "<PERSON><PERSON>unt wurde gesperrt. Bitte kontaktiere unseren Support.", "title_networkError": "Offline", "description_networkError": "<PERSON> scheinst offline zu sein, bitte versuche es erneut."}, "resendActivationEmail": {"successMessage": "Bestätigungsemail erneut versandt.", "errorDialog": {"title": "<PERSON><PERSON>", "description": "Ein unerwart<PERSON> ‘{{error}}’ ist aufgetreten.", "description_invalidRequest": "Irgendetwas mit deinem Account scheint nicht zu stimmen, bitte kontaktiere unseren Support.", "description_emailAlreadyConfirmed": "<PERSON><PERSON> ist bereits bestätigt.", "description_networkError": "<PERSON> sche<PERSON>t offline zu sein.", "dismissButtonLabel": "Abbrechen", "retryButtonLabel": "Noch<PERSON> versuchen", "reauthenticateButtonLabel": "<PERSON>ieder einloggen", "contactSupportButtonLabel": "Support kontaktieren"}}, "codeInput": {"errorMessage_codeMissing": "Bestätingungscode erforderlich", "errorMessage_codeInvalid": "Bestätingungscode ungültig"}, "confirmEmail": {"title": "<PERSON><PERSON>", "description": "Wir haben eine E-Mail mit einem Bestätigungscode an ‘{{email}}’ gesandt. Unsere E-Mail könnte in deinem Spam-Ordner gelandet sein.", "verifyEmailButtonLabel": "<PERSON><PERSON>", "resendActivationEmailButtonLabel": "Email erneut zusenden", "correctEmailButonLabel": "<PERSON><PERSON>", "codeMissingMessage": "Du hast keinen Bestätigungscode angegeben", "errorDialog": {"title": "Bestätigung gescheitert", "title_networkError": "Offline", "description_networkError": "<PERSON> scheinst offline zu sein, bitte versuche es erneut.", "description_wrongEmailOrId": "Ein Nutzer mit dieser Email existiert nicht, bitte melde dich erneut an", "description_emailAlreadyConfirmed": "Du hast deine Email bereits bestätig, bitte melde dich erneut an", "description": "Ein unerwarteter Fehler ist aufgetreten", "logBackInButtonLabel": "<PERSON>eu an<PERSON>den"}, "successMessage": "<PERSON>ail erfo<PERSON>greich bestätigt"}, "updatePassword": {"title": "Passwort ändern", "description": "Bestätige die Änderung mit dem Code, den wir dir per E-Mail zugeschickt haben.", "newPasswordInnputLabel": "Neues Passwort", "updatePasswordButtonLabel": "Passwort ändern", "resendResetPasswordEmailButtonLabel": "Email erneut zusenden", "codeMissingMessage": "Du hast keinen Bestätigungscode angegeben", "errorDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "title_networkError": "Offline", "description": "Ein unerwart<PERSON> ‘{{error}}’ ist aufgetreten. Bitte kontaktiere unseren Support.", "description_networkError": "<PERSON> scheinst offline zu sein, bitte versuche es erneut.", "dismissButtonLabel": "Abbrechen", "contactSupportButtonLabel": "Support Kontaktieren"}}, "completeSocialProfileDialog": {"title": "Zusätzliche Angaben Erforderlich", "description": "Um deinen Account an<PERSON><PERSON>en, ben<PERSON><PERSON><PERSON> wir zusätzlich folgende Angaben.", "dismissButtonLabel": "Abbrechen", "resubmitButtonLabel": "Registrieren"}, "loggedOutErrorDialog": {"title": "Autorisierungs-Fehler", "description": "Ein unbekannter Fehler ‘{{error}}’ ist aufgetreten.", "description_wrongPassword": "Du hast dein Passwort geändert.", "description_outdatedRefreshToken": "<PERSON>ine <PERSON>du<PERSON> ist abgelaufen.", "description_invalidRefreshToken": "<PERSON><PERSON> ist ungültig.", "description_facebookAuthFailed": "Die Anmeldung mit Facebook ist fehlgeschlagen.", "description_appleAuthFailed": "Die Anmeldung mit Apple ist fehlgeschlagen.", "description_googleAuthFailed": "Die Anmeldung mit Google ist fehlgeschlagen.", "description_userEmailUnconfirmed": "<PERSON> hast vergessen, deine <PERSON><PERSON> zu verifizieren, daher wurde dein Account gesperrt. Wir können dir die Bestätigungsemail erneut zusenden.", "description_userInvalidOrBlocked": "Dein Account wurde g<PERSON>, bitte kontaktiere unseren Support für weitere Informationen.", "dismissButtonLabel": "Neu einloggen", "contactSupportButtonLabel": "Support kontaktieren"}, "title_joinCinfinity": "CINFINITY beitreten", "alreadyLoggedInError": {"title": "Gerätewechsel", "description": "Du bist bereits auf einem anderen Gerät eingeloggt. Dein CINFINITY-Account kann immer nur auf einem Gerät genutzt werden. <PERSON>te bestätige, dass du deinen Account nun auf diesem Gerät nutzen möchtest. Ein weiterer Gerätewechsel, auch zurück zu einem bisher genutzten Gerät, ist anschließend erst in 90 Tagen möglich.", "continueButtonLabel": "<PERSON><PERSON>", "cancelButtonLabel": "Abbrechen"}, "appChangeBlockedError": {"title": "App Wechsel zur Zeit nicht möglich", "description": "Dein CINFINITY-Account kann immer nur auf einem Gerät genutzt werden. Der Gerätewechsel ist nur ein Mal alle 90 Tage möglich. Du hast dein Gerät erst vor kurzem gewechselt, daher kannst du dich nun auf diesem Gerät nicht einloggen. Wen<PERSON> du glau<PERSON>, dass dies ein <PERSON>hler ist, schreibe bitte an unseren Support."}, "termsAgreement": {"checkBoxLabel": "Ich akzeptiere die [Allgemeinen Geschäftsbedingungen](https://cinfinity.de/agb) und nehme die [Nutzungsbedingungen]($t(app:Global.termsLink)) und [Datenschutzerklärung]($t(app:Global.privacyLink)) zur Kenntnis.", "agreementNecessaryDialog": {"description": "Bitte tippe die rot markierte Checkbox an, um zu bestätigen, dass du die Allgemeinen Geschäftsbedingungen akzeptierst und die Nutzungsbedingungen und Datenschutzerklärung zur Kenntnis genommen hast."}}}, "EditProfileView": {"title": "<PERSON><PERSON>", "updatePasswordButtonLabel": "Passwort ändern", "submitButtonLabel": "Speichern", "updateEmailSuccessMessage": "Email erfolgreich geändert", "updateSuccessMessage": "Profil wurde g<PERSON>", "updateFailureMessage": "Profil konnte nicht gespeichert werden"}, "EditProfilePictureView": {"title": "<PERSON>il<PERSON><PERSON> bearbeiten", "explanation": {"prefix": "Hier kannst Du ein Profilbild hinzufügen oder ändern. Das Profilbild wird zur Kontrolle am Einlass benötigt, achte daher bitte darauf, dass Du gut zu erkennen bist. Das Profilbild ist nur einmalig alle ", "bold": "90 Tage", "suffix": " änderbar."}, "buttonLabel": "Profilbild ändern", "changeError": {"title": "Profilbild konnte nicht geändert werden", "description": "Das Foto ist nur alle 90 Tage änderbar. Du hast dein Bild am {{date}} geändert."}}, "TakeOrChoosePictureView": {"title": "<PERSON>il<PERSON><PERSON> bearbeiten", "takePictureButtonLabel": "Foto aufnehmen", "chooseFromGalleryButtonLabel": "Aus Galerie auswählen", "confirmPictureButtonLabel": "Foto verwenden", "rejectPictureButtonLabel": "Abbrechen", "changePictureInfo": "Achtung: Das Foto kann nur alle 90 Tage geändert werden. <PERSON><PERSON> stelle sicher, dass du auf dem Foto gut zu erkennen bist.", "cameraNotAuthorizedMessage": "Die App hat keinen Zugriff auf die Kamera. Bitte erlaube den Zugriff in den Einstellungen.", "cameraNotAuthorizedButtonLabel": "App Einstellungen", "cameraError": {"title": "<PERSON><PERSON>", "description": "Die Kamera konnte kein Foto aufnehmen."}}, "NoCinemaSelectedView": {"description": "Du hast kein Kino ausgewählt", "selectCinemaButtonLabel": "<PERSON>no auswählen"}, "MarkSeenMoviesView": {"formatDate": "{{date, formatDateTime MMMM yyyy}}", "title": "Filme auswählen", "description": "Welche Filme hast du im Kino g<PERSON>hen?", "submitButtonLabel": "<PERSON><PERSON>", "noMoviesSelectedDialog": {"title": "Keine Filme markiert", "description": "Hast du im letzten Jahr keine Filme im Kino g<PERSON>hen?", "confirmButtonLabel": "<PERSON>ine Filme g<PERSON>hen", "abortButtonLabel": "<PERSON><PERSON>"}, "errorDialog": {"title": "<PERSON><PERSON>", "description_networkError": "<PERSON> scheinst offline zu sein, bitte versuche es erneut."}, "emptyDisclaimer": "Im letzten Jah<PERSON> scheinen in deinen Kinos keine Filme gelaufen zu sein."}, "RateSeenMoviesView": {"title": "<PERSON>e bewerten", "description": "Bitte bewerte deine gesehenen Filme.", "submitButtonLabel": "<PERSON><PERSON><PERSON>", "errorDialog": {"title": "<PERSON><PERSON>", "description_networkError": "<PERSON> scheinst offline zu sein, bitte versuche es erneut."}}, "ForYouView": {"title": "<PERSON><PERSON><PERSON>", "watchlistInCinemaSection": {"title": "Deine Filme im Kino", "subTitle": "Diese Filme von deiner Merkliste laufen jetzt im Kino!", "emptyMessage": "**Keiner der Filme von deiner Merkliste läuft gerade im Kino!** \n\n Unter den trendigen Filmen findest du sicher ein paar Filme, die dich interessieren könnten."}, "watchlistLastChanceSection": {"title": "Nur noch diese Woche", "subTitle": "Verpasse diese Filme von deiner Merkliste nicht im Kino!", "emptyMessage": ""}, "trendingSection": {"title": "Trending", "subTitle": "Diese Filme wollen die meisten Kinogänger sehen.", "emptyMessage": ""}, "featuredMovieLabel": "Film der Woche", "initialRateMoviesSection": {"title": "Möchtest du persönliche Filmempfehlungen erhalten?", "description": "Dann sag uns einfach welche Filme dir gefallen haben.", "selectButttonLabel": "Auswählen"}, "exitPollSection": {"title": "Wie hat dir der Film gefallen?", "description": "<PERSON><PERSON> interessiert uns!", "rateMovieButtonLabel": "Jetzt bewerten"}}, "DiscoverView": {"searchLabel": "<PERSON><PERSON>", "specialsSection": {"title": "Specials"}, "filmSeriesSection": {"title": "Filmreihen {{cinemaName}}", "numberOfScreeningsLabel": "{{count}} Spielzeit verfügbar", "numberOfScreeningsLabel_plural": "{{count}} Spielzeiten verfügbar"}, "soonInCinemaSection": {"title": "Bald in deinen Kinos", "title_femaleCinemaName": "Bald in der {{cinemaName}}"}, "lastChanceSection": {"title": "Letz<PERSON> Chance", "description": "Nur noch diese Woche in deinen Kinos!"}, "nowInCinemaSection": {"title": "Jetzt in deinen Kinos", "title_femaleCinemaName": "Jetzt in der {{cinemaName}}"}, "eventsAndSpecials": {"title": "Specials im {{cinemaName}}", "title_femaleCinemaName": "Specials in der {{cinemaName}}", "dateLabel": "{{datetime, formatDateTime dd.MM.}}"}, "newsSection": {"title": "Neuigkeiten"}, "watchlistInCinemaSection": {"title": "Deine Filme im Kino", "emptyMessage": "Du hast keinen aktuellen Film auf deiner Merkliste, klicke zum Hinzufügen auf das Merklistensymbol in der oberen Ecke eines Filmposters."}, "notificationInbox": {"preTitle": "zuletzt {{lastReceived, formatDistanceToNow}}", "title": "Neuigkeiten"}, "appFeedbackCard": {"title": "<PERSON><PERSON><PERSON><PERSON>t dir die App?", "likeDialog": {"title": "Hast du einen Moment, um uns zu bewerten?", "dismissButtonLabel": "<PERSON><PERSON>t nicht", "rateAppButtonLabel": "<PERSON>a klar"}, "dislikeDialog": {"title": "Was können wir verbessern?", "description": "Hast du einen Moment, um uns zu beschreiben, was dir in der App fehlt?", "dismissButtonLabel": "<PERSON><PERSON>t nicht", "giveFeedbackButtonLabel": "<PERSON>a klar"}}}, "NotificationListView": {"itemPreTitle": "{{date, formatDistanceToNow}}"}, "SearchView": {"title": "<PERSON><PERSON>", "movieSearchInputLabel": "Film Titel", "searchInfoDialog": "G<PERSON> einen Film Titel im Suchfeld oben ein", "searchFailedDialog": "Wir konnten keinen Film mit diesem Titel finden", "movieScreeningStatus": {"soon": "Bald im Kino", "current": "Jetzt im Kino"}, "movieTitle": "{{title}} ({{releaseDate, formatDateTime yyyy}})", "movieTitle_noDate": "{{title}}"}, "MovieDetailView": {"title": "{{movieTitle}}", "specialBannerLabel": "Special", "weekNumberLabel": "{{week}}. <PERSON><PERSON><PERSON>", "durationLabel": "{{duration}} Min", "releaseDateLabel_past": "jetzt im Kino", "releaseDateLabel_today": "ab heute im Kino", "releaseDateLabel_tomorrow": "ab morgen im Kino", "releaseDateLabel_thisWeek": "ab {{date, formatDateTime EEEE}} im Kino", "releaseDateLabel": "{{date, formatDistanceToNow}} im Kino", "player": {"feedbackLayover": {"title": "<PERSON>öchtest du diesen Film sehen?", "markNotInterestedButtonLabel": "<PERSON><PERSON>", "addToWatchlistButtonLabel": "<PERSON>a"}}, "screenings": {"openMovieListButtonLabel": "In der Reihe “{{movieListTitle}}”", "timeLabel": "{{datetime, formatDateTime HH:mm}}", "dateLabel": "{{datetime, formatDateTime d}}", "weekdayLabel": "{{datetime, formatDateTime EEEEEE}}", "monthLabel": "{{datetime, formatDateTime MMM}}", "yearLabel": "{{datetime, formatDateTime yyyy}}"}, "rating": {"addToWatchlistButtonLabel": "zur Merkliste hinzufügen", "addToWatchlistButtonLabel_active": "auf deiner Merkliste", "starRatingTitle": "Wie hat dir der Film Gefallen?", "starRatingLabels": ["<PERSON><PERSON>", "Ok", "Top"]}, "ageRatings": {"FSK_0": "FSK 0", "FSK_6": "FSK 6", "FSK_12": "FSK 12", "FSK_16": "FSK 16", "FSK_18": "FSK 18", "FSK_NONE": null}, "genres": {"ACTION": "Action", "ABENTEUER": "<PERSON><PERSON><PERSON>", "ANIMATION": "Animation", "KOMOEDIE": "Komödie", "KRIMI": "<PERSON><PERSON><PERSON>", "DOKUMENTARFILM": "Do<PERSON>", "DRAMA": "Drama", "FAMILIE": "Kinderfilm", "FANTASY": "Fantasy", "HISTORIE": "Historie", "HORRROR": "Horror", "MUSIK": "Mu<PERSON>", "MYSTERY": "Mystery", "LIEBESFILM": "Liebesfilm", "SCIENCE_FICTION": "SciFi", "TV_FILM": "TV Film", "THRILLER": "Thriller", "KRIEGSFILM": "Kriegsfilm", "WESTERN": "Western", "KONZERT": "<PERSON><PERSON><PERSON>", "KUNST_AUSSTELLUNG": "<PERSON><PERSON>", "MUSICAL": "Musical", "OPER": "<PERSON><PERSON>", "KLASSIK_KONZERT": "<PERSON><PERSON><PERSON>", "ROCK_POP_KONZERT": "<PERSON><PERSON><PERSON>", "SPECIAL_EVENT": null, "TANZ": "<PERSON><PERSON>", "THEATER": "Theater", "FILM_FESTIVAL": null, "BIOGRAPHIE": "Biographie", "SPORT": "Sport", "LITERATURVERFILMUNG": "Literaturverfilmung", "KOSTUEMFILM": "Kostümfilm", "TEENAGE": "Teenage", "EASTERN": "Eastern", "PORNO": "Porno", "FAMILIENGESCHICHTE": "Familiengeschichte", "HEIMATFILM": "Heimatfilm", "SOZIALDRAMA": "Sozialdrama", "AMOUR_FOU": "<PERSON><PERSON>", "POLIT_THRILLER": "Polit Thriller", "KUNSTFILM": "Kunstfilm", "MELODRAM": "<PERSON><PERSON><PERSON>", "TRAGIK_KOMOEDIE": "Tragikomödie", "KULTFILM": "Kultfilm", "SKURRIL": "<PERSON><PERSON><PERSON><PERSON>", "LIFESTYLE": "Lifestyle", "PSYCHO_THRILLER": "<PERSON><PERSON><PERSON>er", "PERSIFLAGE": "Persiflage", "TRAGOEDIE": "Tragödie", "EROTIK": "Erotik", "STUMMFILM": "Stummfilm", "FERNSEHEN_IM_KINO": "Fernseh<PERSON> I<PERSON>", "SCHICKSALE": "Schicksale", "ALLTAG": "Alltag", "PROBLEMFILM": "Problemfilm", "GESCHICHTSVERFILMUNG": "Geschichtsverfilmung", "PUPPENFILM": "Puppenfilm", "EPISODENFILM": "Episodenfilm", "REMAKE": "Remake", "EPOS": "<PERSON><PERSON><PERSON>", "ROADMOVIE": "Roadmovie", "SCHWARZE_KOMOEDIE": "Schwarze Komödie", "KURZFILM": "Kurzfilm", "KATASTROPHENFILM": "Katastrophenfilm", "BUEHNENVERFILMUNG": "Bühnenverfilmung", "TIERFILM": "Tierfilm", "ITALOWESTERN": "Italowestern", "MAERCHENFILM": "Märchenfilm", "MONUMENTALFILM": "Monumentalfilm", "AUFKLAERUNGSFILM": "Aufklärungsfilm", "AVANTGARDE": "<PERSON><PERSON><PERSON><PERSON>", "NATURFILM": "Naturfilm", "GOTHIC": "Gothic", "FILM_NOIR": "Film Noir", "EXPERIMENTALFILM": "Experimentalfilm", "SATIRE": "Satire", "SPORTFILM": "Sportfilm", "BALLETT": "<PERSON><PERSON>", "LIVE": "Live", "TALK": "Talk", "REISEFILM": "Reisefilm", "MOCKUMENTARY": "Mockumentary", "MULTIMEDIASHOW": "Multimediashow", "COMING_OF_AGE": "Coming Of Age", "REISE_NATUR": "<PERSON><PERSON>"}}, "WebBookingViewMars": {"finishedTitle_success": "Buchung erfolgreich", "finishedTitle_error": "Buchung erfolgreich", "finishedDescription_success": "Du kannst die Buchung nun verlassen, die Tickets erscheinen gleich in der App. Zusätzlich erhältst Du diese per E-Mail. Deine Bonuspunkte werden dir nach Vorstellungsbeginn gutgeschrieben.", "finishedDescription_error": "Du kannst die Buchung nun verlassen, die Tickets erhältst Du per E-Mail. Leider ist ein Fehler bei den Bonuspunkten aufgetreten. Bitte wende dich an unseren Support."}, "BookingView": {"title": "{{movieTitle}}", "subTitle": "{{datetime, formatDateTime EEEEEE. dd.MM. HH:mm 'Uhr'}}", "changedSeatSelectionMessage": "<PERSON><PERSON> musste angepasst werden", "areaSelectionInfo": "<PERSON><PERSON><PERSON>e einen Bereich aus der Liste unten", "legend": {"standardSeat": "Sitz", "wheelChairSeat": "Rollstuhlplatz", "loveSeat": "<PERSON><PERSON><PERSON><PERSON>", "occupied": "Besetzt", "selected": "Ausgewählt"}, "priceSelection": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": "Wähle deine Sitzplätze", "notSelectedLabel": "<PERSON>cht zugewiesen", "priceLabel": "{{name}}: {{amount, formatCurrency}}", "totalPriceLabel": "Gesamtpreis:", "totalPriceAmount": "{{amount, formatCurrency}}", "disclaimer": "Eventuell werden für einige Besuchergruppen Vergünstigungen angeboten. An der Kinokasse müssen dafür entsprechende Nachweise vorgelegt werden.", "missingPriceCategory": "<PERSON>te wähle einen Tarif aus", "submitButtonLabel": "<PERSON><PERSON>", "noSeatsSelected": "Bitte wähle mindestens einen Sitzplatz aus"}, "accountRequiredDialog": {"title": "Du brauchst einen Account um Tickets zu kaufen", "registerButtonLabel": "Jetzt registrieren", "cancelButtonLabel": "Abbrechen"}, "onlineTicketingDisclaimerDialog": {"description": "Du wirst nun zum Online-Ticketing weitergeleitet.", "description_cannotCollectPoints": "Du wirst nun zum Online-Ticketing weitergeleitet.\n\nLeider ist es noch nicht möglich, automatisch Bonuspunkte für den Onlinekauf zu vergeben. Du kannst aber deine Bestätigungsemail mit Angabe deiner $t(app:Global.appName) ID Nummer an [<EMAIL>](mailto:<EMAIL>) weiterleiten, so können wir deine Punkte nachträglich gutschreiben. Deine $t(app:Global.appName) ID wird unter dem QR Code angezeigt, wenn du die Bonuskarte in der App antippst.", "description_noOnlineTicketing": "<PERSON><PERSON><PERSON> diese Vorstellung können online keine Tickets gekauft werden.", "description_telephoneReservation": "<PERSON><PERSON><PERSON> diese Vorstellung können online keine Tickets gekauft werden. Du kannst aber telefonisch Tickets reservieren unter {{tel}}.", "openButtonLabel": "<PERSON><PERSON>", "dismissButtonLabel": "Ok", "dismissButtonLabel_telephoneReservation": "Abbrechen", "callReservationHotlineButtonLabel": "Anrufen"}, "calendarEvent": {"title": "Kino: {{movieTitle}}"}, "reserveBonuspointsSuccessMessage": "Bonuspunkte vorgemerkt! Werden nach der Vorstellung gutgeschrieben.", "reserveBonuspointsErrorDialog": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> diesen Ticketkauf konnten leider keine Bonuspunkte vorgemerkt werden.", "dismissButtonLabel": "Ok"}, "timeout": {"title": "Timeout", "description": "Deine Zeit für die Sitzplatzwahl ist abgelaufen. Bitte versuche es erneut.", "dismissButtonLabel": "Ok"}, "subscriptionWarningText_userHasBlockedSubscription": "Du kannst aus folgenden Gründen kein Abo-Ticket für diese Vorstellung kaufen: Deine Mitgliedschaft wurde gesperrt.", "subscriptionWarningText_userHasNoSubscription": "Du kannst aus folgenden Gründen kein Abo-Ticket für diese Vorstellung kaufen: Du hast keine aktive Mitgliedschaft. Bitte schließe eine Mitgliedschaft ab, um Tickets zu buchen.", "subscriptionWarningText_numberOfSubscriptionTicketsPerMonthReached": "Du kannst aus folgenden Gründen kein Abo-Ticket für diese Vorstellung kaufen: Du hast bereits die maximale Anzahl an Tickets für diesen Monat gebucht.", "subscriptionWarningText_userHasTooManyOpenSubscriptionTickets": "Du kannst aus folgenden Gründen kein Abo-Ticket für diese Vorstellung kaufen: Du hast bereits zu viele offene Tickets.", "subscriptionWarningText_userAlreadyHasTicketForScreening": "Du kannst aus folgenden Gründen kein Abo-Ticket für diese Vorstellung kaufen: Du hast bereits ein Ticket für diese Vorstellung.", "subscriptionWarningText_userAlreadyHasTicketForMovie": "Du kannst aus folgenden Gründen kein Abo-Ticket für diese Vorstellung kaufen: Du hast bereits ein Ticket für diesen Film.", "subscriptionWarningText_userHasOverlappingTicket": "Du kannst aus folgenden Gründen kein Abo-Ticket für diese Vorstellung kaufen: Du hast bereits ein Ticket für eine Vorstellung, die sich mit dieser überschneidet.", "subscriptionWarningText_ticketHasImplausibleLocation": "Du kannst aus folgenden Gründen kein Abo-Ticket für diese Vorstellung kaufen: Du hast bereits ein Ticket für eine Vorstellung in einem anderen Kino, die sich aufgrund der Entfernung zu dem anderen Kino mit dieser überschneidet.", "linkedSubscriptionsWarningText": "Du kannst für diese Vorstellung kein Ticket für {{linkedUsernames}} kaufen", "selectLinkedAccountInfo": "Hier kannst du auswählen, für welche verknüpften Accounts du Tickets mitbuchen möchtest.", "maxLinkedAccountsInfo": "Buche für bis zu {{maxLinkedAccountsNumber}} deiner Freunde Abo-Tickets", "noProfilePictureDialog": {"description": "Um ein Ticket zu kaufen benötigst du ein Profilbild.", "cancelButtonLabel": "Abbrechen", "addPictureButtonLabel": "Jetzt erstellen"}, "subscriptionBlockedDialog": {"goBackButtonLabel": "OK", "userDataButtonLabel": "Profil"}, "leaveBookingDialog": {"title": "<PERSON><PERSON><PERSON> beenden", "description": "Willst du die Buchung wirklich beenden?", "dismissButtonLabel": "<PERSON><PERSON>", "confirmButtonLabel": "<PERSON>a"}, "mainUserHasNoSubcriptionError": "Du hast aktuell kein aktives Abo", "invalidMainUserErrorTitle": "Achtung", "invalidMainUserError": "Du kannst dein Abo für diese Vorstellung nicht verwenden: {{error}}", "invalidSubscriptionError_userHasNoSubscription": "<PERSON><PERSON> g<PERSON>iges Abo", "invalidSubscriptionError_userHasBlockedSubscription": "Abo ist gesperrt", "invalidSubscriptionError_numberOfSubscriptionTicketsPerMonthReached": "Maximale Anzahl an Abo-Tickets pro Monat erreicht", "invalidSubscriptionError_userHasTooManyOpenSubscriptionTickets": "Zu viele offene Abo-Tickets", "invalidSubscriptionError_userAlreadyHasTicketForScreening": "Ticket für diese Vorstellung bereits vorhanden", "invalidSubscriptionError_userAlreadyHasTicketForMovie": "Ticket für diesen Film bereits vorhanden", "invalidSubscriptionError_userHasOverlappingTicket": "Ticket für eine Vorstellung vorhanden, die sich mit dieser überschneidet", "invalidSubscriptionError_ticketHasImplausibleLocation": "Ticket für eine Vorstellung in einem anderen Kino vorhanden, die sich mit dieser überschneidet", "bookingDisabled": {"title": "Ausverkauft", "description": "Diese Vorstellung ist leider nicht (mehr) buchbar."}, "timeoutDialog": {"title": "Zeitüberschreitung", "description": "Die Zeit für die Auswahl der Tickets ist abgelaufen."}, "errorDialog": {"title": "<PERSON><PERSON>", "description": "Es ist ein Fehler bei der Buchung aufgetreten.", "descripton_subscription": "<PERSON><PERSON> ist ein Fehler beim Anwenden des Abos aufgetreten.", "description_initialize": "Beim Initialisieren des Buchungsprozesses ist ein Fehler aufgetreten. Bitte wende dich an den Support.", "description_confirm": "<PERSON>s ist ein Fehler beim Bestätigen der Buchung aufgetreten. Bitte gedulde dich einen Moment - wir schreiben dir, sobald die Buchung abgeschlossen wurde. Bitte buche kein weiteres Ticket für den selben Film.", "description_ticketDataNotFound": "Die Buchung konnte nicht abgeschlossen werden. Wir versuchen es erneut und informieren dich dann.", "description_networkError": "Die Buchung konnte aufgrund eines Netzwerkfehlers nicht abgeschlossen werden. Sobald die Verbindung wiederhergestellt ist, versuchen wir es erneut und informieren dich dann.", "description_retryQueue": "Deine letzte Buchung konnte nicht abgeschlossen werden. Bitte wende dich an den Support.", "title_subscriptionNotAllowed": "<PERSON>bo nicht erlaubt", "description_subscriptionNotAllowed": "Du kannst für diese Vorstellung kein Abo-Ticket kaufen."}, "successDialog": {"title": "Erfolgreich", "title_retryQueue": "Buchung abgeschlossen", "description": "Die Buchung war erfolgreich. Falls sich Deine Pläne ändern, storniere bitte rechtzeitig. Dies geht in vielen Kinos nur bis 2 Stunden vor Vorstellungsbeginn.", "description_retryQueue": "Deine letzte Buchung konnte nun erfolgreich abgeschlossen werden. Schaue in deinem Profil nach, um die Tickets zu finden."}}, "BookingSummaryView": {"title": "Zusammenfassung", "selectedSeats": "Ausgewählte Plätze:", "selectedSeatsArea": "{{area}}: {{count}}", "submitButtonLabel": "Jetzt Zahlungspflichtig bestellen", "submitButtonLabel_free": "<PERSON><PERSON><PERSON> bestellen", "errorDialog": {"title": "<PERSON><PERSON>", "description": "Der Buchuungsprozess konnte nicht abgeschlossen werden: {{error}}", "buttonLabel": "Ok"}}, "ExitPollView": {"title": "Filmbewertung", "seenMovieSectionTitle": "Wie hat dir ‘{{movieTitle}}’ gefallen?", "similarMoviesSectionTitle": "Diese Filme könnten dir auch gefallen", "traileredMoviesSectionTitle": "<PERSON>e Trailer haben dir gefallen"}, "ShowtimesView": {"title": "{{cinemaName}} Spielzeiten", "activeFiltersLabel": "Aktive Filter", "noActiveFiltersDisclaimer": "<PERSON><PERSON> au<PERSON>", "noShowtimesDisclaimer": "<PERSON><PERSON>", "noShowtimesDisclaimer_activeFilters": "<PERSON><PERSON> Spielzeiten mit den aktiven Filtern", "deactivateFiltersButtonLabel": "<PERSON><PERSON>", "timeLabel": "{{datetime, formatDateTime HH:mm}}", "dateLabel": "{{datetime, formatDateTime dd.MM.}}", "pseudoDateLabel_all": "Alle", "pseudoDateLabel_later": "<PERSON><PERSON><PERSON><PERSON>", "filterDialog": {"title": "Spielzeiten-Filter", "cinemaSectionLabel": "<PERSON><PERSON>", "dayTimeSectionLabel": "Tageszeit", "dayTimeSectionFilters": [{"label": "vor 18 Uhr", "from": "04:00:00", "to": "18:00:00"}, {"label": "18 – 22 <PERSON><PERSON>", "from": "18:00:00", "to": "22:00:00"}, {"label": "nach 22 Uhr", "from": "22:00:00", "to": "04:00:00"}], "screeningAttributes": {"2D": {"type": "SCREENING", "sectionTitle": "Vorstellungsart", "label": "2D"}, "3D": {"type": "SCREENING", "sectionTitle": "Vorstellungsart", "label": "3D"}, "OV": {"type": "SCREENING", "sectionTitle": "Vorstellungsart", "label": "OV"}, "OMU": {"type": "SCREENING", "sectionTitle": "Vorstellungsart", "label": "OmU"}, "SPECIAL": {"type": "MOVIE", "sectionTitle": "Vorstellungsart", "label": "Special"}, "4DX": {"type": "SCREENING", "sectionTitle": "Version", "label": "4DX", "description": "I<PERSON><PERSON><PERSON>"}, "PAUSE": {"type": "SCREENING", "sectionTitle": "Eigenschaften", "iconName": "pauseCinuru", "description": "<PERSON>"}, "OVERLENGTH": {"type": "SCREENING", "sectionTitle": "Eigenschaften", "iconName": "overlengthCinuru", "description": "Überlänge"}, "DOLBY_ATMOS": {"type": "SCREENING", "sectionTitle": "Eigenschaften", "iconName": "dolbyAtmosCinuru", "description": "Dolby® Atmos"}, "DBOX_SEATS": {"type": "SCREENING", "sectionTitle": "Eigenschaften", "label": "D-Box", "description": "D-Box Motion Seats"}, "4K": {"type": "SCREENING", "sectionTitle": "Eigenschaften", "label": "4K"}, "71_SOUND": {"type": "SCREENING", "sectionTitle": "Eigenschaften", "iconName": "sevenOneSoundCinuru", "description": "7.1 Surround Sound"}, "WHEELCHAIR_SEAT": {"type": "AUDITORIUM", "sectionTitle": "Eigenschaften", "iconName": "wheelchairCinuru", "description": "Saal ist barrierefrei zugänglich"}, "ANALOG": {"type": "SCREENING", "sectionTitle": "Eigenschaften", "iconName": "analogProjectionCinuru", "description": "Analoge Projektion"}, "HFR": {"type": "SCREENING", "sectionTitle": "Eigenschaften", "label": "HFR", "description": "High Frame Rate Projektion"}, "SING_ALONG": {"type": "SCREENING", "sectionTitle": "Eigenschaften", "iconName": "singAlognCinuru", "description": "zum Mitsingen"}, "IMAX": {"type": "SCREENING", "sectionTitle": "Version", "label": "IMAX"}, "GRETA": {"type": "MOVIE", "sectionTitle": "Eigenschaften", "label": "Greta"}, "FBW_WERTVOLL": {"type": "MOVIE", "sectionTitle": "Eigenschaften", "imageUrl": "https://static.cinuru.com/public/labels/fbw_w.png", "description": "Prädikat Wertvoll"}, "FBW_BESONDERS_WERTVOLL": {"type": "MOVIE", "sectionTitle": "Eigenschaften", "imageUrl": "https://static.cinuru.com/public/labels/fbw_bw.png", "description": "Prädikat Besonders Wertvoll"}, "NEW": {"type": "MOVIE", "sectionTitle": "Eigenschaften", "label": "Neu im Kino"}, "LAST_CHANCE": {"type": "MOVIE", "sectionTitle": "Eigenschaften", "label": "Letz<PERSON> Chance"}, "GILDE": {"type": "MOVIE", "sectionTitle": "Eigenschaften", "label": "Gilde"}}, "resetFiltersButtonLabel": "<PERSON><PERSON>", "selectedCinemaSectionLabel": "<PERSON><PERSON>"}}, "InCinemaView": {"title": "<PERSON><PERSON>", "selectCinemasButtonLabel": "<PERSON><PERSON>"}, "BonusProgramView": {"perks": [], "achievements": [], "cinemaSection": {"title": "<PERSON><PERSON>", "title_plural": "Über die Kinos"}, "noAccountSection": {"title": "Du brauchst einen Account, um ein Abo abzuschließen.", "createAccountButtonLabel": "Account anlegen"}, "accountBlockedSection": {"title": "Dein Account wurde gesperrt", "contactSupportButtonLabel": "Support kontaktieren"}, "termsAgreementSection": {"title": "Teilnahmebedingungen", "description": "Mit der Teilnahme am Bonusprogramm akzeptierst du dessen [Teilnahmebedingungen]({{termsLink}})", "description_profile": "Um am Bonusprogramm teilzunehmen, musst du dessen [Teilnahmebedingungen]({{termsLink}}) zustimmen.", "description_update": "Die [Teilnahmebedingungen]({{termsLink}}) des Bonusprogramms wurden geändert. Mit der weiteren Teilnahme akzeptierst du diese.", "acceptTermsButtonLabel": "Einverstanden"}, "bonusCardSection": {"title": "Bonuskarte", "title_stampCard": "Stempelkarte", "cardLabel_stampCard": "8 Besuche = 2 Tickets zum halben Preis", "cardLabel_stampCardFull": "2 Tickets zum halben Preis", "offlineDisclaimer": "<PERSON>ine Internetverbindung. Punktestand könnte abweichen.", "offlineDisclaimer_stampCard": "<PERSON>ine <PERSON>verbindung.", "historyButtonLabel": "Bonuspunkte-<PERSON><PERSON><PERSON><PERSON>", "historyButtonLabel_stampCard": "Stempel-<PERSON><PERSON>lau<PERSON>", "qrCodeDialog": {"title": "CINFINITY-ID", "disclaimer_nightlyPoints": "Bonuspunkte werden über Nacht gutgeschrieben."}}, "provisionalBonusPointsSection": {"title": "Du warst {{datetime, formatDistanceToNow }} im Kino", "description": "Gib an welchen Film du gesehen hast und erhalte einen Bonus", "description_coins": "Gib an welchen Film du gesehen hast und sammle Bonuspunkte", "description_stamps": "Gib an welchen Film du gesehen hast und erhalte einen Stempel", "discardConfirmDialog": {"title": "Verwerfen", "description": "Möchtest du wirklich keinen Bonus für diese Buchung sammeln?", "description_coins": "Möchtest du wirklich keine Bonuspunkte für diese Buchung sammeln?", "description_stamps": "Möchtest du wirklich keine Stempel für diese Buchung sammeln?", "confirmButtonLabel": "<PERSON>a", "abortButtonLabel": "<PERSON><PERSON>"}}, "voucherSection": {"title": "Prämien", "redeemableUntilBadge": "bis {{redeemableUntil, formatDateTime dd.MM.}}", "qrCodeDialog": {"title": "Prä<PERSON><PERSON>"}}, "voucherCatalogButtonLabel": "Prämienkatalog", "couponScannerButtonLabel": "<PERSON><PERSON><PERSON><PERSON> scannen", "termsButtonLabel": "Teilnahmebedingungen", "showCinemaProductsButtonLabel": "<PERSON><PERSON> erhalten", "statusLevels": []}, "ProvisionalBonusPointBookingMovieSelectView": {"title": "Was hast du gesehen?", "description": "Gib an in welchen Film du {{datetime, formatDistanceToNow}} gegangen bist und erhalte einen Bonus", "description_stamps": "Gib an in welchen Film du {{datetime, formatDistanceToNow}} gegangen bist und erhalte einen Stempel", "description_coins": "Gib an in welchen Film du {{datetime, formatDistanceToNow}} gegangen bist und sammle Bonuspunkte", "noMovieSelectedLabel": "W<PERSON>hle einen Film", "confirmMovieSelectionButtonLabel": "Auswählen", "errorDialog": {"title": "<PERSON><PERSON>", "description": "Es ist ein unerwarteter Fehler aufgetreten.", "description_networkError": "<PERSON> scheinst offline zu sein, bitte versuche es erneut.", "description_bookingDoesNotExist": "Du Buchung existiert nicht mehr.", "description_invalidRequest": "Ungültige Anfrage."}}, "CinemaDetailView": {"title": "{{cinemaName}}", "openShowtimesButtonLabel": "Spielzeiten", "openBonusProgramButtonLabel": "Bonusprogramm", "openBonusProgramButtonLabel_stampCard": "Stempelkarte", "linkSectionTitle": "Weitere Informationen", "currentInformationSectionTitle": "Aktuelles", "specialAboutUsSectionTitle": "Über uns", "addressSectionTitle": "<PERSON><PERSON><PERSON>", "openInMapsButtonLabel": "In Maps öffnen", "pricesSectionTitle": "<PERSON><PERSON>", "openingHoursSectionTitle": "Öffnungszeiten", "accessibilitySectionTitle": "Barrierefreiheit", "sneaksSectionTitle": "Sneak Preview", "specialOffersSectionTitle": "<PERSON><PERSON><PERSON>", "giftVouchersSectionTitle": "Gutscheine", "technologiesSectionTitle": "Technologie", "historySectionTitle": "Geschichte", "imprintSectionTitle": "Impressum"}, "BonusHistoryView": {"title": "Bonuspunkte-<PERSON><PERSON><PERSON><PERSON>", "title_stampCard": "Stempel-<PERSON><PERSON>lau<PERSON>", "dateTimeLabel": "{{datetime, formatDateTime dd.MM. HH:mm 'Uhr'}}", "emptyDisclaimer": "Derzeit gibt es noch keine Einträge."}, "VoucherCatalogView": {"title": "Prämienkatalog", "buyVoucherDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> für {{voucherPrice}} Punkte kaufen?", "dismissButtonLabel": "Abbrechen", "buyVoucherButtonLabel": "<PERSON><PERSON><PERSON>"}, "insufficentCoinsToBuyVoucherDialog": {"title": "Nicht genügend Guthaben", "description": "Kostet {{voucherPrice}} Punkte."}, "voucherSoldOutDialog": {"title": "Prämie ausverkauft", "description": "Alle Prämien wurden verkauft", "description_perPerson": "<PERSON>ur einmal pro Person verfügbar", "description_perPerson_plural": "Nur {{maxVoucherQuantityPerPerson}} pro Person verfügbar"}, "voucherBuySuccessDialog": {"title": "<PERSON><PERSON>", "description": "Du hast {{voucherTitle}} erworben."}, "voucherBuyErrorDialog": {"title": "<PERSON><PERSON>", "description": "<PERSON>s ist ein unerwarteter Fehler aufgetreten. Bitte versuche es erneut.", "description_notAvailable": "Diese Prämie ist leider nicht mehr verfügbar.", "description_voucherTypeExpired": "Diese Prämie ist leider nicht mehr verfügbar.", "description_maxQuantityPerPersonReached": "Du hast bereits die maximale Anzahl an Prämien dieser Art pro Account erworben.", "description_maxQuantityReached": "Diese Prämie ist leider ausverkauft.", "description_insufficientBonusPoints": "Du hast nicht genug Bonuspunkte um diese Prämie zu kaufen."}, "cinemaProductsDialog": {"title": "Bonuspunkte erhalten", "description": "<PERSON><PERSON><PERSON> einen Ticketkauf erhälst du die jeweils angegebenen Bonuspunkte."}}, "CouponScannerView": {"title": "<PERSON><PERSON><PERSON><PERSON> scannen", "manualInputFallback": {"noPermissionsDisclaimer": "Gutscheine können nur mit Kamera-Zugriff gescannt werden.", "requestCameraPersmissionsButtonLabel": "Zugriff gewähren", "codeInput": {"label": "<PERSON><PERSON>", "hint": "Befindet sich unter dem QR Code"}}, "successDialog": {"title": "Super!", "dismissButtonLabel": "OK"}, "errorDialog": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Der Code konnte nicht eingelöst werden.", "description_alreadyRedeemed": "Du hast diesen Code bereits eingelöst. Jeder Code kann nur einmalig eingelöst werden.", "description_couponCodeNotExisting": "Dieser Code existiert nicht.", "description_invalid": "Dieser Code ist nicht mehr gültig.", "description_wrongUser": "Dieser Code ist für einen anderen Nutzer bestimmt.", "dismissButtonLabel": "OK"}}, "ProfileView": {"title": "Profil", "createAccountButtonLabel": "Account anlegen", "noRatingsDisclaimer": "<PERSON><PERSON> du Filme bewertest, \nwerden diese hier angezeigt.", "subscription": {"bannerText": "Jetzt Abo abschließen und unbegrenzt ins Kino gehen.", "noShowWarningText": "Du hast innerhalb des letzten Monats bereits {{nNoShowTickets}} Tickets gebucht und nicht eingelöst. <PERSON><PERSON> beachte, dass bei weiterem Nichterscheinen deine Mitgliedschaft gesperrt werden kann. Solltest du im Kino gewesen sein, achte bitte immer darauf, dass dein Ticket gescannt wird.", "blockedText": "Deine Mitgliedschaft wurde gesperrt, da du innerhalb des letzten Monats {{nNoShowTickets}} Tickets gebucht und nicht eingelöst hast. Deine Mitgliedschaft steht dir ab dem {{availableAgainDateString}} wieder zur verfügung. Wenn du glau<PERSON>, dass dies ein <PERSON>hler ist, wende dich bitte an den Kundenservice.", "billingSubscriptionPaymentFailed": "Deine Mitgliedschaft wurde gesperrt, da die letzte Zahlung fehlgeschlagen ist. <PERSON><PERSON> du <PERSON>, dass dies ein <PERSON>hler ist, wende dich bitte an den Kundenservice.", "paymentFailed": "Deine Mitgliedschaft wurde gesperrt, da die letzte Zahlung fehlgeschlagen ist. <PERSON><PERSON> du <PERSON>, dass dies ein <PERSON>hler ist, wende dich bitte an den Kundenservice.", "cancelDialog": {"title": "<PERSON><PERSON>", "description": "Möchtest du dein Abo wirklich kündigen?", "dismissButtonLabel": "Abbrechen", "confirmButtonLabel": "Kündigen"}, "nextPayment": "Nächste Zahlung", "canceled": "<PERSON>ek<PERSON><PERSON><PERSON> zum {{date, formatDateTime dd.MM.yyyy}}", "noPaymentsLeft": "<PERSON><PERSON>", "type": "Abo-Typ", "monthlyPrice": "Monatl<PERSON><PERSON>"}, "ratingsList": {"title": "Bewertungen", "emptyDisclaimerTitle": "Du hast noch keinen Film auf deiner Bewertungsliste", "emptyDisclaimer": "Klicke zum Hinzufügen auf einen der 5 Sterne in der Filmdetailansicht."}, "watchList": {"title": "Merkliste", "emptyDisclaimerTitle": "Du hast noch keinen Film auf deiner Merkliste", "emptyDisclaimer": "Klicke zum Hinzufügen auf das Merklistensymbol in der oberen Ecke eines Filmposters."}, "confirmLogoutDialog": {"title": "Abmelden", "description": "Möchtest du dich wirklich ausloggen?", "dismissButtonLabel": "Abbrechen", "logoutButtonLabel": "Abmelden"}, "confirmAccountDeletionDialog": {"title": "Account l<PERSON><PERSON>", "title_anonymous": "Nutzerdaten löschen", "description": "Möchtest du deinen Account mit allen deinen Nutzerdaten wirklich unwiderruflich löschen?", "description_anonymous": "Möchtest du wirklich alle deine Nutzerdaten unwiderruflich löschen?", "dismissButtonLabel": "Abbrechen", "deleteButtonLabel": "Löschen", "offlineFailureDisclaimer": "<PERSON> scheinst offline zu sein, bitte versuche es erneut."}, "accountDeletedDialog": {"title": "Account <PERSON><PERSON><PERSON>", "title_anonymous": "Nutzerdaten gelöscht", "description": "Dein Account wurde gelöscht. Klicke auf OK um die App neu zu starten.", "description_anonymous": "<PERSON><PERSON>n wurden gelöscht. Klicke auf OK um die App neu zu starten.", "okButtonLabel": "OK"}, "ticketSection": {"title": "Deine Tickets", "openTicketListButtonLabel": "Alle Buchungen zeigen", "disclaimer": "Du kannst mit deinem CINFINITY-Abo bis zu drei Tickets im Voraus buchen. <PERSON><PERSON><PERSON> eines dieser Tickets am Einlass des Kinos gescannt oder von dir storniert wurde, kannst du ein Weiteres buchen."}, "invitation": {"info": "<PERSON> {{username}} möchte eure Accounts vernknüpfen.", "acceptButtonLabel": "Akzeptieren", "declineButtonLabel": "<PERSON><PERSON><PERSON><PERSON>"}, "linkAccountButtonLabel": "Account mit Freunden verknüpfen"}, "VoucherListView": {"title": "<PERSON><PERSON>", "voucherDialog": {"title": "Prämie"}}, "PerksListView": {"title": "Deine <PERSON>-Vorteile", "emptyDisclaimer": "Derzeit genießt du noch keine Status-Vorteile"}, "AchievementListView": {"title": "<PERSON><PERSON>", "emptyDisclaimer": "Derzeit hast du noch keine Badges"}, "TicketListView": {"title": "Tickets", "emptyLabel": "Du hast keine Tickets", "timeLabel": "{{datetime, formatDateTime HH:mm}}", "dateLabel": "{{datetime, formatDateTime dd.MM.}}", "subscriptionBlockedDialog": {"goBackButtonLabel": "OK", "userDataButtonLabel": "Profil"}}, "SettingsView": {"title": "Einstellungen", "feedbackSection": {"description": "Du hast ein technisches Problem?  \nSchau doch mal in unsere [FAQs](https://cinfinity.de/kino-abo/#faq) dort findest du Antworten auf häufig gestellte Fragen. Falls du dort nicht fündig wirst, wende dich bitte an unser Support-Team.", "sendFeedbackButtonLabel": "Support kontaktieren"}, "accountSection": {"title": "Account", "registerButtonLabel": "Account anlegen", "editProfileButtonLabel": "<PERSON><PERSON>", "selectCinemasButtonLabel": "<PERSON><PERSON>", "allowDataUsageButtonLabel": "Datenverarbeitung erlauben", "dataUsageAllowedLabel": "Du hast die Datenverarbeitung erlaubt", "logoutButtonLabel": "Abmelden", "deleteAccountButtonLabel": "Account l<PERSON><PERSON>", "editProfilePictureButtonLabel": "<PERSON>il<PERSON><PERSON> bearbeiten", "deleteAccountButtonLabel_anonymous": "<PERSON><PERSON>"}, "notificationSection": {"title": "Benachrichtigungen", "watchlistChannel": {"title": "Merkliste", "description": "Films<PERSON><PERSON> von deiner Merkliste", "askPermissionMessage": "<PERSON><PERSON><PERSON><PERSON> du erinnert werden, wenn Filme von deiner Merkliste ins Kino kommen?"}, "exitPollChannel": {"title": "Bewertungen", "description": "Bewerte Filme, die du gesehen hast", "askPermissionMessage": "Möchtest du Filme bewerten, nachdem du sie im Kino gesehen hast?"}, "cinemaNewsChannel": {"title": "Kino-Neuigkeiten", "description": "Neuigkeiten und Empfehlungen", "askPermissionMessage": "Möchtest du über besondere Neuigkeiten und Filmempfehlungen benachrichtigt werden?"}, "bonusProgramNewsChannel": {"title": "Bonus-Neuigkeiten", "description": "Benachrichtigungen über Prämien und Vorteile", "askPermissionMessage": "Möchtest du über erhaltene Prämien und weitere Vorteile benachrichtigt werden?"}, "offlineErrorMessage": "<PERSON>ine Präfer<PERSON>zen konnten leider nicht gespeichert werden, bitte versuche es erneut."}, "appearanceSection": {"title": "Farbschema", "darkButtonLabel": "<PERSON><PERSON><PERSON>", "autoButtonLabel": "Auto", "lightButtonLabel": "Hell"}, "legalSection": {"title": "Rechtliches", "termsButtonLabel": "Nutzungsbedingungen", "privacyAgreementButtonLabel": "Datenschutzerklärung", "bonusTermsButtonLabel": "Bonusprogram Teilnahmebedingungen", "openSourceDisclaimer": "Wir nutzen Open-Source-Software in dieser App. Den folgenden Entwicklern sind wir dankbar für ihre großartige Arbeit. Außerdem nutzen wir die TMDb API, sind aber nicht von TMDb zertifiziert oder mit TMDb affiliiert.", "openSourceLicensesButtonLabel": "Open-Source-<PERSON><PERSON><PERSON>", "agbButtonLabel": "Allgemeine Geschäftsbedingungen"}, "systemSettingsSection": {"title": "System", "rateAppButtonLabel": "<PERSON><PERSON>", "osSettingsButtonLabel": "{{osName}} App Einstellungen", "checkForUpdateButtonLabel": "Nach Update Suchen", "nativeUpdateAvailableDialog": {"title": "Update verfügbar", "description": "Ein Update ist im App Store verfügbar, möchtest du es jetzt installieren?", "description_android": "Ein Update ist im Play Store verfügbar, möchtest du es jetzt installieren?", "dismissButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "installUpdateButtonLabel": "Jetzt updaten"}, "codepushUpdateAvailableDialog": {"title": "Update verfügbar", "description": "Ein Update ist verfügbar, möchtest du es jetzt installieren?", "dismissButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "installUpdateButtonLabel": "Jetzt updaten"}, "updateCheckErrorDialog": {"title": "Fehler bei Update-Suche", "description": "Die Suche nach Updates ist fehlgeschlagen.", "contactSupportButtonLabel": "Support kontaktieren"}, "noUpdateAvailableDialog": {"title": "<PERSON>in Update verfügbar", "description": "Deine App-Version ist up to date"}}, "subscriptionsSection": {"title": "Abonnements", "upgradeButtonLabel": "Verbessern", "cancelButtonLabel": "Kündigen", "canceledButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> zum {{date}}", "openInvoicesButtonLabel": "Re<PERSON><PERSON>ngen", "cancelDialog": {"title": "<PERSON><PERSON>", "description": "Möchtest du dein Abo wirklich kündigen?", "dismissButtonLabel": "Abbrechen", "confirmButtonLabel": "Kündigen"}, "unsubscribe": {"successTitle": "<PERSON><PERSON>", "successDescription": "Dein Abo wurde gekündigt. Du hast noch Zugriff bis zum {{cancellationEffectiveDate}}.", "errorMessage": "<PERSON>in <PERSON>bo konnte nicht gekündigt werden."}, "subscriptionRestrictionDialog": {"title": "Konto kann nicht gelöscht werden", "description": "Du hast ein aktives Abo. Du kannst deinen Account erst nach Beendigung des Abos löschen. Solltest du deinen Account sofort löschen wollen schreibe bitte eine <NAME_EMAIL>", "confirmButtonLabel": "OK"}}}, "OfflineView": {"title": "Offline", "contentTitle": "Offline :(", "description": "<PERSON> sche<PERSON>t offline zu sein.", "disclaimer": "Ohne Internet funktioniert diese Seite leider nicht.", "refetchButtonLabel": "Neu laden", "contactSupportButtonLabel": "Support"}, "CrashView": {"title": "<PERSON><PERSON><PERSON><PERSON>", "contentTitle": "Ups :(", "description": "Da ist wohl was schief gelaufen.", "disclaimer": "<PERSON><PERSON> uns den Fehler zu beheben, indem du ihn meldest.", "reportErrorButtonLabel": "<PERSON><PERSON> melden"}, "ReportIssueToSupportView": {"introduction": "Um den Fehler schnellstmöglich beheben zu können, würden wir uns sehr freuen, wenn du so genau wie möglich beschreiben könntest, was du in der App gemacht hast, bevor der Fehler aufgetreten ist.\n\nDeine Beschreibung:\n\nz.B. Als ich auf das Filmposter in gedrückt habe, ist die App abgestürzt.\n\n\n\n\n\n\n\nSystemnachricht:\n"}, "Global": {"appName": "Cinfinity", "termsVersion": 1, "privacyLink": "https://cinfinity.de/datenschutz", "termsLink": "https://cinfinity.de/nutzungsbedingungen-b2c/", "agbLink": "https://cinfinity.de/agb/", "transactionalOutboundMail": "<EMAIL>", "defaultUserName": "$t(app:Global.appName) Nutzer", "reportIssueSuccessMessage": "Der Fehler wurde übermittelt.", "icon": "https://static.cinuru.com/public/newsletterimage/cinuru.png", "emailChannels": {"RECOMMENDATION": {"name": "Personalisierte Filmempfehlungen"}}, "tabs": {"discover": {"iconName": "movie", "label": "Filme"}, "foryou": {"iconName": "foryou", "label": "<PERSON><PERSON><PERSON>"}, "search": {"iconName": "loupe", "label": "<PERSON><PERSON>"}, "showtimes": {"iconName": "calendar", "label": "Programm"}, "incinema": {"iconName": "star", "label": "Extra"}, "profile": {"iconName": "profile", "label": "Profil"}, "cinema": {"iconName": "cinema", "label": "<PERSON><PERSON>"}, "abo": {"label": "Abo", "iconName": "ticket"}}, "permissionRequests": {"notification": {"title": "$t(app:Global.appName) möchte dir Benachrichtigungen senden", "description": "Bitte aktiviere Benachrichtigungen in den Einstellungen."}, "microphone": {"title": "$t(app:Global.appName) möchte auf dein Mikrophon zugreifen", "description": "Wird nur für die Identifizierung von Trailern im Kinosaal verwendet, keine Daten werden an uns übertragen.\n\nBitte erlaube Mikrofonzugriff in den Einstellungen."}, "camera": {"title": "$t(app:Global.appName) möchte auf deine Kamera zugreifen", "description": "Wird nur für Scannen von QR Codes verwendet, keine Daten werden an uns übertragen.\n\nBitte erlaube Kamera-Zugriff in den Einstellungen."}, "location": {"title": "$t(app:Global.appName) möchte auf deinen Standort zugreifen, wenn du die App nutzt", "description": "Wird nur für die Berechnung des Abstands vom Kino genutzt, keine Daten werden an uns übertragen.\n\nBitte erlaube Zugriff auf Ortsdaten in den Einstellungen."}, "appTracking": {"title": "Darf $t(app:Global.appName) deine Aktivitäten in Apps und auf Websites anderer Unternehmen erfassen?", "description": "Damit wird Dein <PERSON>le<PERSON>nis noch persönlicher."}, "androidAppTrackingTransparency": {"title": "Darf $t(app:Global.appName) deine Aktivitäten in Apps und auf Websites anderer Unternehmen erfassen?", "description": "Damit wird Dein <PERSON>le<PERSON>nis noch persönlicher."}, "acceptButtonLabel": "<PERSON>a", "rejectButtonLabel": "<PERSON><PERSON>"}, "globalErrorDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ein unerwarteter Fehler ist aufgetreten. Bitte versuche es in einigen Minuten erneut, sollte der Fehler weiterhin bestehen schreibe uns <NAME_EMAIL>", "reportErrorButtonLabel": "<PERSON><PERSON> melden", "restartAppButtonLabel": "Neustart"}, "graphQLErrorDialog": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ein unerwarteter Fehler ist aufgetreten. Du könntest uns sehr helfen, indem du uns durch Tippen auf ‘$t(app:Global.graphQLErrorDialog.reportErrorButtonLabel)’ eine Email mit Details zum error schickst.", "reportErrorButtonLabel": "<PERSON><PERSON> melden"}, "reportBrokenLinkDialog": {"title": "Kaputter Link", "description": "<PERSON><PERSON><PERSON><PERSON> Glückwunsch, du hast einen Bug gefunden. E<PERSON> wäre super, wenn du uns mitteilen könntest, dass der link ‘{{link}}’ kaputt ist, dann kümmern wir uns darum.", "reportErrorButtonLabel": "Ok"}, "missingOtherAppDialog": {"title_browser": "<PERSON><PERSON> install<PERSON>t", "title_email": "<PERSON><PERSON>programm installiert", "title_phone": "Telefonfunktion nicht verfügbar", "description_browser": "Wir können diesen Link nicht ö<PERSON>, da kein Browser zur Verfügung steht. Bitte installiere einen Browser und öffne den Link erneut.", "description_email": "Wir können diesen Link nicht ö<PERSON>nen, da kein Emailprogramm zur Verfügung steht. Bitte installiere ein Emailprogramm und öffne den Link erneut.", "description_phone": "Auf Ihrem Gerät steht keine Telefonfunktion zur Verfügung. Sie können uns unter {{phoneNumber}} erreichen."}, "reportBrokenDeepLinkDialog": {"title": "Kaputter Link", "description": "Da ist wohl was schief gelaufen. <PERSON><PERSON> wäre super, wenn du uns mitteilen könntest, dass der deep link ‘{{link}}’ kaputt ist, dann kümmern wir uns darum.", "reportErrorButtonLabel": "Ok"}, "emails": {"brokenPosterPlaceholder": "https://static.cinuru.com/public/newsletterimage/placeholder/", "greeting": "Viel Spaß im Kino!", "signature": "<PERSON><PERSON>-Team", "footer": ["Cinfinity GmbH", "Theaterplatz 1 | 23909 Ratzeburg", "GF: <PERSON>, <PERSON><PERSON>"], "privacyLabel": "Datenschutzerklärung"}, "privacyLabel": "Datenschutzerklärung"}, "ConfirmRegistrationEmail": {"subject": "Willkommen bei CINFINITY", "preview": "Willkommen bei CINFINITY. Bitte bestätige deine E-Mail Adresse.", "title": "Willkommen!", "salutation": "Hallo {{name}},", "description": "Bitte bestätige deine E-Mail Adresse, um loszulegen. ", "description_code": "Bitte bestätige deine Email Adresse mit dem folgenden Code:", "description_raw": "Wir freuen uns, dass du dich bei $t(app:Global.appName) angemeldet hast! Bitte bestätige deine Email Adresse, in dem du folgenden Link öffnest:\n{{href}}", "description_raw_code": "Bitte bestätige deine Email Adresse mit dem folgenden Code: {{code}}", "buttonLabel": "<PERSON><PERSON>", "appLinkLabel": "Tippe um die App zu öffnen oder gib den Code manuell ein.", "greeting": "Wir freuen uns, dass du dabei bist!\nViel Spaß mit CINFINITY"}, "ResetPasswordEmail": {"subject": "Passwort zurücksetzen", "preview": "So kannst du dein Password zurücksetzen.", "title": "Passwort zurücksetzen", "salutation": "Hallo {{name}},", "description": "wir haben eine Anfrage erhalten um dein Password zurückzusetzen.", "description_code": "mit folgendem Code kannst du ein neues Password für deinen CINFINITY-Account in der App festlegen:", "description_raw": "Wir haben eine Anfrage erhalten um dein Password zurückzusetzen. Dies kannst du mit folgenden Link tun:\n{{href}}", "description_raw_code": "mit folgendem Code kannst du ein neues Password für deinen CINFINITY-Account in der App festlegen: {{code}}", "buttonLabel": "Passwort zurücksetzen"}, "ConfirmEmailChangeEmail": {"subject": "<PERSON><PERSON>", "preview": "Bitte bestätige deine neue Email Adresse.", "title": "<PERSON><PERSON>", "salutation": "Hallo {{name}},", "description": "du hast die Email deines $t(app:Global.appName) Profils geändert. Bitte bestätige deine neue Email Adresse.", "description_code": "du bist dabei deine E-Mail Adresse zu ändern. ", "description_raw": "Du hast die Email deines $t(app:Global.appName) Profils geändert. Bitte bestätige deine Email Adresse, in dem du folgenden Link öffnest:\n{{href}}", "description_raw_code": "du bist dabei deine E-Mail Adresse zu ändern. Hier dein Code: {{code}}", "buttonLabel": "Neue Email bestätigen", "appLinkLabel": "Tippe um die App zu öffnen oder gib den Code manuell ein, um die Änderung zu bestätigen."}, "MovieRecommendationEmail": {"salutation": "Hallo{{name}},", "subject": "Dein $t(app:Global.appName)-Newsletter", "preview": "Deine persönlichen Filmempfehlungen sind da.", "preview_quiz": "Lust auf persönliche Filmempfehlungen?", "title": "Kennst du diese Filme schon? \nUnsere Vorschläge der Woche für dich und deinen Geschmack.", "title_quiz": "<PERSON>hau nur noch Filme nach deinem Geschmack!", "description": "falls du das Gefühl hast, hin und wieder im falschen Film zu sein: Wir versuchen deinen Geschmack bestmöglich zu analysieren, um dir einmal die Woche eine optimal auf dich zugeschnittene Filmempfehlung zu geben.", "recommendedMoviePlaceholderText": "Hier ist noch Platz für deine persönliche Filmempfehlung!", "description_quiz": "Leider wissen wir noch zu wenig über deinen Filmgeschmack. Nimm einmalig an unserem Filmbewertungsquiz teil und erhalte künftig individuell auf dich zugeschnittene Filmempfehlungen!", "description_program": "Weitere Filme und Spielzeiten des $t(app:Global.appName) gibt's hier:", "buttonLabel": "Das volle Programm!", "buttonLabel_quiz": "Filme bewerten!", "defaultMovieDetails": {"synopsis": "- leider ist für diesen Film noch kein Beschreibungstext verfügbar -", "poster": "https://static.cinuru.com/public/imagesSmall/1562229930350.jpg"}, "linkDescription": "Mehr zum Film", "recommendationDisclaimer": "Dein Film der Woche:", "otherMoviesDisclaimer": "Weitere Filme im Programm:", "otherMoviesDisclaimer_quiz": "Stöbern? Diese Filme laufen gerade in deinen Lieblingskinos:", "selectedCinemasScreeningDisclaimer": "{{movieTitle}} in deinen Kinos:", "unsubscribing": "<PERSON> du unseren Newsletter nicht mehr empfangen möchtest, klicke hier!"}, "NewsletterEmail": {"signupForm": {"header": "Newsletter Auswahl", "header_error": "ERROR", "description": "W<PERSON>hle hier die Newsletter aus, die du abbonieren möchtest und teile uns deine Emailadresse mit:", "description_error": "Deine Eingabe war falsch oder unvollständig. W<PERSON>hle mindestens einen Newsletter aus, den du abbonieren möchtest und teile uns deine vollständige Emailadresse mit:", "greeting": "<PERSON><PERSON><PERSON> Grüße, \n <PERSON><PERSON> {{brand}}-Team", "correctEmail": "Überprüfe die Richtigkeit deiner Emailadresse!"}, "signupFormSendingResponse": {"header": "VIELEN DANK", "description": "Eine Bestätigungsemail wurde an deine Emailadresse versendet.", "greeting": "<PERSON><PERSON><PERSON> Grüße, \n <PERSON><PERSON> {{brand}}-Team"}, "subscribingWebResponse": {"header_success": "Erfolgreiche Anmeldung", "header_error": "ERROR", "description_success": "Wir wünschen dir viel Spaß mit unserem Newsletter.", "description_error": "<PERSON>ider ist ein Fehler aufgetreten Wir kümmern uns umgehend um das Problem.", "greeting": "<PERSON><PERSON><PERSON> Grüße, \n <PERSON><PERSON> {{brand}}-Team"}, "unsubscribingWebResponse": {"header": "Erfolgreiche Abmeldung", "description": "<PERSON><PERSON><PERSON>, dass Du unserem Newsletter bis hierhin gefolgt bist.", "greeting": "<PERSON><PERSON><PERSON> Grüße, \n <PERSON><PERSON> {{brand}}-Team"}}, "ConfirmNewsletterEmail": {"subject": "Newsletter Email", "preview": "Bitte bestätige deine Email Adresse, um unseren Newsletter zu erhalten.", "title": "Newsletter Email", "description": "<PERSON> würdest gern den Newsletter \"$t(app:Global.emailChannels.{{channel}}.name)\" erhalten? Dann bestätige hier bitte deine Email Adresse.", "buttonLabel": "<PERSON><PERSON>"}, "ReminderActivationEmail": {"subject": "Email Bestätigungs Erinnerung", "preview": "Bitte bestätige deine Email Adresse!", "title": "Email Bestätigungs Erinnerung!", "salutation": "Hallo {{name}},", "description": "du hast dich mit dieser Email Adresse bei $t(app:Global.appName) angemeldet. Bitte bestätige sie innerhalb der nächsten 24h, sonst müssen wir deinen Account leider sperren.", "description_raw": "du hast dich mit dieser Email Adresse bei $t(app:Global.appName) angemeldet. Bitte bestätige sie innerhalb der nächsten 24h, sonst müssen wir deinen Account leider sperren. Zur bestätigung öffne bitte den folgenden Link:\n{{href}}", "buttonLabel": "<PERSON><PERSON>"}, "AccountBlockedEmail": {"subject": "$t(app:Global.appName) Account gesperrt", "preview": "Dein $t(app:Global.appName) Account wurde gesperrt.", "title": "Dein Account wurde gesperrt!", "salutation": "Hallo {{name}},", "description": "leider hast du deine Email nicht bestätigt. Um eine missbräuchliche Nutzung zu vermeiden, haben wir deinen $t(app:Global.appName) Account nun gesperrt.", "description_raw": "leider hast du deine Email nicht bestätigt. Um eine missbräuchliche Nutzung zu vermeiden, haben wir deinen $t(app:Global.appName) Account nun gesperrt. Du kannst deinen Account ganz ein<PERSON>ch entsperren, indem du den folgenden Link öffnest:\n{{href}}", "buttonLabel": "Account entsperren", "disclaimer": "Falls du dich nicht bei $t(app:Global.appName) angemeldet hast, ignoriere diese Mail bitte! Dein Account wird demnächst automatisch gelöscht.", "greeting": "Viele Grüße,"}, "Notifications": {"exitPoll": {"title": "Wie hat dir ‘{{movieTitle}}’ gefallen?", "body": "<PERSON>zt bewerten!"}, "provisionalBonusPoints": {"title": "Welchen Film hast du im {{cinemaName}} gesehen?", "body_coins": "Jetzt auswählen und Bonuspunkte erhalten.", "body_stamps": "Jetzt auswählen und Stempel erhalten."}}, "AddNameView": {"saveButtonLabel": "Speichern", "title": "<PERSON><PERSON>", "subtitle": "Dein Profil ist nicht vollständig. Bitte fülle alle notwendigen Felder aus."}, "OrderListView": {"title": "<PERSON><PERSON>", "emptyLabel": "Du hast keine Bestellungen", "dateLabel": "{{datetime, formatDateTime d. MMM, uppercase}}"}, "OrderItem": {"title": "Snacks und Getränke", "imageUrl": "https://cdn-app.cineplex.de/app/cineplex/conc/650.jpg"}, "TicketDetailView": {"title": "{{datetime, formatDateTime d. MMM yyyy - HH:mm, uppercase}}", "runningSinceDisclaimer": "Läuft seit {{runningSinceMinutes}} Minuten", "canceledLabel": "<PERSON><PERSON><PERSON><PERSON>", "refundWaitingHeading": "<PERSON><PERSON><PERSON>", "refundWaitingLabel": "Du erhältst eine E-Mail, sobald die Stornierung abgeschlossen ist.", "reservationLabel": "Reservierung", "qrCodeLabel": "Buchungsnummer", "pastLabel": "Abgelaufen", "convertIntoSaleButtonLabel": "in Kauf <PERSON>n", "auditoriumLabel": "Saal: ", "rowLabel": "<PERSON><PERSON><PERSON>: ", "rowLabel_plural": "<PERSON><PERSON><PERSON>: ", "seatLabel": "Sitz: ", "seatLabel_plural": "Sitze: ", "cancelButtonLabel": "Buchung stornieren", "cancelConfirmDialog": {"title": "Stornieren", "description": "Willst du zum Online-Ticketing gehen, um die Buchung zu stornieren?", "description_TICKET_INTERNATIONAL": "Willst du die Buchung wirklich stornieren?", "dismissButtonLabel": "Abbrechen", "cancelButtonLabel": "<PERSON><PERSON>", "hint": "Achtung: <PERSON><PERSON> das Ticket zusammen mit anderen Tickets gekauft wurde, werden durch die Stornierung auch alle anderen Tickets storniert.  "}, "cancelFailedDisclaimer": "Die Stornierung ist fehlgeschlagen.", "cancelImpossibleOther": "Diese Buchung ist nicht stornierbar.", "cancelImpossiblePreparationStarted": "Dieses Ticket ist nicht stornierbar, da die zugehörige Getränke & Snacks Bestellung bereits zubereitet wird.", "flatrateTicketNotYetValid": "Das Ticket kann erst kurz vor der Vorstellung angezeigt werden. Bitte probiere es später nochmal.", "addPhotoDescription": "Bitte fügen Sie ein Profilfoto hinzu, um Ihre Identität zu bestätigen.", "addPhotoButtonLabel": "Foto hinzufügen", "cancelTicket": {"errorDialog": {"title": "<PERSON><PERSON>", "title_networkError": "Offline", "description": "<PERSON>im <PERSON>ornieren der Buchung ist ein unerwarteter Fehler aufgetreten. Bitte versuche es erneut.", "description_networkError": "<PERSON> scheinst offline zu sein, bitte versuche es erneut. $t(app:Global.contactSupportDescription)", "title_cancellationPeriodExpired": "Nicht mehr stornierbar", "description_cancellationPeriodExpired": "Deine Buchung konnte nicht storniert werden. <PERSON><PERSON>, dass eine Stornierung nur bis zu einer gewissen Zeit vor Vorstellungsbeginn möglich ist. $t(app:Global.contactSupportDescription)", "title_bookingNotFound": "Buchung nicht gefunden", "description_bookingNotFound": "Deine Buchung konnte nicht gefunden werden. $t(app:Global.contactSupportDescription)"}, "cancelTicketSuccessMessage": "Ticket wurde storniert."}}, "OrderDetailView": {"title": "{{datetime, formatDateTime d. MMM yyyy, uppercase}}", "canceledLabel": "<PERSON><PERSON><PERSON><PERSON>", "refundWaitingHeading": "<PERSON><PERSON><PERSON>", "refundWaitingLabel": "Du erhältst eine E-Mail, sobald die Stornierung abgeschlossen ist.", "startPreparationButtonLabel": "Jetzt Zubereitung vor Ort starten", "qrCodeLabel": "Buchungsnummer", "pastLabel": "Abgelaufen", "cancelButtonLabel": "Bestellung stornieren", "pickUpCodeLabel": "<PERSON><PERSON>", "orderDetailsLabel": "Bestelldetails", "cancelConfirmDialog": {"title": "Ticket stornieren", "description": "Wenn du deine Bestellung stornierst, wird auch das dazugehörige Kinoticket storniert. Willst du deine Bestellung wirklich stornieren?", "description_api": "Willst du die Buchung wirklich stornieren?", "dismissButtonLabel": "<PERSON><PERSON>", "cancelButtonLabel": "<PERSON>a"}, "cancelFailedDisclaimer": "Die Stornierung ist fehlgeschlagen.", "cancelImpossibleOther": "Diese Buchung ist nicht stornierbar.", "cancelImpossiblePreparationStarted": "Diese Buchung ist nicht stornierbar, da sie bereits zubereitet wird.", "cancelTicket": {"errorDialog": {"title_networkError": "Offline", "description_networkError": "<PERSON> scheinst offline zu sein, bitte versuche es erneut. $t(app:Global.contactSupportDescription)", "title_cancellationPeriodExpired": "Nicht mehr stornierbar", "description_cancellationPeriodExpired": "Deine Buchung konnte nicht storniert werden. <PERSON><PERSON>, dass eine Stornierung nur bis zu einer gewissen Zeit vor Vorstellungsbeginn möglich ist. $t(app:Global.contactSupportDescription)", "title_bookingNotFound": "Buchung nicht gefunden", "description_bookingNotFound": "Deine Buchung konnte nicht gefunden werden. $t(app:Global.contactSupportDescription)"}, "cancelTicketSuccessMessage": "Bestellung zur Stornierung vorgemerkt."}, "startOrderPreparationConfirmDialog": {"title": "Zubereitung starten", "description": "Willst du deine Bestellung jetzt im Kino zubereiten lassen?", "dismissButtonLabel": "<PERSON><PERSON>", "cancelButtonLabel": "<PERSON>a"}, "startOrderPreparation": {"errorDialog": {"title_networkError": "Offline", "description_networkError": "<PERSON> scheinst offline zu sein, bitte versuche es erneut. $t(app:Global.contactSupportDescription)", "title_startOrderError": "Oh ein <PERSON>", "description_startOrderError": "Es ist leider zur Zeit nicht möglich Bestellungen über die App zu starten. Du kannst die Bestellung an der Theke abholen, einfach den QR-Code vorzeigen."}, "successMessage": "<PERSON><PERSON> Bestellung wird nun zubereitet."}}, "CreatedSubscriptionInfoEmail": {"salutation": "Hallo {{name}},", "subject": "Willkommen bei CINFINITY", "preview": "CINFINITY-Abo erfolgreich abgeschlossen. ", "title": "Abo abgeschlossen", "description": "herzlich Willkommen im CINFINITY-Universum! Wir freuen uns riesig, dass du dabei bist, und wünschen dir ganz viel Spaß mit deinem CINFINITY-Abo.\n Entdecke jetzt alle teilnehmenden Kinos in der CINFINITY-App und auf unserer Website. Es werden mit der Zeit immer mehr Kinos dazukommen. Falls dein Lieblingskino nicht dabei sein sollte, kannst du es gerne auf CINFINITY aufmerksam machen. Jedes Kino kann kostenlos bei uns mitmachen. \nÜbrigens: Du kannst deinen Account mit anderen Personen verknüpfen, die ebenfalls ein CINFINITY-Abo besitzen. So könnt ihr eure Kinoabende direkt zusammen planen. \nDu hast Fragen? Melde dich jederzeit bei uns. ", "greeting": "<PERSON>iel Spaß im Kino\nDein CINFINITY-Team"}, "UpdatedSubscriptionInfoEmail": {"salutation": "Hallo {{name}},", "subject": "Zahlung eingegangen", "preview": "<PERSON><PERSON>, deine Zahlung ist eingegangen.", "title": "Zahlung erhalten", "description": "deine Zahlung ist erfolgreich bei uns eingegangen. In der CINFINITY-App findest du deine dazugehörige Rechnung.", "greeting": "Herzlichen Dank\nDein CINFINITY-Team"}, "CancelledSubscriptionInfoEmail": {"salutation": "Hallo {{name}},", "subject": "Kündigung bestätigt", "preview": "Du hast dein Abo erfolgreich gekündigt.", "title": "<PERSON><PERSON>", "description": "hiermit bestätigen wir die Kündigung deines CINFINITY-Abos. <PERSON><PERSON><PERSON>, dass du uns verlässt! Wenn dir danach ist, kannst du uns gerne den Grund deiner Kündigung mitteilen. Schreib uns einfach eine E-Mail an: <EMAIL> . Dein CINFINITY-Account bleibt übrigens bestehen und du kannst unsere App weiterhin im vollen Umfang nutzen. Wir würden uns freuen dich bald mal wieder im CINFINITY-Universum begrüßen zu können.", "greeting": "Hasta la vista\nDein CINFINITY-Team"}, "SeatSelection": {"submitPricingCategories": {"errorDialog": {"title": "<PERSON><PERSON>", "description": "Es ist ein Fehler aufgetreten: {{error}}", "description_noSelectedSeatBelongsToBooker": "Bitte wähle mindestens einen Tarif aus, der kein Abo eines anderen Nutzers ist.", "buttonLabel": "Ok"}}}, "PaypalOrderWebView": {"cancelDialog": {"title": "Zahlung abgebrochen", "description": "Die Zahlung wurde abgebrochen.", "buttonLabel": "Ok"}, "errorDialog": {"title": "<PERSON><PERSON>", "description": "Die Zahlung konnte nicht abgeschlossen werden.", "buttonLabel": "Ok"}, "successDialog": {"title": "Zahlung erfolgreich", "description": "Die Zahlung war erfolgreich.", "buttonLabel": "Ok"}}, "SubscriptionTierSelectView": {"perMonth": "pro <PERSON><PERSON>", "buttonLabel": "Jetzt kaufen", "laterButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "cancelButtonLabel": "Abbrechen", "active": "Aktiv", "subscribeError": "Das Abonnement konnte nicht gekauft werden.", "subscribe": {"errorDialog": {"title": "<PERSON><PERSON>", "description": "Es ist ein Fehler aufgetreten: {{error}}.", "description_alreadyHasSubscribed": "Du hast bereits ein Abonnement."}, "paypalDialog": {"title": "Information", "description": "Du wirst nun zu PayPal weitergeleitet, um die Zahlung abzuschließen.", "description_infoText": "{{infoText}}", "confirmButtonLabel": "Ok", "dismissButtonLabel": "Abbrechen"}}, "priceDisclaimer": "150,00 € für 12 Monate im Voraus. Entspricht 12,50 € mtl. (Ab dem 13. <PERSON><PERSON> 13,90 € mtl.) Bei monatlicher Zahlweise 13,90 € mtl. *58,00 € für 4 Monate im Voraus, entspricht 14,50 € mtl. (Ab dem 5. <PERSON><PERSON> 13,90 € mtl.) Bei monatlicher Zahlweise 14,90 € mtl. (Ab dem 5. <PERSON><PERSON> 13,90 € mtl.) Alle Preise verstehen sich... usw.", "surchargeDisclaimer": "Übliche Zuschläge für 3D, Logenplätze, Überlänge usw., können vom jeweiligen Kino selbst festgelegt und zusätzlich erhoben werden. Diese Zuschläge werden bei der Ticketbuchung direkt an das Kino bezahlt.", "termsDisclaimer": "Es gelten unsere [Allgemeinen Geschäftsbedingungen](https://cinfinity.de/agb)."}, "ConcessionsSelectView": {"intro": {"header": "Snacks & Getränke", "info": "Direct mit bestellen und Zeit sparen", "CTA": "<PERSON><PERSON> Auswahl!", "skip": "Direkt zur Ticketkasse gehen"}}, "LinkAccountView": {"title": "Accounts verknüpfen", "description": "Hier kannst du deinen Account mit Freunden verknüpfen, damit ihr gemeinsam Tickets buchen könnt. Gib die E-Mail Adresse des CINFINITY-Accounts ein den du verknüpfen möchtest. Der eingeladene Nutzer muss der Einladung anschließend noch zustimmen.", "linkedAccountsSection": {"title": "Verknüpfte Accounts", "buttonLabel_cancel": "Aufheben", "buttonLabel_decline": "<PERSON><PERSON><PERSON><PERSON>", "buttonLabel_accept": "Akzeptieren", "buttonLabel_delete": "Entfernen", "status_accepted": "Verknüpft", "status_invited": "Einladung ausstehend", "status_incomingInvite": "Einladung erhalten", "status_declinedByUser": "Einladung abgelehnt", "status_declinedByFriend": "Verknüpfung Aufgehoben", "snackbar_invited": "Die Einladung wurde gesendet", "snackbar_declined": "Die Einladung wurde abgelehnt.", "snackbar_accepted": "Die Einladung wurde akzeptiert.", "snackbar_deleted": "Die Verknüpfung wurde aufgehoben.", "networkError": "Netzwerkfehler", "error": {"unknownErrorMessage": "Ein unbekannter Fehler ist aufgetreten.", "badUserInputErrorMessage": "Ungültige E-Mail Adresse."}}, "linkNewAccountSection": {"title": "Neuen Account verknüpfen", "buttonLabel": "Verknüpfen", "emailInput": {"label": "E-Mail Adresse", "errorMessage": "Ungültige E-Mail Adresse", "buttonLabel": "Verknüpfen"}, "successMessage": "Die Einladung wurde versendet.", "errorDialog": {"title": "<PERSON><PERSON>", "description_userNotFound": "Diese E-Mail Adresse ist uns nicht bekannt", "description_unknownError": "Ein unbekannter Fehler ist aufgetreten", "description_networkError": "<PERSON>s konnte keine Verbindung zum Server hergestellt werden", "description_ownAccount": "Du hast deine eigene E-Mail eingegeben", "description_alreadyLinked": "Eure Accounts sind bereits verknüpft", "description_alreadyInvited": "Du hast diesen Nutzer bereits eingeladen", "description_alreadyDeclined": "Der Nutzer hat deine Einladung abgelehnt"}}}, "InvoiceListView": {"title": "Re<PERSON><PERSON>ngen", "emptyLabel": "Du hast keine Re<PERSON>nungen"}, "CinfinityAboView": {"title": "CINFINITY-Abo", "openInvoicesButtonLabel": "Re<PERSON><PERSON>ngen"}, "OnlineShopVoucherEmail": {"subject": "<PERSON>in Cinfinity Gutschein", "salutation": "Hallo {{name}},", "text": "im Anhang senden wir dir deinen Cinfinity Gutschein. V<PERSON>ß beim e<PERSON><PERSON>sen oder verschenken!", "greeting": "<PERSON><PERSON>-Team"}, "ConfirmedOnlineTicketingBookingRequest": {"subject": "<PERSON><PERSON>", "salutation": "Hallo {{name}},", "text": "Bitte entschuldige die Verzögerung. Deine Kinotickets für {{movieTitle}} am {{datetime, formatDateTime d. MMM yyyy - HH:mm, uppercase}} werden nun in der App angezeigt."}}