{"name": "dashboard", "version": "2.3", "private": true, "scripts": {"build": "yarn codegen && NODE_ENV=production webpack", "build:cinfinity": "yarn codegen && NODE_ENV=production webpack --env brand=cinfinity", "codegen": "graphql-codegen", "start": "concurrently --raw --kill-others 'yarn codegen -w' 'sleep 5 && webpack serve'", "serve": "serve -s -p 4000 dist", "cleanstart": "rm -rf ./node_modules/.cache/babel-loader/* && yarn start", "build:server": "git pull && yarn && NODE_ENV=production yarn build && rm -rf /var/www/dashboard.cinuru.com/* && cp -r dist/. /var/www/dashboard.cinuru.com", "build:server:dev": "yarn build && rm -rf /var/www/devdashboard.cinuru.com/* && cp -r dist/. /var/www/devdashboard.cinuru.com", "build:server:cinfinity": "rm -rf dist && NODE_ENV=production yarn build --env brand=cinfinity && rm -rf /var/www/dashboard.cinfinity.de/* && cp -r dist/. /var/www/dashboard.cinfinity.de", "analyze": "source-map-explorer 'dist/*.js'"}, "dependencies": {"@apollo/client": "^3.13.5", "@assortment/darkmodejs": "^1.2.1", "@cinuru/components": "link:../components", "@cinuru/emails": "link:../emails", "@cinuru/utils": "link:../utils", "@date-io/date-fns": "^2.11.0", "@emotion/react": "^11.4.1", "@emotion/styled": "^11.3.0", "@material-ui/core": "4.12.3", "@mui/icons-material": "^5.0.4", "@mui/lab": "^5.0.0-alpha.51", "@mui/material": "^6.2.1", "@mui/styled-engine": "npm:@mui/styled-engine-sc@latest", "@mui/styles": "^5.0.1", "@mui/system": "^6.2.1", "@mui/utils": "^6.2.1", "@mui/x-date-pickers": "^7.23.2", "@nivo/bar": "^0.79.0", "@nivo/core": "^0.79.0", "@nivo/funnel": "^0.79.0", "@nivo/legends": "^0.79.0", "@nivo/line": "^0.79.0", "@types/react-csv": "^1.1.10", "assert": "^2.0.0", "base-64": "^1.0.0", "chroma-js": "^3.1.2", "common-tags": "^1.8.0", "date-fns": "^2.22.1", "dayjs": "^1.11.13", "graphql": "^15.5.0", "graphql-tag": "^2.12.4", "history": "^4.10.1", "hoist-non-react-statics": "^3.3.2", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "material-ui-popup-state": "^1.9.3", "notistack": "^3.0.2", "prop-types": "^15.7.2", "react": "17.0.2", "react-art": "^17.0.2", "react-colorful": "^5.6.0", "react-csv": "^2.2.2", "react-device-frameset": "^1.3.4", "react-dnd": "^14.0.2", "react-dnd-html5-backend": "^14.0.0", "react-dom": "^17.0.2", "react-error-boundary": "^5.0.0", "react-hotkeys": "^2.0.0", "react-native-gesture-handler": "1.10.3", "react-native-pose": "^0.9.1", "react-native-web": "^0.17.0", "react-router": "^6.11.2", "react-router-dom": "^6.11.2", "react-window": "^1.8.6", "shortid": "^2.2.16", "styled-components": "^5.3.0", "styled-native-components": "^0.3.4", "svgs": "^4.1.1", "ua-parser-js": "^0.7.33", "uuid": "^11.1.0"}, "resolutions": {"@mui/styled-engine": "npm:@mui/styled-engine-sc@latest"}, "devDependencies": {"@babel/cli": "^7.14.5", "@babel/core": "^7.14.6", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-transform-react-display-name": "^7.27.1", "@babel/preset-env": "^7.14.5", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.14.5", "@babel/register": "^7.25.9", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/typescript-react-apollo": "^4.3.2", "@parcel/watcher": "^2.5.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@types/chroma-js": "^3.1.1", "@types/copy-webpack-plugin": "^10.1.3", "@types/webpack-dev-server": "^4.7.2", "babel-loader": "^8.2.2", "babel-plugin-import": "^1.13.3", "concurrently": "^9.1.2", "copy-webpack-plugin": "^13.0.0", "css-loader": "^5.2.6", "dotenv": "^16.5.0", "dotenv-webpack": "^8.1.0", "file-loader": "^6.2.0", "html-loader": "^2.1.2", "html-webpack-plugin": "^5.3.1", "mini-create-react-context": "^0.4.1", "rc-util": "^5.13.1", "react-refresh": "^0.10.0", "regenerator-runtime": "^0.13.7", "serve": "^14.2.4", "source-map-explorer": "^2.5.2", "style-loader": "^2.0.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "type-fest": "^2.12.2", "typescript": "^5.8.2", "webpack": "^5.98.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1", "webpack-manifest-plugin": "^5.0.1", "webpack-node-externals": "^3.0.0", "workbox-webpack-plugin": "^7.3.0"}}