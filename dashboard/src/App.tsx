import * as React from 'react';
import './app.css';
import CssBaseline from '@mui/material/CssBaseline';
import styled, { useTheme } from 'styled-components';
import { SnackbarProvider } from 'notistack';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import 'dayjs/locale/de';
import { ErrorBoundary } from 'react-error-boundary';
import { PortalProvider as PortalProviderCinuruComponents } from '@cinuru/components';
import { Login } from './screens/Login';
import { useUserPrivileges } from './utils/user';
import ThemeProvider from './components/ThemeProvider';
import NavMenu from './components/NavMenu';
import { Box, Drawer, Toolbar } from '@mui/material';
import PortalProvider from './components/PortalProvider';
import { useWindowDimensions } from './utils/dimensions';
import {
	StickyHeaderWrapperProvider,
	useStickyHeaderWrapperProps,
} from './utils/useStickyHeaderWrapper';
import AppToolBar from './components/AppToolBar';
import { SuspenseDefault } from './components/SuspenseDefault';
import { Outlet } from 'react-router-dom';
import { ErrorBoundaryDefault } from './components/ErrorBoundaryDefault';

const ContentWrapper = styled(Box)`
	flex: 1;
	display: flex;
	flex-direction: row;
	justify-content: center;
	height: 100vh;
`;

const RouteWrapper = styled(Box)`
	flex: 1;
	height: 100%;
	background-color: ${(p) => p.theme.palette.grey[100]};
	@media print {
		margin-left: 1rem;
	}
	overflow: auto;
`;

const CustomSnackbarProvider = styled(SnackbarProvider).attrs({
	maxSnack: 3,
	style: {
		fontSize: '16px',
	},
	anchorOrigin: {
		horizontal: 'center',
		vertical: 'bottom',
	},
})``;

const ContentBox = styled(Box)
	.withConfig({ shouldForwardProp: (prop) => !prop.includes('drawerWidth') })
	.attrs({ component: 'main' })<{ drawerWidth: string | number }>`
	flex-grow: 1;
	width: calc(100% - ${(props) => props.drawerWidth});
	margin-left: ${(props) => props.drawerWidth};
`;

const StyledDrawer = styled(Drawer)`
	width: 22rem;
	flex-shrink: 0;
	& .MuiDrawer-paper {
		width: 22rem;
		background: ${(props) => props.theme.palette.primary.main};
		border: none;
	}
`;

const Navigator = () => {
	const [openMenu, setOpenMenu] = React.useState(false);
	useUserPrivileges();
	const theme = useTheme();

	const { width } = useWindowDimensions();

	const shouldCollapseBar = width < theme.breakpoints.values.sm;

	const drawerWidth = shouldCollapseBar ? '0px' : '22rem';

	const stickyWrapperProps = useStickyHeaderWrapperProps();

	const onClose = React.useCallback(() => setOpenMenu(false), []);
	const onOpen = React.useCallback(() => setOpenMenu(true), []);

	return (
		<>
			<StyledDrawer
				variant={shouldCollapseBar ? 'temporary' : 'permanent'}
				open={openMenu}
				anchor={'left'}
				onClose={onClose}
			>
				<NavMenu onItemClick={onClose} />
			</StyledDrawer>
			<AppToolBar
				drawerWidth={drawerWidth}
				shouldCollapse={shouldCollapseBar}
				toggleMenu={onOpen}
			/>
			<ContentBox drawerWidth={drawerWidth}>
				<ContentWrapper>
					<RouteWrapper>
						<Toolbar />
						<Box
							// maximum width but not wider than maxContentWidth, horizontally centered
							p="2rem"
							maxWidth={stickyWrapperProps.maxContentWidth}
							flex="1"
						>
							<ErrorBoundaryDefault>
								<SuspenseDefault>
									<Outlet />
								</SuspenseDefault>
							</ErrorBoundaryDefault>
						</Box>
					</RouteWrapper>
				</ContentWrapper>
			</ContentBox>
		</>
	);
};

const fallbackRenderLogin = ({ resetErrorBoundary }) => <Login reset={resetErrorBoundary} />;

const App: React.FC = () => {
	return (
		<>
			<CssBaseline />
			<ThemeProvider>
				<CustomSnackbarProvider>
					{/*@ts-ignore*/}
					<PortalProviderCinuruComponents>
						<PortalProvider>
							<LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="de">
								<StickyHeaderWrapperProvider>
									<ErrorBoundary fallbackRender={fallbackRenderLogin}>
										<SuspenseDefault>
											<Navigator />
										</SuspenseDefault>
									</ErrorBoundary>
								</StickyHeaderWrapperProvider>
							</LocalizationProvider>
						</PortalProvider>
					</PortalProviderCinuruComponents>
				</CustomSnackbarProvider>
			</ThemeProvider>
		</>
	);
};

export default App;
