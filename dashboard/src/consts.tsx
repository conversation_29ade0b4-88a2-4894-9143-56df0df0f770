import { Brand } from 'utils/theme';

type ApiEndPoint =
	| 'http://localhost:3000'
	| 'https://api.cinuru.com'
	| 'https://devapi.cinuru.com/dev';
type FileServerEndPoint = 'http://localhost:3006' | 'https://static.cinuru.com';
export type Feature =
	| 'NEW_CAMPAIGNING'
	| 'NEW_STATISTICS'
	| 'NEW_EDIT_CINEMA'
	| 'TARGETGROUP_EDIT'
	| 'USER_EDIT'
	| 'CINFINITY_STATS'
	| 'PAYOUT_OVERVIEW'
	| 'BONUSPROGRAM';

export type Privilege =
	| 'accessRightDashboard'
	| 'accessRightFilmStatistics'
	| 'accessRightBonusProgram'
	| 'accessRightCampaigns'
	| 'adminRole'
	| 'rootRole'
	| 'supportRole';

export const API_ENDPOINT: ApiEndPoint =
	(process.env.API_ENDPOINT as ApiEndPoint) || 'https://api.cinuru.com';
export const FILE_SERVER_ENDPOINT: FileServerEndPoint =
	(process.env.FILE_SERVER_ENDPOINT as FileServerEndPoint) || 'https://static.cinuru.com';
export const FEATURES: Feature[] = ((process.env.FEATURES &&
	(JSON.parse(process.env.FEATURES) as unknown)) as Feature[]) || ['NEW_EDIT_CINEMA'];

export const brand: Brand = (process.env.BRAND as Brand) || 'CINURU';

export const isCinfinity = brand.toLowerCase().startsWith('cinfinity');

export const DATE_FORMAT = 'yyyy-MM-dd';
export const DATE_TIME_FORMAT = 'dd.MM.yyyy HH:mm';

export const BEACONS_UUID = 'B4569BBD-0757-4963-9093-21E880EC25D7';

console.log('FEATURES: ', FEATURES);
console.log('API_ENDPOINT', API_ENDPOINT);
