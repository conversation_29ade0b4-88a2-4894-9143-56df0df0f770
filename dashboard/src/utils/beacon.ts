import { default as gql } from 'graphql-tag';
import client from '../apollo';
import { ApolloError, useQuery, useSuspenseQuery } from '@apollo/client';
import React from 'react';
import { ID } from 'utils/types';
import { Cinema } from './cinema';

export const BeaconFragment = gql`
	fragment BeaconFragment on Beacon {
		id
		instanceId
		lastUpdated
		lastScan
		cinema {
			id
			name
		}
		comments
		lastBatteryChange
	}
`;

export type Beacon = {
	id: string;
	instanceId: number;
	lastUpdated: Date;
	lastScan: Date;
	cinema: Cinema;
	comments: string;
	lastBatteryChange: Date;
};

export const useBeacons = (): { beacons: Beacon[]; refetch: () => void } => {
	const { data, refetch } = useSuspenseQuery<{ beacons: Beacon[] }>(
		gql`
			query FetchBeacons {
				beacons {
					...BeaconFragment
				}
			}
			${BeaconFragment}
		`,
		{
			fetchPolicy: 'cache-and-network',
		}
	);
	return React.useMemo(
		() => ({
			beacons: data.beacons || [],
			refetch,
		}),
		[data.beacons]
	);
};

export const useBeaconById = (id?: ID): { data: Beacon; error?: ApolloError; loading: boolean } => {
	const { data, error, loading } = useQuery(
		gql`
			query BeaconById($id: ID!) {
				beaconById(id: $id) {
					...BeaconFragment
				}
			}
			${BeaconFragment}
		`,
		{
			variables: { id },
			skip: !id,
		}
	);
	return {
		data: data?.beaconById,
		error,
		loading,
	};
};

export const createBeacon = async ({
	cinemaId,
	comments,
	lastBatteryChange,
}: {
	cinemaId: ID;
	comments?: string;
	lastBatteryChange?: Date;
}): Promise<
	| { success: true; error: null; beacon?: Beacon }
	| { success: false; error: 'NETWORK_ERROR' | string; beacon?: Beacon }
> => {
	try {
		const { data } = await client.mutate({
			mutation: gql`
				mutation CreateBeacon($cinemaId: ID!, $comments: String, $lastBatteryChange: DateTime) {
					createBeacon(
						cinemaId: $cinemaId
						comments: $comments
						lastBatteryChange: $lastBatteryChange
					) {
						...BeaconFragment
					}
				}
				${BeaconFragment}
			`,
			variables: {
				cinemaId,
				comments,
				lastBatteryChange,
			},
		});

		const result = data?.createBeacon;

		return {
			success: true,
			error: null,
			beacon: result,
		};
	} catch (e: any) {
		if (e.networkError) {
			return { success: false, error: 'NETWORK_ERROR', beacon: undefined };
		} else {
			throw e;
		}
	}
};

export const updateBeacon = async ({
	beaconId,
	comments,
	lastBatteryChange,
}: {
	beaconId: ID;
	comments?: string;
	lastBatteryChange?: Date;
}): Promise<
	| { success: true; error: null; beacon?: Beacon }
	| { success: false; error: 'NETWORK_ERROR' | string; beacon?: Beacon }
> => {
	try {
		const { data } = await client.mutate({
			mutation: gql`
				mutation UpdateBeacon($id: ID!, $comments: String, $lastBatteryChange: DateTime) {
					updateBeacon(id: $id, comments: $comments, lastBatteryChange: $lastBatteryChange) {
						...BeaconFragment
					}
				}
				${BeaconFragment}
			`,
			variables: {
				id: beaconId,
				comments,
				lastBatteryChange,
			},
		});
		const result = data?.updateBeacon;

		return {
			success: true,
			error: null,
			beacon: result,
		};
	} catch (e: any) {
		if (e.networkError) {
			return { success: false, error: 'NETWORK_ERROR', beacon: undefined };
		} else {
			throw e;
		}
	}
};

export const deleteBeacon = async (
	beaconId: ID
): Promise<
	{ success: true; error: null } | { success: false; error: 'NETWORK_ERROR' | string }
> => {
	try {
		const { data } = await client.mutate({
			mutation: gql`
				mutation DeleteBeacon($id: ID!) {
					deleteBeacon(id: $id) {
						success
					}
				}
			`,
			variables: { id: beaconId },
		});

		const result = data?.deleteBeacon;

		if (result.success) {
			return { success: true, error: null };
		} else {
			return { success: false, error: 'Failed to delete beacon' };
		}
	} catch (e: any) {
		if (e.networkError) {
			return { success: false, error: 'NETWORK_ERROR' };
		} else {
			throw e;
		}
	}
};
