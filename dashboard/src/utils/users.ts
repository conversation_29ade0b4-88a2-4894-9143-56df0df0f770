import gql from 'graphql-tag';
import { ID } from '@cinuru/utils/types';
import client from '../apollo';
import { Ticket, TicketDetails } from './ticket';
import { VoucherInstance, VoucherInstanceFragment } from './voucher';
import { Subscription, SubscriptionDetailFragment } from './subscriptions';
import {
	CinemaFragmentDoc,
	SearchUsersQuery,
	UpdateUserDocument,
	useSearchUsersSuspenseQuery,
	useSearchUsersTotalSuspenseQuery,
	useUserByIdSuspenseQuery,
} from '../gql/graphql';

gql`
	fragment UserDetails on User {
		id
		firstName
		lastName
		street
		houseNumber
		zipCode
		city
		email
		blocked
		blockedText
		invoices {
			id
			priceInCents
			date
			url
		}
		linkedAccounts {
			id
			userId
			username
			email
			acceptedDatetime
			status
		}
		subscriptions {
			...SubscriptionDetailFragment
		}
		tickets {
			...TicketDetails
		}
		privileges {
			adminForCinemas {
				...CinemaFragment
			}
			belongsToCinemaOperatingCompanies {
				id
				name
			}
		}
		vouchers(onlyRedeemed: false) {
			...VoucherInstanceFragment
		}
	}
	${CinemaFragmentDoc}
	${TicketDetails}
	${VoucherInstanceFragment}
	${SubscriptionDetailFragment}
	mutation UpdateUser(
		$userId: ID!
		$blocked: Boolean
		$blockedText: String
		$firstname: String
		$lastname: String
		$street: String
		$houseNumber: String
		$zipCode: String
		$city: String
		$email: String
		$adminCinemaOperatingCompanyIds: [ID!]
		$resetAppChangeBlockedUntil: Boolean
	) {
		updateUser(
			userId: $userId
			blocked: $blocked
			blockedText: $blockedText
			firstname: $firstname
			lastname: $lastname
			street: $street
			houseNumber: $houseNumber
			zipCode: $zipCode
			city: $city
			email: $email
			adminCinemaOperatingCompanyIds: $adminCinemaOperatingCompanyIds
			resetAppChangeBlockedUntil: $resetAppChangeBlockedUntil
		) {
			...UserDetails
		}
	}

	query UserById($id: ID!) {
		userById(id: $id) {
			...UserDetails
		}
	}
`;

export type User = {
	id: ID;
	firstName: string;
	lastName: string;
	email: string;
	street?: string;
	houseNumber?: string;
	zipCode?: string;
	city?: string;
	blocked: boolean;
	blockedText?: string;
	linkedAccounts: {
		id: ID;
		userId: ID;
		username: string;
		email: string;
		acceptedDatetime?: string;
		status: 'INVITED' | 'INCOMING_INVITE' | 'ACCEPTED' | 'DECLINED_BY_USER' | 'DECLINED_BY_FRIEND';
	}[];
	invoices: {
		id: ID;
		priceInCents: number;
		date: string;
		url: string;
	}[];
	subscriptions: Subscription[];
	tickets: Ticket[];
	privileges: {
		belongsToCinemaOperatingCompanies: {
			id: ID;
			name: string;
		}[];
	};
	vouchers: VoucherInstance[];
};

export const updateUser = async ({
	userId,
	firstName,
	lastName,
	street,
	houseNumber,
	zipCode,
	city,
	email,
	blocked,
	blockedText,
	adminCinemaOperatingCompanyIds,
	resetAppChangeBlockedUntil,
}: {
	userId: ID;
	firstName?: string;
	lastName?: string;
	street?: string;
	houseNumber?: string;
	zipCode?: string;
	city?: string;
	email?: string;
	blocked?: boolean;
	blockedText?: string;
	adminCinemaOperatingCompanyIds?: ID[];
	resetAppChangeBlockedUntil?: boolean;
}): Promise<
	{ success: true; error: undefined; user?: User } | { success: false; error?: string; user?: User }
> => {
	try {
		console.log(
			'calling mutation with vars',
			JSON.stringify({
				userId,
				firstname: firstName,
				lastname: lastName,
				street,
				houseNumber,
				zipCode,
				city,
				email,
				blocked,
				blockedText,
				adminCinemaOperatingCompanyIds,
				resetAppChangeBlockedUntil,
			})
		);
		const { errors, data } = await client.mutate({
			mutation: UpdateUserDocument,
			variables: {
				userId,
				firstname: firstName,
				lastname: lastName,
				street,
				houseNumber,
				zipCode,
				city,
				email,
				blocked,
				blockedText,
				adminCinemaOperatingCompanyIds,
				resetAppChangeBlockedUntil,
			},
			errorPolicy: 'all',
		});
		if (errors) {
			return { success: false, error: errors[0]?.message, user: undefined };
		} else return { success: true, error: undefined, user: data.updateUser };
	} catch (e: any) {
		if (e.networkError) return { success: false, error: 'NETWORK_ERROR', user: undefined };
		else throw e;
	}
};

export const updateUserSubscription = async (
	id: ID,
	blocked: boolean,
	blockedText: string
): Promise<{ success: true; error: undefined } | { success: false; error?: string }> => {
	try {
		const { errors } = await client.mutate({
			mutation: gql`
				mutation UpdateUserSubscription($id: ID!, $blocked: Boolean!, $blockedText: String) {
					updateUserSubscription(id: $id, blocked: $blocked, blockedText: $blockedText) {
						id
					}
				}
			`,
			variables: {
				id,
				blocked,
				blockedText,
			},
			errorPolicy: 'all',
			refetchQueries: ['UserById'],
		});
		if (errors) {
			const error = errors[0]?.message;
			return { success: false, error };
		} else return { success: true, error: undefined };
	} catch (e: any) {
		if (e.networkError) return { success: false, error: 'NETWORK_ERROR' };
		else throw e;
	}
};

export type SearchedUser = SearchUsersQuery['searchUsers'][0];

gql`
	query SearchUsersTotal($queryText: String, $filterActiveSubscriptions: Boolean) {
		searchUsersTotal(queryText: $queryText, filterActiveSubscriptions: $filterActiveSubscriptions)
	}

	query SearchUsers(
		$queryText: String
		$filterActiveSubscriptions: Boolean
		$skip: Int
		$limit: Int
	) {
		searchUsers(
			queryText: $queryText
			filterActiveSubscriptions: $filterActiveSubscriptions
			skip: $skip
			limit: $limit
		) {
			id
			firstName
			lastName
			email
			telephone
			country
			blocked
			privileges {
				adminForCinemas {
					id
				}
			}
		}
	}
`;

export const useUserById = (id: ID) => {
	const { data } = useUserByIdSuspenseQuery({
		variables: { id },
	});
	return data!.userById! as User;
};

export const useSuspenseSearchUsers = ({
	variables,
}: {
	variables: {
		queryText?: string;
		filterActiveSubscriptions?: boolean;
		skip?: number;
		limit?: number;
	};
}) => {
	const { data: { searchUsers: users } = {} } = useSearchUsersSuspenseQuery({
		fetchPolicy: 'cache-first',
		variables,
	});

	const { data: { searchUsersTotal: count = 0 } = {} } = useSearchUsersTotalSuspenseQuery({
		fetchPolicy: 'cache-first',
		variables,
	});

	return {
		users,
		count,
	};
};
