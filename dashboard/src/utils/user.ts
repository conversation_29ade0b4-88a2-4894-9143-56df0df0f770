import { gql } from 'graphql-tag';
import cookies from 'js-cookie';
import client from '../apollo';
import { ID } from '@cinuru/utils/types';
import { TargetGroupFilterConfig } from '../screens/CinemaOperatingCompany/queries';
import { PrivilegesQuery, usePrivilegesSuspenseQuery } from '../gql/graphql';

gql`
	fragment cinemaOperatingCompanyFragment on CinemaOperatingCompany {
		id
		name
		accessRightDashboard
		accessRightFilmStatistics
		accessRightBonusProgram
		accessRightCampaigning
		cinemas {
			id
			name
		}
		associatedUsers {
			id
			fullName
			email
		}
	}

	fragment CinemaFragment on Cinema {
		id
		name
		accessRestrictedTo
		city
		clusters
	}

	fragment UserFragment on User {
		id
		name
		email
		privileges {
			accessRightDashboard
			accessRightFilmStatistics
			accessRightBonusProgram
			accessRightCampaigns
			adminForCinemas {
				...CinemaFragment
			}
			adminForBonusPrograms {
				id
			}
			adminRole
			rootRole
			supportRole
			belongsToCinemaOperatingCompanies {
				...cinemaOperatingCompanyFragment
			}
		}
	}

	query Privileges {
		user: currentUser {
			...UserFragment
		}
	}
`;

export type CinemaOperatingCompany = {
	id: ID;
	name: string;
	accessRightDashboard: boolean;
	accessRightFilmStatistics: boolean;
	accessRightBonusProgram: boolean;
	accessRightCampaigning: boolean;
	cinemas?: {
		id: string;
		name: string;
	}[];
	associatedUsers?: {
		id: string;
		fullName?: string;
		email: string;
	}[];
	targetGroupFilterConfig?: TargetGroupFilterConfig[];
	pricingScheme?: string;
	pricingLevel?: string;
	externalPhoneNumber?: string;
};

export type Privileges = NonNullable<NonNullable<PrivilegesQuery['user']>['privileges']>;

export const useCurrentUser = () => {
	const { data } = usePrivilegesSuspenseQuery({
		fetchPolicy: 'cache-first',
	});
	return data!.user!;
};

export const useUserPrivileges = () => {
	return useCurrentUser().privileges;
};

export const logout = async () => {
	await client.mutate({
		mutation: gql`
			mutation Logout {
				logout {
					success
				}
			}
		`,
	});
	client.stop();
	client.resetStore();
	cookies.remove('_csrf');
	window.location.href = '/';
};
