/* eslint-disable react-perf/jsx-no-new-function-as-prop */
import React, { useState, useCallback, useMemo } from 'react';
import { Box, Chip, Select, MenuItem, ListItemText, Checkbox, Typography } from '@mui/material';
import FormControlWrapper from './FormControlWrapper';
import styled from 'styled-components';
import { useUserPrivileges } from '../utils/user';
import { difference, groupBy, uniq } from 'lodash';
import TextField from './TextField';

const DropdownContainer = styled(Box)`
	display: flex;
	justify-content: space-between;
	max-height: 400px;
`;

const FiltersContainer = styled(Box)`
	display: flex;
	flex-direction: column;
	padding: 2rem;
	border-right: 1px solid #ddd;
	width: 20%;
`;

const CinemasContainer = styled(Box)`
	display: flex;
	padding: 2rem;
	flex-direction: column;
	flex-grow: 1;
	overflow-y: auto;
	width: 100%;
`;

const FixedHeightSelect = styled(Select)`
	.MuiSelect-select {
		height: 1.4rem;
		overflow-y: auto;
	}
`;

interface CinemaSelectFieldProps {
	multi?: boolean;
	value?: string[];
	onChange?: (value: string[]) => void;
	label?: string;
	disabled?: boolean;
	m?: string;
	error?: boolean;
}

const emptyArray = [];

const SideEntry = ({
	checked,
	label,
	onClick,
}: {
	checked: boolean;
	label: string;
	onClick: () => any;
}) => (
	<Box
		onClick={(ev) => {
			ev.stopPropagation();
			onClick();
		}}
		// eslint-disable-next-line react-perf/jsx-no-new-object-as-prop
		sx={{
			cursor: 'pointer',
			padding: '4px 8px',
			backgroundColor: checked ? '#eee' : 'transparent',
			borderRadius: 1,
			marginBottom: 1,
		}}
	>
		<Typography variant="body2" fontWeight={checked ? 'bold' : 'normal'}>
			{label}
		</Typography>
	</Box>
);

const CinemaSelectField: React.FC<CinemaSelectFieldProps> = ({
	multi = false,
	value = emptyArray,
	onChange,
	label,
	disabled,
	error,
	m,
}) => {
	const [selectedCluster, setselectedCluster] = useState<string | undefined>(undefined);
	const [search, setSearch] = useState('');

	const handleCinemaChange = useCallback(
		(newValue) => {
			onChange?.(newValue);
		},
		[onChange]
	);

	const { adminForCinemas } = useUserPrivileges();

	const clusters = useMemo(() => uniq(adminForCinemas.flatMap((c) => c.clusters)), [
		adminForCinemas,
	]);

	const cinemasByCluster = useMemo(() => groupBy(adminForCinemas, 'clusters'), [adminForCinemas]);

	const filteredCinemas = useMemo(
		() =>
			(cinemasByCluster[selectedCluster || ''] || []).filter((c) =>
				search?.trim()
					? c.name?.toLowerCase().includes(search.trim().toLowerCase()) ||
					  c.city?.toLowerCase().includes(search.trim().toLowerCase())
					: true
			),
		[adminForCinemas, selectedCluster, search]
	);

	return (
		<FormControlWrapper m={m} label={label || 'Kinos in Vergleichsgruppe'}>
			<FixedHeightSelect
				label={label || 'Kinos in Vergleichsgruppe'}
				multiple={multi}
				value={value}
				disabled={disabled}
				error={error}
				readOnly={disabled}
				renderValue={(selected: any) => (
					<Box display="flex" flexWrap="wrap" gap={0.5}>
						{selected
							.map((id) => adminForCinemas.find((c) => c.id === id))
							.filter(Boolean)
							.map((cinema) => (
								<Chip key={cinema.id} label={cinema.name} />
							))}
					</Box>
				)}
			>
				<TextField
					placeholder={'Suchen...'}
					value={search}
					variant={'filled'}
					onChange={(e) => setSearch(e)}
				/>
				<DropdownContainer>
					{/* Filters on the Left */}
					<FiltersContainer>
						<Typography variant="subtitle1" fontWeight="bold" mb={1}>
							Filter
						</Typography>
						{multi && (
							<SideEntry
								onClick={() => handleCinemaChange(filteredCinemas.map((c) => c.id))}
								label={'Alle'}
								checked={
									!!filteredCinemas.length &&
									filteredCinemas.length === value.length &&
									filteredCinemas.every((c) => value.includes(c.id))
								}
							/>
						)}
						<SideEntry
							onClick={() =>
								handleCinemaChange(
									difference(
										value,
										filteredCinemas.map((c) => c.id)
									)
								)
							}
							label={'Keine'}
							checked={value.length === 0}
						/>
						{clusters.map((cluster) => (
							<SideEntry
								key={cluster}
								onClick={() =>
									setselectedCluster((prev) => (prev === cluster ? undefined : cluster))
								}
								checked={selectedCluster === cluster}
								label={cluster}
							/>
						))}
					</FiltersContainer>

					{/* Cinema List on the Right */}
					<CinemasContainer>
						{filteredCinemas.map((cinema) => {
							const isChecked = value.indexOf(cinema.id) > -1;

							return (
								<MenuItem
									key={cinema.id}
									value={cinema.id}
									onClick={() => {
										const newValue = isChecked
											? value.filter((id) => id !== cinema.id)
											: [...value, cinema.id];

										handleCinemaChange(multi ? newValue : newValue.slice(-1));
									}}
								>
									<Checkbox checked={isChecked} />
									<ListItemText primary={cinema.name} secondary={cinema.city} />
								</MenuItem>
							);
						})}
					</CinemasContainer>
				</DropdownContainer>
			</FixedHeightSelect>
		</FormControlWrapper>
	);
};

export default CinemaSelectField;
