import React, { forwardRef } from 'react';
import { AppBar, Box, IconButton as MuiIconButton, Toolbar, Typography } from '@mui/material';
import styled, { useTheme } from 'styled-components';
import { useStickyHeaderWrapperProps } from '../utils/useStickyHeaderWrapper';
import { Menu } from '@mui/icons-material';
import Button from './Button';
import { unstable_usePrompt as usePrompt } from 'react-router-dom';
import IconButton from './IconButton';

const Background = styled(Box)`
	height: 100%;
	width: 100%;
	padding: 0 1rem;
	position: absolute;
	display: flex;
	justify-content: center;
	align-items: center;
`;

const StyledTypography = styled(Typography)`
	color: ${(p) => p.theme.palette.common.white};
`;

type AppToolBarProps = {
	drawerWidth: string;
	shouldCollapse: boolean;
	toggleMenu: () => void;
};

const StyledAppBar = styled(AppBar)
	.withConfig({ shouldForwardProp: (prop) => !prop.includes('drawerWidth') })
	.attrs({ position: 'fixed' })<{ drawerWidth: number | string }>`
	position: fixed;
	margin-left: ${(props) => props.drawerWidth} !important;
	width: calc(100% - ${(props) => props.drawerWidth}) !important;
`;

const StyledToolbar = styled(Toolbar).attrs({ sx: { padding: '0 !important' } })``;

const StyledIconButton = styled(MuiIconButton)`
	margin-right: 2;
`;

const AppToolBar: React.FC<AppToolBarProps> = ({ drawerWidth, shouldCollapse, toggleMenu }) => {
	const theme = useTheme();

	const stickyWrapperProps = useStickyHeaderWrapperProps();

	usePrompt({
		when: !!(stickyWrapperProps.showWarningOnLeave && stickyWrapperProps.warningOnLeave),
		message: stickyWrapperProps.warningOnLeave!,
	});

	return (
		<StyledAppBar drawerWidth={drawerWidth}>
			<StyledToolbar>
				<Background bgcolor={theme.customColors.accentBlue}>
					{shouldCollapse && (
						<StyledIconButton color="inherit" aria-label="open drawer" onClick={toggleMenu}>
							<Menu htmlColor={theme.customColors.white} />
						</StyledIconButton>
					)}
					<Box
						// border="1px solid red"
						m="0 1rem"
						display="flex"
						flexDirection="row"
						justifyContent="space-between"
						alignItems="center"
						width="100%"
					>
						<StyledTypography variant="h6">{stickyWrapperProps.label}</StyledTypography>
						<Box display="flex" flexDirection="row">
							{stickyWrapperProps.buttons?.map(
								({
									label: buttonLabel,
									onClick,
									disabled: buttonDisabled,
									loading,
									loadingText,
									startIconName,
									endIconName,
									collapsedIconName,
								}) =>
									shouldCollapse ? (
										<IconButton
											key={buttonLabel}
											m="0 0 0 1rem"
											size="small"
											color={theme.customColors.white}
											disabled={loading || stickyWrapperProps.disabled || buttonDisabled}
											onClick={onClick}
											loading={loading}
											iconName={collapsedIconName}
										/>
									) : (
										<Button
											key={buttonLabel}
											m="0 0 0 1rem"
											variant="textButton"
											startIconName={startIconName}
											endIconName={endIconName}
											disabled={loading || stickyWrapperProps.disabled || buttonDisabled}
											onClick={onClick}
											loading={loading}
											loadingText={loadingText || '...'}
											loadingVariant="disabledTextButton"
										>
											{buttonLabel}
										</Button>
									)
							)}
						</Box>
					</Box>
				</Background>
			</StyledToolbar>
		</StyledAppBar>
	);
};

export default AppToolBar;
