import styled from 'styled-components';
import { NavLink } from 'react-router-dom';
import CinfinityLogo from '../images/cinfinitylogo.svg';

import { transparentize } from '../utils/color';
import { brand, FEATURES } from '../consts';
import { Box, Typography } from '@mui/material';
import theme from '../utils/theme';
import CinuruLogoSVG from '../images/cinuru-logo.svg';
import Icon from './Icon';
import { NavMenuEntries, NavMenuEntryCore } from '../routes';
import { logout, useUserPrivileges } from '../utils/user';
import { forwardRef, useMemo } from 'react';

const Wrapper = styled(Box)`
	background: ${(p) => p.theme.palette.primary.main};
	height: 100vh;
	align-items: center;
	min-width: 22rem;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: stretch;
	overflow: auto;
	z-index: 4;
	@media print {
		display: none;
	}
	&::-webkit-scrollbar {
		display: none;
	}
	@-moz-document url-prefix() {
		overflow-y: auto;
	}
`;

const LogoWrapper = styled(Box)`
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 0 2.5rem;
	margin: 2.2rem 0;
	gap: 10px;
`;

const SectionWrapper = styled(Box)`
	display: flex;
	flex-direction: column;
	margin: 2rem 0;
`;

const SectionTitleWrapper = styled(Box)`
	display: flex;
	padding: 0 2.5rem;
	flex-direction: row;
	align-items: center;
	height: 3rem;
	opacity: 0.85;
`;

const Link = styled(NavLink)`
	text-decoration: none;
	padding: 0.5rem 2.5rem;
	width: 100%;
	color: ${(p) => p.theme.palette.common.white};
	font-weight: 600;

	&:hover,
	&:active,
	&:focus {
		color: ${(p) => p.theme.palette.common.white};
		background-color: ${(p) => transparentize(0.85, p.theme.palette.common.white)};
	}

	&.active {
		background: ${(p) => transparentize(0.85, p.theme.palette.common.white)};
	}
`;

const Spacer = styled(Box)`
	flex: 1;
`;

const LogoutButton = styled(Box)`
	padding: 1.5rem 0;
	cursor: pointer;
`;

const Logo = () => {
	switch (brand) {
		case 'CINFINITY':
		case 'CINFINITY-WEB':
			return <img width={'100%'} src={CinfinityLogo} />;
		default:
			return (
				<>
					<Icon size="4.5rem" name="logo" color="$accentText0" margin="0rem -0.25rem" />
					<img
						src={CinuruLogoSVG}
						alt="Cinuru Logo"
						height="20px"
						color={theme.colorScheme.accentText0}
					/>
				</>
			);
	}
};

const NavMenu = forwardRef<any, { onItemClick: () => void }>(({ onItemClick }) => {
	const privileges = useUserPrivileges();

	const filterEntries = <T extends Partial<NavMenuEntryCore>>(section: T) =>
		(!section.brand?.length || section.brand.includes(brand)) &&
		(!section.features?.length || section.features.every((f) => FEATURES.includes(f))) &&
		(!section.requiredPrivileges?.length ||
			section.requiredPrivileges.every((p) => !!(privileges && privileges[p])));

	const finalEntries = useMemo(
		() =>
			NavMenuEntries.map((e) => ({ ...e, subEntries: e.subEntries?.filter(filterEntries) }))
				.filter((e) => e.subEntries?.length)
				.filter(filterEntries),
		[privileges]
	);

	return (
		<>
			<LogoWrapper>
				<Logo />
			</LogoWrapper>
			<Wrapper>
				{finalEntries.map((section, index) => (
					<SectionWrapper key={index}>
						<SectionTitleWrapper>
							<Icon
								name={section.iconName}
								size="2rem"
								margin="0 0.5rem 0 -0.25rem"
								color="$accentText0"
							/>
							<Typography
								variant="body2"
								fontSize="13px"
								fontWeight={700}
								color={theme.colorScheme.accentText0}
							>
								{section.label.toUpperCase()}
							</Typography>
						</SectionTitleWrapper>
						{section.subEntries?.filter(filterEntries).map(({ path, label }, subIndex) => (
							<Link onClick={onItemClick} key={subIndex} to={path}>
								<Typography
									variant="body2"
									fontSize="13px"
									fontWeight={600}
									color={theme.colorScheme.accentText0}
									lineHeight="2rem"
								>
									{label}
								</Typography>
							</Link>
						))}
					</SectionWrapper>
				))}
			</Wrapper>
			<Spacer />
			<LogoutButton onClick={logout}>
				<SectionTitleWrapper>
					<Icon name="logOut" size="2rem" margin="0rem 0.5rem 0rem -0.25rem" color="$accentText0" />
					<Typography variant="body2" fontWeight={700} color={theme.colorScheme.accentText0}>
						AUSLOGGEN
					</Typography>
				</SectionTitleWrapper>
			</LogoutButton>
		</>
	);
});

export default NavMenu;
