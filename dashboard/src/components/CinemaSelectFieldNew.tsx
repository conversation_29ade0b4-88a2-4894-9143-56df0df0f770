import { ID } from '@cinuru/utils/types';
import { TextFieldProps } from '@mui/material/TextField';
import * as React from 'react';

import { useUserPrivileges } from '../utils/user';
import SearchSelectField, { SelectFieldItem } from './SearchSelectField';
import TextField from './TextField';

export type CinemaSelectFieldNewRef = {
	validate: () => boolean;
};

/**
 * @deprecated Use SplitCinemaSelectFieldNew instead
 */
const CinemaSelectFieldNew2 = React.forwardRef<
	CinemaSelectFieldNewRef,
	{
		label: string;
		onChange?: (movies: { id: ID; name: string }[]) => void;
		multi?: boolean;
		variant?: TextFieldProps['variant'];
		defaultIds?: ID[] | null;
		defaultMovieIds?: ID[];
		m?: string;
		disabled?: boolean;
		width?: string;
		onCinemasFetched?: (cinemas: { id: ID; name: string; accessRestrictedTo: string }[]) => void;
		allCinemas?: boolean;
	}
>(
	(
		{
			label,
			onChange,
			multi,
			variant = 'outlined',
			defaultIds,
			m,
			disabled,
			width,
			onCinemasFetched,
			allCinemas,
		},
		ref
	) => {
		const viewerPrivileges = useUserPrivileges();
		const availableCinemas = viewerPrivileges?.adminForCinemas;

		React.useEffect(() => {
			if (availableCinemas && onCinemasFetched) {
				const transformed = availableCinemas.map((cinema) => ({
					id: cinema.id as ID,
					name: cinema.name,
					accessRestrictedTo: cinema.accessRestrictedTo,
				}));
				onCinemasFetched(transformed);
			}
		}, [availableCinemas, onCinemasFetched]);

		const handleChange = React.useCallback(
			(items: SelectFieldItem[]) => {
				const transformed = items.map((item) => ({ id: item.value as ID, name: item.label }));
				onChange?.(transformed);
			},
			[onChange]
		);

		const transformed = React.useMemo(
			() =>
				availableCinemas?.map((cinema) => ({
					label: cinema.name,
					value: cinema.id as string,
				})),
			[availableCinemas]
		);

		const defaultItems = React.useMemo(() => {
			const cinemasToInclude = allCinemas
				? availableCinemas
				: (availableCinemas ?? []).filter((cinema) => (defaultIds ?? []).includes(cinema.id));

			const items = cinemasToInclude?.map(({ id, name }) => ({
				label: name,
				value: id as string,
			}));

			// Trigger onChange when allCinemas is true and availableCinemas is populated
			if (allCinemas && availableCinemas && items?.length) {
				handleChange(items);
			}

			return items;
		}, [availableCinemas, defaultIds, allCinemas, handleChange]);

		return !availableCinemas ? (
			<TextField label={label} variant={variant} m={m} isLoading />
		) : (
			<SearchSelectField
				allItems={transformed}
				label={label}
				onChange={handleChange}
				multi={multi}
				variant={variant}
				defaultItems={defaultItems}
				m={m}
				ref={ref}
				disabled={disabled}
				width={width}
			/>
		);
	}
);

export default CinemaSelectFieldNew2;
