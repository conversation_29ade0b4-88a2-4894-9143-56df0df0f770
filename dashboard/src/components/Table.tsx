import * as React from 'react';
import styled from 'styled-components';

import {
	Table as MuiTable,
	TableBody,
	TableCell,
	TableContainer,
	TableHead as MuiTableHead,
	TablePagination,
	TableRow as MuiTableRow,
	Checkbox,
	Chip,
	Box,
} from '@mui/material';

import Tooltip from './Tooltip';

import fuzzySearch from '@cinuru/utils/fuzzySearch';
import type { ID } from '@cinuru/utils/types';

import Button from './Button';
import SearchField from './SearchField';

const Row = styled(Box)`
	display: flex;
	align-items: center;
	flex-flow: row wrap;
`;

const fuseSearchOptions = {
	shouldSort: true,
	includeScore: true,
	threshold: 0.2,
};

export type Actions<ActionType> = {
	allActions: {
		type: ActionType;
		label: string;
	}[];
	getPossibleActionTypes: (rowIds?: ID[], rowItems?: RowItem[]) => string[];
};

type ItemControlProps<ActionType extends string> = {
	selectedItemIds: ID[];
	items: RowItem[];
	onHandleAction?: (rowAction: ActionType, itemIds: ID[]) => void;
	onHandleSearchItem?: (query: string) => void;
	label?: string;
	actions?: Actions<ActionType>;
};

export const mapColumns = (arr: (string | null | undefined | false)[]): Array<ColumnItem> =>
	arr
		.filter((x) => typeof x === 'string')
		.map((label, idx) => ({
			label,
			id: idx,
		}));

const TableControl = <ActionType extends string>({
	selectedItemIds,
	items,
	onHandleAction,
	onHandleSearchItem,
	actions,
	isSearchable = true,
}: ItemControlProps<ActionType> & {
	isSearchable?: boolean;
}) => {
	const handleRowAction = React.useCallback(
		(rowAction: ActionType) => {
			onHandleAction?.(rowAction, selectedItemIds);
		},
		[onHandleAction, selectedItemIds]
	);

	const possibleActionTypes = React.useMemo(
		() => actions?.getPossibleActionTypes?.(selectedItemIds, items),
		[actions, items, selectedItemIds]
	);

	return (
		<Row justifyContent="space-between" m="0rem 0rem 3rem">
			{isSearchable ? <SearchField onChange={onHandleSearchItem} /> : null}
			<Row>
				{actions?.allActions.map(({ label: actionLabel, type }) => (
					<Button
						m="0 0.5rem 0 0"
						key={type}
						onClick={handleRowAction}
						id={type}
						variant="outlined"
						disabled={!selectedItemIds.length || !possibleActionTypes?.includes(type)}
					>
						{actionLabel}
					</Button>
				))}
			</Row>
		</Row>
	);
};

const TableHead = ({
	onSelectAllClick,
	numSelected,
	rowCount,
	columns,
	isSelectable,
}: {
	onSelectAllClick: (event: any) => void;
	numSelected: number;
	rowCount: number;
	columns: ColumnItem[];
	isSelectable?: boolean;
}) => {
	return (
		<MuiTableHead>
			<MuiTableRow>
				{isSelectable ? (
					<TableCell padding="checkbox">
						<Checkbox
							indeterminate={numSelected > 0 && numSelected < rowCount}
							checked={rowCount > 0 && numSelected === rowCount}
							onChange={onSelectAllClick}
						/>
					</TableCell>
				) : null}
				{columns.map((column) => (
					<TableCell key={column.id} align="left">
						<Box display="flex" alignItems="center">
							{column.label}
							{column.toolTip ? <Tooltip text={column.toolTip} /> : null}
						</Box>
					</TableCell>
				))}
			</MuiTableRow>
		</MuiTableHead>
	);
};

const StyledChip = styled(Chip).attrs((p) => ({
	size: 'small',
	color: p.color || 'primary',
}))<{ color?: string }>`
	margin-right: 0.5rem;
`;

export type ColumnItem = {
	id: number;
	label: string;
	toolTip?: string;
};

export type RowItem = {
	id: ID;
	data: (
		| {
				text: React.ReactNode;
		  }
		| {
				text?: React.ReactNode;
				chips: string[];
		  }
		| {
				buttonLabel: string;
				onPress: () => void;
		  }
	)[];
	rawData: any;
};

const TableRow = ({
	item,
	selected,
	onSelectItem,
	isSelectable,
}: {
	item: RowItem;
	selected?: boolean;
	onSelectItem: (itemId: ID) => void;
	isSelectable?: boolean;
}) => {
	const handleSelectItem = React.useCallback(() => {
		onSelectItem?.(item.id);
	}, [onSelectItem, item.id]);

	return (
		<MuiTableRow hover role="checkbox" aria-checked={selected} tabIndex={-1} key={item.id}>
			{isSelectable ? (
				<TableCell padding="checkbox">
					<Checkbox onClick={handleSelectItem} checked={selected} color="primary" />
				</TableCell>
			) : null}
			{item.data.map((row, index) => {
				return 'text' in row ? (
					<TableCell key={index}>{row.text}</TableCell>
				) : 'chips' in row ? (
					<TableCell key={index}>
						<Row>
							<Box display="flex" flexDirection="column">
								{row.text ? <Row>{row.text}</Row> : null}
								<Row>
									{row.chips.map((chipLabel) => (
										<StyledChip key={chipLabel} label={chipLabel} color={'primary'} />
									))}
								</Row>
							</Box>
						</Row>
					</TableCell>
				) : 'buttonLabel' in row ? (
					<TableCell align="left" key={index}>
						<Button variant="outlined" onClick={row.onPress}>
							{row.buttonLabel}
						</Button>
					</TableCell>
				) : (
					<TableCell key={index} />
				);
			})}
		</MuiTableRow>
	);
};

const MinWidthTable = styled(MuiTable)`
	min-width: 750px;
`;

const defaultRowsPerPageOptions = [5, 10, 25];

const StyledTableRow = styled(MuiTableRow)<{ $emptyRows: number }>`
	height: ${(p) => p.$emptyRows * 53}px;
`;

type TableProps<ActionType extends string> = {
	label?: string;
	allRows: RowItem[];
	columns: ColumnItem[];
	rowsPerPageOptions?: number[];
	onHandleAction?: (value: string, itemIds: ID[]) => void;
	searchPath?: string;
	actions?: Actions<ActionType>;
	count?: number;
	defaultPage?: number;
	onPageChange?: (page: number) => any;
	isSearchable?: boolean;
	isSelectable?: boolean;
	defaultRowsPerPage?: 5 | 10 | 25;
	onDefaultRowsPerPageChange?: (limit: number) => any;
	m?: string;
};

const StyledTablePagination = styled(TablePagination).attrs({
	component: 'div',
})`
	.MuiTablePagination-selectLabel {
		margin: 0;
	}
	.MuiTablePagination-displayedRows {
		margin: 0;
	}
`;

const labelDisplayedRows = ({ from, to, count }) =>
	`${from}–${to} von ${count !== -1 ? count : `mehr als ${to}`}`;

// renders tablecontrols, tablehead, tablebody and tablepagination
const Table: React.FC<TableProps<string>> = ({
	label = '',
	allRows,
	columns,
	rowsPerPageOptions = defaultRowsPerPageOptions,
	onHandleAction,
	searchPath = 'data.text',
	actions,
	isSearchable = true,
	isSelectable = true,
	defaultPage,
	onPageChange,
	count = allRows.length,
	defaultRowsPerPage = 10,
	onDefaultRowsPerPageChange,
	m,
}) => {
	const [selectedRowIds, setSelectedRowIds] = React.useState<ID[]>([]);
	const [searchedRows, setSearchedRows] = React.useState<RowItem[] | undefined>(undefined);

	const [page, setPage] = React.useState(0);
	const [rowsPerPage, setRowsPerPage] = React.useState(defaultRowsPerPage);

	const currentRows = React.useMemo(() => searchedRows || allRows, [allRows, searchedRows]);

	React.useEffect(() => {
		if (defaultPage != null) {
			handleChangePage(defaultPage);
		}
	}, [defaultPage]);

	const handleSearchItem = React.useCallback(
		(query) => {
			const sanitizedQuery = query.trim();
			if (sanitizedQuery === '') {
				setSearchedRows(undefined);
			} else {
				const searchResults = fuzzySearch(
					allRows,
					sanitizedQuery,
					searchPath,
					fuseSearchOptions,
					true
				).map(({ item }) => item);
				setSearchedRows(searchResults);
			}
		},
		[allRows, searchPath]
	);

	const handleSelectAllRows = React.useCallback(
		(event) => {
			const checked = event.target.checked;
			checked ? setSelectedRowIds(currentRows.map((n) => n.id)) : setSelectedRowIds([]);
		},
		[currentRows]
	);

	const handleSelectItem = React.useCallback((id: ID) => {
		setSelectedRowIds((b) => {
			return b.includes(id) ? b.filter((i) => i !== id) : [...b, id];
		});
	}, []);

	const handleChangePage = React.useCallback((newPage) => {
		setPage(newPage);
		onPageChange?.(newPage);
	}, []);

	const handleChangeRowsPerPage = React.useCallback((event) => {
		const newLimit = parseInt(event.target.value, 10) as any;
		setRowsPerPage(newLimit);
		onDefaultRowsPerPageChange?.(newLimit);
		handleChangePage(0);
	}, []);
	const emptyRows = React.useMemo(
		() => rowsPerPage - Math.min(rowsPerPage, currentRows.length - page * rowsPerPage),
		[currentRows.length, page, rowsPerPage]
	);

	React.useEffect(() => {
		setSelectedRowIds((prev) => prev.filter((row) => allRows.some((r) => r.id === row)));
	}, [allRows]);

	return (
		<Box width="100%" m={m}>
			<TableControl
				label={label}
				selectedItemIds={selectedRowIds}
				items={currentRows}
				onHandleAction={onHandleAction}
				onHandleSearchItem={handleSearchItem}
				actions={actions}
				isSearchable={isSearchable}
			/>
			<TableContainer>
				<MinWidthTable size="medium">
					<TableHead
						numSelected={selectedRowIds.length}
						onSelectAllClick={handleSelectAllRows}
						rowCount={currentRows.length}
						columns={columns}
						isSelectable={isSelectable}
					/>
					<TableBody>
						{currentRows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((item) => (
							<TableRow
								key={item.id}
								item={item}
								selected={selectedRowIds.includes(item.id)}
								onSelectItem={handleSelectItem}
								isSelectable={isSelectable}
							/>
						))}
						{emptyRows > 0 && (
							<StyledTableRow $emptyRows={emptyRows}>
								<TableCell colSpan={6} />
							</StyledTableRow>
						)}
					</TableBody>
				</MinWidthTable>
			</TableContainer>
			<StyledTablePagination
				rowsPerPageOptions={rowsPerPageOptions}
				count={count}
				rowsPerPage={rowsPerPage}
				page={page}
				onPageChange={(_, page) => handleChangePage(page)}
				onRowsPerPageChange={handleChangeRowsPerPage}
				labelRowsPerPage="Zeilen pro Seite"
				labelDisplayedRows={labelDisplayedRows}
			/>
		</Box>
	);
};

export default Table;
