import React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import Txt from './Txt';

export const ErrorBoundaryDefault: React.FC<{
	children: React.ReactNode;
	resetKeys?: any[];
}> = ({ children, resetKeys }) => {
	const fallbackRender = React.useCallback(
		({ error }) => (error ? <Txt color="error">{error.message}</Txt> : null),
		[]
	);

	return (
		<ErrorBoundary resetKeys={resetKeys} fallbackRender={fallbackRender}>
			{children}
		</ErrorBoundary>
	);
};
export const withDefaultErrorBoundary = <T extends Record<string, any>>(
	Component: React.FC<T>,
	displayName?: string
): React.FC<T> => {
	const WrappedComponent = (props: T) => {
		return (
			<ErrorBoundaryDefault>
				<Component {...props} />
			</ErrorBoundaryDefault>
		);
	};

	if (displayName) {
		WrappedComponent.displayName = displayName;
	}

	return WrappedComponent;
};
