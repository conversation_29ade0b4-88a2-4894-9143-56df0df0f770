import * as React from 'react';

import AddMovie from './AddMovie';
import ShowMovies from './ShowMovies';
import EditMovie from './EditMovie';
import Dialog from '../../components/Dialog';
import 'dayjs/locale/de';
import ContentWrapper from '../../components/ContentWrapper';
import CinemaSelectField from '../../components/SplitCinemaSelectFieldNew';

const EditSoonInCinema = () => {
	const [cinemaId, setCinemaId] = React.useState(null);

	const openAddDialog = React.useCallback(() => {
		Dialog.render({
			noDismiss: true,
			renderContent: ({ dismissPortal }) => (
				<AddMovie dismissPortal={dismissPortal} cinemaId={cinemaId} />
			),
		});
	}, [cinemaId]);

	const openEditDialog = React.useCallback(
		(item) => {
			Dialog.render({
				renderContent: ({ dismissPortal }) => (
					<EditMovie dismissPortal={dismissPortal} cinemaId={cinemaId} item={item} />
				),
			});
		},
		[cinemaId]
	);

	const handleCinemaChange = React.useCallback((cinema) => {
		setCinemaId(cinema[0]);
	}, []);

	const value = cinemaId ? [cinemaId] : [];

	return (
		<React.Fragment>
			<ContentWrapper>
				<CinemaSelectField label={`Kino wählen`} value={value} onChange={handleCinemaChange} />
			</ContentWrapper>
			{cinemaId ? (
				<ShowMovies
					cinemaId={cinemaId}
					openAddDialog={openAddDialog}
					openEditDialog={openEditDialog}
				/>
			) : null}
		</React.Fragment>
	);
};

export default EditSoonInCinema;
