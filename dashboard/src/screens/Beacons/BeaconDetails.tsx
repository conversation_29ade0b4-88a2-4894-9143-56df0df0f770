import React, { useCallback } from 'react';
import Table, { mapColumns } from '../../components/Table';
import SectionWrapper2 from '../../components/SectionWrapper2';
import { deleteBeacon, useBeacons } from '../../utils/beacon';
import { orderBy } from 'lodash';
import { ID } from 'utils/types';
import { useNavigate } from 'react-router';
import Dialog from '../../components/Dialog';
import { enqueueSnackbar } from 'notistack';
import Txt from '../../components/Txt';
import { useStickyHeaderWrapper } from '../../utils/useStickyHeaderWrapper';
import { ErrorBoundary } from 'react-error-boundary';
import { IconName } from '../../components/Button';

const columns = mapColumns([
	'Kino',
	'InstanceId',
	'Hinweise / Ort',
	'Letz<PERSON> Scan',
	'<PERSON>zter Batteriewechsel',
	'',
	'',
]);

const BeaconDetails: React.FC = () => {
	const navigate = useNavigate();
	const { beacons, refetch } = useBeacons();

	const handleEditBeacon = useCallback(
		(id: ID) => () => {
			navigate(`/admin/beacons/details/${id}`);
		},
		[]
	);

	const deleteBeaconHandler = useCallback(async (beaconId) => {
		const result = await deleteBeacon(beaconId);
		if (result.success) {
			enqueueSnackbar('Beacon erfolgreich gelöscht', {
				variant: 'success',
			});
			refetch();
		} else {
			enqueueSnackbar(
				`Fehler beim Löschen des Beacons: ${result.error ? result.error : 'Unbekannte Fehler'}`,
				{ variant: 'error' }
			);
		}
	}, []);

	const handleDeleteBeacon = React.useCallback(
		(beaconId: ID) => async () => {
			Dialog.render({
				title: 'Beacon löschen',
				description: `Möchten Sie den Beacon wirklich löschen?`,
				buttons: [
					{
						id: `cancel-button-1-${beaconId}`,
						label: 'Abbrechen',
					},
					{
						id: `cancel-button-2-${beaconId}`,
						label: 'Beacon löschen',
						onClick: () => deleteBeaconHandler(beaconId),
					},
				],
			});
		},
		[deleteBeaconHandler]
	);

	const rows = React.useMemo(
		() =>
			orderBy(
				beacons.map((beacon) => ({
					id: beacon.id,
					rawData: beacon,
					cinema: beacon.cinema.name,
					data: [
						{
							text: String(beacon.cinema.name),
						},
						{ text: String(beacon.instanceId) },
						{ text: beacon.comments ? String(beacon.comments) : '-' },
						{
							text: beacon.lastScan
								? new Date(beacon.lastScan).toLocaleString('de-DE', {
										dateStyle: 'long',
										timeStyle: 'short',
								  })
								: '-',
						},
						{
							text: beacon.lastBatteryChange
								? new Date(beacon.lastBatteryChange).toLocaleString('de-DE', {
										dateStyle: 'long',
								  })
								: '-',
						},
						{
							buttonLabel: 'Bearbeiten',
							onPress: handleEditBeacon(beacon.id),
						},
						{
							buttonLabel: 'Löschen',
							onPress: handleDeleteBeacon(beacon.id),
						},
					],
				})),
				['cinema'],
				['asc']
			),
		[beacons, handleEditBeacon, handleDeleteBeacon]
	);

	return (
		<Table
			columns={columns}
			label={'Beacons'}
			allRows={rows}
			isSelectable={false}
			isSearchable={false}
		/>
	);
};

const fallbackRender = () => (
	<Txt>Keine Beacons vorhanden. Erstellen Sie einen neuen Beacon, um zu starten.</Txt>
);

const BeaconDetailsPage: React.FC = () => {
	const navigate = useNavigate();

	const handleCreateBeacon = useCallback(() => {
		navigate(`/admin/beacons/new`);
	}, []);

	const stickyHeaderProps = React.useMemo(
		() => ({
			maxContentWidth: '130rem',
			label: 'Beacons',
			buttons: [
				{
					label: 'Beacon erstellen',
					startIconName: 'Add' as IconName,
					onClick: handleCreateBeacon,
					collapsedIconName: 'Add' as IconName,
				},
			],
		}),
		[handleCreateBeacon]
	);

	useStickyHeaderWrapper(stickyHeaderProps);
	return (
		<SectionWrapper2 p="3rem">
			<ErrorBoundary fallbackRender={fallbackRender}>
				<SectionWrapper2 p="3rem">
					<BeaconDetails />
				</SectionWrapper2>
			</ErrorBoundary>
		</SectionWrapper2>
	);
};

export default BeaconDetailsPage;
