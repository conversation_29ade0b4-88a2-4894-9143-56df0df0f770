import React from 'react';
import { useNavigate, useParams } from 'react-router';
import Box from '@mui/material/Box';
import TextField from '../../components/TextField';
import SectionWrapper2 from '../../components/SectionWrapper2';
import Txt from '../../components/Txt';

import { Beacon, updateBeacon, createBeacon, useBeaconById } from '../../utils/beacon';
import { ID } from 'utils/types';
import Button from '../../components/Button';
import { BEACONS_UUID } from '../../consts';
import DateSelectField from '../../components/DateSelectField';
import { dateToString } from '../../utils/time';
import { enqueueSnackbar } from 'notistack';
import { useStickyHeaderWrapper } from '../../utils/useStickyHeaderWrapper';
import { withErrorBoundary } from 'react-error-boundary';
import CinemaSelectField from '../../components/SplitCinemaSelectFieldNew';

const EditBeaconRender = ({ beacon, isNew }: { beacon?: Beacon; isNew: boolean }) => {
	const navigate = useNavigate();
	const [loading, setLoading] = React.useState(false);
	const [formState, setFormState] = React.useState({
		cinemaId: beacon?.cinema?.id ?? '',
		comments: beacon?.comments ?? '',
		lastBatteryChange: beacon?.lastBatteryChange
			? dateToString(new Date(beacon.lastBatteryChange))
			: undefined,
	});

	const handleInputChange = React.useCallback(
		(name: string) => (value: string) => {
			setFormState((prevState) => ({
				...prevState,
				[name]: value,
			}));
		},
		[]
	);

	const handleDateChange = React.useCallback((date: string | undefined) => {
		setFormState((prevState) => ({
			...prevState,
			lastBatteryChange: date,
		}));
	}, []);

	const handleSelectCinemaId = React.useCallback((items: string[]) => {
		const newCinemaId = items.at(-1);

		setFormState((prevState) => ({
			...prevState,
			cinemaId: newCinemaId || '',
		}));
	}, []);

	const defaultIds = React.useMemo(() => (formState.cinemaId ? [formState.cinemaId] : []), [
		formState.cinemaId,
	]);

	const handleSaveBeacon = React.useCallback(async () => {
		setLoading(true);
		let result;

		if (isNew) {
			result = await createBeacon({
				...formState,
				lastBatteryChange: formState.lastBatteryChange
					? new Date(formState.lastBatteryChange)
					: undefined,
			});
		} else {
			result = await updateBeacon({
				beaconId: beacon!.id,
				...formState,
				lastBatteryChange: formState.lastBatteryChange
					? new Date(formState.lastBatteryChange)
					: undefined,
			});
		}

		if (result.success) {
			enqueueSnackbar(isNew ? 'Beacon erfolgreich erstellt' : 'Beacon erfolgreich aktualisiert', {
				variant: 'success',
			});
			if (isNew) navigate(`/admin/beacons/details/${result.beacon.id}`);
			else navigate(-1);
		} else {
			enqueueSnackbar(
				`Fehler beim ${isNew ? 'Erstellen' : 'Aktualisieren'} des Beacons: ${
					result.error ? result.error : 'Unbekannte Fehler'
				}`,
				{ variant: 'error' }
			);
		}
		setLoading(false);
	}, [isNew, beacon, formState, enqueueSnackbar]);

	return (
		<Box width="100%">
			<SectionWrapper2 p="2rem">
				<Box flexDirection="column" display="flex" gap="2rem" width="100%">
					{isNew ? (
						<CinemaSelectField
							label="Kino auswählen"
							onChange={handleSelectCinemaId}
							value={defaultIds}
						/>
					) : (
						<TextField
							name="cinema"
							value={beacon?.cinema?.name || ' '}
							label="Kino"
							fullWidth
							disabled
						/>
					)}
					{!isNew && (
						<>
							<TextField name="uuid" value={BEACONS_UUID} label="UUID" fullWidth disabled />
							<TextField
								name="instanceId"
								value={beacon?.instanceId || ' '}
								label="InstanceId"
								fullWidth
								disabled
							/>
						</>
					)}
					<TextField
						name="comments"
						value={formState.comments}
						onChange={handleInputChange('comments')}
						placeholder="z.B. links der Einganstür von Kino 3"
						label="Hinweise / Ort"
						fullWidth
					/>
					<DateSelectField
						label="Letzter Batteriewechsel"
						defaultDate={formState.lastBatteryChange}
						onChange={handleDateChange}
						variant={'outlined'}
						flex
					/>
					<Button
						m="2rem 0 0 0"
						onClick={handleSaveBeacon}
						label={isNew ? 'Erstellen' : 'Speichern'}
						loading={loading}
						variant="contained"
						disabled={!formState.cinemaId}
					/>
				</Box>
			</SectionWrapper2>
		</Box>
	);
};

const EditBeacon: React.FC = () => {
	const { id } = useParams<{ id?: string }>();
	const isNew = !id;
	const { data } = useBeaconById(id as ID);

	const stickyHeaderProps = React.useMemo(
		() => ({
			label: isNew ? 'Beacon erstellen' : 'Beacon bearbeiten',
			maxContentWidth: '130rem',
		}),
		[isNew]
	);
	useStickyHeaderWrapper(stickyHeaderProps);

	return <EditBeaconRender beacon={isNew ? undefined : data} isNew={isNew} />;
};
export default withErrorBoundary(EditBeacon, {
	fallbackRender: ({ error }) => <Txt color="error">{error.message}</Txt>,
});
