import React from 'react';
import styled, { useTheme } from 'styled-components';
import { format, startOfDay, startOfWeek } from 'date-fns';
import { de } from 'date-fns/locale';

import { Box } from '@mui/material';

import SectionWrapper2 from '../../components/SectionWrapper2';
import Txt from '../../components/Txt';
import ResponsiveBarChart from '../../components/ResponsiveBarChart';
import { NewSubscriptionStatisticsQuery, useNewSubscriptions } from '../../utils/statistics';
import CinemaSelectField from '../../components/SplitCinemaSelectFieldNew';
import { useWindowDimensions } from '../../utils/dimensions';
import { useStickyHeaderWrapper } from '../../utils/useStickyHeaderWrapper';
import { SuspenseDefault } from '../../components/SuspenseDefault';
import { ErrorBoundaryDefault } from '../../components/ErrorBoundaryDefault';
import { useNavigate } from 'react-router';
import { Theme } from '@nivo/core';

const Section = styled(SectionWrapper2).attrs({
	p: `2rem`,
	m: `0 0 2rem`,
})``;

const Spacer = styled(Box)`
	height: 3rem;
`;

const StyledCard = styled(Box)<{ flex?: string; onClick: any }>`
	border: 1px solid ${(p) => p.theme.customColors.grey};
	border-radius: 0.5rem;
	padding: 2rem;
	display: flex;
	flex-direction: column;
	flex: ${(p) => p.flex ?? '1'};
	justify-content: space-between;
	cursor: ${(p) => (p.onClick ? 'pointer' : 'default')};
`;

const CardTitle = styled(Txt).attrs((p) => ({
	variant: 'h5',
	fontWeight: 'bold',
	m: p.m ?? '0 0 2rem',
}))``;

const isToday = (date: Date) => startOfDay(new Date()) < date;

const isCurrentWeek = (date: Date) => startOfWeek(new Date(), { weekStartsOn: 4 }) < date;

const getBarLabel = (date: Date, groupBy: TimePeriod) => {
	switch (groupBy) {
		case 'MONTH':
			return format(new Date(date), 'MMM', { locale: de });
		case 'WEEK':
			return isCurrentWeek(date) ? 'Aktuell' : `KW ${format(date, 'I')}`;
		case 'DAY':
			return isToday(date)
				? 'Heute'
				: `${format(date, 'EE', { locale: de })} ${format(date, 'dd')}`;
		default:
			throw new Error('Unreachable');
	}
};

const formatBarData = (data: DataArray, groupBy: TimePeriod) =>
	data.map((d) => ({
		id: format(new Date(d.date), 'yyyy-MM-dd'),
		label: getBarLabel(new Date(d.date), groupBy),
		value: d.count.toFixed(2),
		added: (d.newCount || 0).toFixed(2),
	}));

type TimePeriod = 'MONTH' | 'WEEK' | 'DAY';

type DataArray = {
	date: string;
	count: number;
	newCount?: number;
}[];

const CHART_KEYS = ['value'];
const DOUBLE_CHART_KEYS = ['value', 'added'];

/*const mockData = formatBarData(
	[
		{
			date: '2024-01-02',
			count: 20,
		},
		{
			date: '2024-02-02',
			count: 30,
		},
	],
	'WEEK'
);*/

const AverageBarGraphSection: React.FC<{
	label: string;
	data: DataArray;
	withAdded?: boolean;
	groupBy: TimePeriod;
}> = ({ label, data, groupBy, withAdded = false }) => {
	const formattedData = React.useMemo(() => {
		return formatBarData(data, groupBy);
	}, [data, groupBy]);

	const { width } = useWindowDimensions();
	const theme = useTheme();

	const smallScreen = width < theme.breakpoints.values.sm;

	const chartTheme = React.useMemo<Theme>(() => {
		return {
			tooltip: {
				container: { fontSize: theme.typography.body1.fontSize },
			},
			axis: {
				ticks: { text: { fontSize: theme.typography.body2.fontSize } },
			},
		};
	}, [theme]);

	return (
		<Section>
			<CardTitle>{label}</CardTitle>
			<Box height="50rem">
				<ResponsiveBarChart
					groupMode="grouped"
					layout={smallScreen ? 'horizontal' : 'vertical'}
					data={formattedData}
					keys={withAdded ? DOUBLE_CHART_KEYS : CHART_KEYS}
					indexBy="label"
					theme={chartTheme}
				/>
			</Box>
		</Section>
	);
};

const CinfinityStatsContent = ({
	data,
}: {
	data: NewSubscriptionStatisticsQuery['newSubscriptionStatistics'];
}) => {
	const navigate = useNavigate();
	const basicData = React.useMemo(() => {
		return [
			{
				id: 'numberOfUsers',
				label: 'Gesamtanzahl der Benutzer',
				value: data.numberOfUsers,
			},
			{
				id: 'numberOfActiveSubscriptions',
				label: 'Aktive Abonnements',
				value: data.numberOfActiveSubscriptions,
				onClick: () => navigate('/users/overview?activeSubscriptions=true'),
			},
		];
	}, [data]);

	return (
		<>
			<Box display="flex" flexDirection="row" gap="2rem" m="0 0 2rem" flexWrap="wrap">
				{basicData.map((d) => (
					<StyledCard onClick={d.onClick} key={d.id} bgcolor="white" minWidth="40rem">
						<CardTitle>{d.label}</CardTitle>
						<Box display="flex" alignItems="flex-end" justifyContent="flex-end">
							<Txt variant="h4" fontWeight="bold" color="primary.main">
								{d.value}
							</Txt>
						</Box>
					</StyledCard>
				))}
			</Box>
			<AverageBarGraphSection
				label={'Anzahl Kinobesuche: Tagesverlauf'}
				data={data.moviesPerDay}
				groupBy={'DAY'}
			/>
			<AverageBarGraphSection
				label={'Anzahl Kinobesuche: Wochenschnitt'}
				data={data.moviesPerWeek}
				groupBy={'WEEK'}
			/>
			<AverageBarGraphSection
				label={'Anzahl Kinobesuche: Monatsschnitt'}
				data={data.moviesPerMonth}
				groupBy={'MONTH'}
			/>
			<AverageBarGraphSection
				label={'Aktive Abonnements: Tagesverlauf'}
				data={data.subscriptionsPerDay}
				withAdded
				groupBy={'DAY'}
			/>
			<AverageBarGraphSection
				label={'Aktive Abonnements: Wochenschnitt'}
				data={data.subscriptionsPerWeek}
				withAdded
				groupBy={'WEEK'}
			/>
			<AverageBarGraphSection
				label={'Aktive Abonnements: Monatsschnitt'}
				data={data.subscriptionsPerMonth}
				withAdded
				groupBy={'MONTH'}
			/>
			<AverageBarGraphSection
				label={'Tickets/Abonutzer pro Monat: Tagesverlauf'}
				data={data.ticketsUserPerDay}
				groupBy={'DAY'}
			/>
			<AverageBarGraphSection
				label={'Tickets/Abonutzer pro Monat: Wochenschnitt'}
				data={data.ticketsUserPerWeek}
				groupBy={'WEEK'}
			/>
			<AverageBarGraphSection
				label={'Tickets/Abonutzer pro M. : Monatsschnitt'}
				data={data.ticketsUserPerMonth}
				groupBy={'MONTH'}
			/>
		</>
	);
};
const CinfinityStatsFetcher: React.FC<{ cinemaIds: string[] }> = ({ cinemaIds }) => {
	const { data } = useNewSubscriptions(cinemaIds);

	return <CinfinityStatsContent data={data!.newSubscriptionStatistics} />;
};

const CinfinityStats: React.FC = () => {
	const [cinemaIds, setCinemaIds] = React.useState<string[]>([]);

	const stickyHeaderProps = React.useMemo(
		() => ({
			label: 'Cinfinity Statistiken',
			maxContentWidth: '130rem',
		}),
		[]
	);
	useStickyHeaderWrapper(stickyHeaderProps);

	return (
		<>
			<CinemaSelectField multi value={cinemaIds} onChange={setCinemaIds} />
			<Spacer />
			<ErrorBoundaryDefault resetKeys={cinemaIds}>
				<SuspenseDefault>
					<CinfinityStatsFetcher cinemaIds={cinemaIds} />
				</SuspenseDefault>
			</ErrorBoundaryDefault>
		</>
	);
};

export default CinfinityStats;
