import React from 'react';
import Table, { mapColumns } from '../../components/Table';
import SectionWrapper2 from '../../components/SectionWrapper2';
import TextField from '../../components/TextField';
import { useDebouncedSearchText } from '../../utils/search';
import { useSearchVoucherInstancesByOrderNumber } from '../../utils/voucher';
import { useStickyHeaderWrapper } from '../../utils/useStickyHeaderWrapper';
import { ErrorBoundaryDefault } from '../../components/ErrorBoundaryDefault';
import { SuspenseDefault } from '../../components/SuspenseDefault';

const columns = mapColumns([
	'Gutscheincode',
	'Gutscheintyp',
	'Eingelöst durch Nutzer',
	'Gültig',
	'Grund',
	'',
]);

const ShopifyOrdersFetcher = ({ queryText }: { queryText: string }) => {
	const vouchers = useSearchVoucherInstancesByOrderNumber(queryText);

	const rows = React.useMemo(() => {
		return vouchers.map((voucher) => ({
			id: voucher.id,
			rawData: voucher,
			data: [
				{
					text: voucher.qrCode,
					onPress: () => {
						window.location.href = `/gutscheine/details?search=${voucher.qrCode}`;
					},
				},
				{
					text: voucher.voucherClass.title,
				},
				{
					text: voucher.user
						? `${voucher.user.fullName} - (
					${voucher.user.email})`
						: '',
				},
				{
					text: voucher.valid ? 'Ja' : voucher.redeemedDatetime ? 'Eingelöst' : 'Nein',
				},
				{
					text: voucher.reason || '-',
				},
			],
		}));
	}, [vouchers]);

	return (
		<Table
			columns={columns}
			label={'Benutzer'}
			allRows={rows}
			isSelectable={false}
			isSearchable={false}
		/>
	);
};

const ShopifyOrders: React.FC = () => {
	const { queryText, setQueryText } = useDebouncedSearchText();

	const stickyHeaderProps = React.useMemo(
		() => ({
			label: 'Shopify bestellungen',
			maxContentWidth: '130rem',
		}),
		[]
	);
	useStickyHeaderWrapper(stickyHeaderProps);

	const resetKeys = React.useMemo(() => [queryText], [queryText]);

	return (
		<>
			<SectionWrapper2 p="3rem">
				<TextField
					type="text"
					label="Bestellnummer"
					autoFocus
					key={queryText}
					defaultValue={queryText}
					onChange={setQueryText}
				/>

				<ErrorBoundaryDefault resetKeys={resetKeys}>
					<SuspenseDefault>
						<ShopifyOrdersFetcher queryText={queryText} />
					</SuspenseDefault>
				</ErrorBoundaryDefault>
			</SectionWrapper2>
		</>
	);
};

export default ShopifyOrders;
