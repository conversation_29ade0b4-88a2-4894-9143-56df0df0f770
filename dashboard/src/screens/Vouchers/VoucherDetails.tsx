import React from 'react';
import Table, { mapColumns } from '../../components/Table';
import SectionWrapper2 from '../../components/SectionWrapper2';
import TextField from '../../components/TextField';
import { useDebouncedSearchText } from '../../utils/search';
import { updateVoucherInstance, useSearchVoucherInstanceByQR } from '../../utils/voucher';
import Dialog from '../../components/Dialog';
import { useStickyHeaderWrapper } from '../../utils/useStickyHeaderWrapper';
import { ErrorBoundaryDefault } from '../../components/ErrorBoundaryDefault';
import { SuspenseDefault } from '../../components/SuspenseDefault';

const columns = mapColumns([
	'Gutscheintyp',
	'Eingelöst am',
	'Eigelöst durch',
	'<PERSON><PERSON><PERSON><PERSON>',
	'<PERSON><PERSON>kung',
	'Unterneh<PERSON>',
	'Aktionen',
]);

const VoucherDetails: React.FC<{ queryText: string }> = ({ queryText }) => {
	const voucher = useSearchVoucherInstanceByQR(queryText);

	const handleBlockVoucher = React.useCallback(
		() => async () => {
			Dialog.render({
				title: voucher?.valid ? 'Gutschein sperren' : 'Gutschein entsperren',
				description: `Möchten Sie den Gutschein wirklich ${
					voucher?.valid ? 'sperren' : 'entsperren'
				}?`,
				buttons: [
					{
						id: `cancel-button-1-${voucher?.id}`,
						label: 'Abbrechen',
					},
					{
						id: `cancel-button-2-${voucher?.id}`,
						label: 'Weiter',
						onClick: () => voucher && updateVoucherInstance(voucher.id, !voucher.valid),
					},
				],
			});
		},
		[voucher]
	);

	const rows = React.useMemo(() => {
		if (!voucher) return [];
		return [
			{
				id: voucher.id,
				rawData: voucher,
				data: [
					{
						text: voucher.voucherClass?.title,
					},
					{ text: voucher.redeemedDatetime ? voucher.redeemedDatetime : '-' },
					{
						text: voucher.user
							? `${voucher.user.fullName} - (
					${voucher.user.email})`
							: '',
					},
					{
						text: voucher.valid ? 'Ja' : voucher.redeemedDatetime ? 'Eingelöst' : 'Nein',
					},
					{
						text: voucher.reason || '-',
					},
					{
						text: voucher.company?.name || '-',
					},
					!voucher.redeemedDatetime
						? {
								buttonLabel: voucher.valid ? 'Voucher sperren' : 'Voucher entsperren',
								onPress: handleBlockVoucher(),
						  }
						: {
								text: '-',
						  },
				],
			},
		];
	}, [voucher, handleBlockVoucher]);

	return (
		<Table
			columns={columns}
			label={'Benutzer'}
			allRows={rows}
			isSelectable={false}
			isSearchable={false}
		/>
	);
};

const VoucherDetailsDefault: React.FC = () => {
	const { queryText, setQueryText } = useDebouncedSearchText();

	const stickyHeaderProps = React.useMemo(
		() => ({
			label: 'Gutschein details',
			maxContentWidth: '130rem',
		}),
		[]
	);
	useStickyHeaderWrapper(stickyHeaderProps);

	const resetKeys = React.useMemo(() => [queryText], [queryText]);

	return (
		<SectionWrapper2 p="3rem">
			<TextField
				type="text"
				label="Gutscheincode"
				autoFocus
				key={queryText}
				defaultValue={queryText}
				onChange={setQueryText}
			/>
			<ErrorBoundaryDefault resetKeys={resetKeys}>
				<SuspenseDefault>
					<VoucherDetails queryText={queryText} />
				</SuspenseDefault>
			</ErrorBoundaryDefault>
		</SectionWrapper2>
	);
};

export default VoucherDetailsDefault;
