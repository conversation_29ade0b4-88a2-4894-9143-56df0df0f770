import React, { useState, useRef, useCallback } from 'react';
import styled from 'styled-components';
import cookies from 'js-cookie';
import { gql } from 'graphql-tag';
import { Box, Typography } from '@mui/material';
import TextField from '../components/TextField';
import Button from '../components/Button';
import { useApolloClient } from '@apollo/client';
import { PrivilegesDocument } from '../gql/graphql';

const Wrapper = styled(Box)`
	width: 100%;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	flex: 1;
	align-items: center;
	justify-content: center;
	background: ${(p) => p.theme.customColors.accentGradient0}; ;
`;

const FormWrapper = styled(Box)`
	display: flex;
	flex-direction: column;
	width: 45rem;
	align-items: stretch;
	padding: 4rem;
	border-radius: 2rem;
	background-color: ${(p) => p.theme.customColors.white};
`;

const typographySx = {
	color: (theme) => theme.colorScheme.background0,
};

const typographyErrorSx = {
	color: (theme) => theme.colorScheme.error,
};

const linkStyle = {
	color: 'inherit',
	textDecoration: 'underline',
};

type LoginProps = {
	reset: () => void;
};

export const Login: React.FC<LoginProps> = ({ reset }: LoginProps) => {
	const [email, setEmail] = useState('');
	const [password, setPassword] = useState('');
	const [emailError, setEmailError] = useState(false);
	const [passwordError, setPasswordError] = useState(false);
	const [topError, setTopError] = useState('');
	const [loading, setLoading] = useState(false);

	const client = useApolloClient();

	const submitRef = useRef<HTMLInputElement>(null);

	const handleClickSubmit = useCallback(() => submitRef.current?.click(), []);

	const handleLogin = useCallback(
		async (e) => {
			if (e && e.preventDefault) e.preventDefault();
			if (!email || !password) {
				if (!email) {
					setTopError('Bitte Email angeben');
					setEmailError(true);
				} else if (!password) {
					setTopError('Bitte Passwort angeben');
					setPasswordError(true);
				}

				return;
			}
			try {
				setLoading(true);
				const { data, errors } = await client.mutate({
					mutation: gql`
						mutation Login($email: String!, $password: String!) {
							login(email: $email, password: $password, privileged: true) {
								user {
									id
								}
								csrf
								fileServerToken
							}
						}
					`,
					variables: { email, password },
					errorPolicy: 'all',
					fetchPolicy: 'no-cache',
				});
				if (errors?.length) {
					setLoading(false);
					switch (errors[0]!.message) {
						case 'WRONG_PASSWORD':
							setTopError('Passwort inkorrekt.');
							setPasswordError(true);
							break;
						case 'USER_INVALID_OR_BLOCKED':
							setTopError('Nutzer gesperrt.');
							setEmailError(true);
							break;
						case 'WRONG_EMAIL_OR_ID':
							setTopError('Unbekannte Email.');
							setEmailError(true);
							break;
						case 'NO_PRIVILEGES':
							setTopError('Keine Zugriffsberechtigung.');
							setEmailError(true);
							break;
						default:
							throw new Error(errors[0]!.message);
					}
				} else {
					cookies.set('_csrf', data.login.csrf, { expires: 1 });
					cookies.set('_fileServerToken', data.login.fileServerToken, { expires: 1 });
					const { errors: newErrors } = await client.query({
						query: PrivilegesDocument,
						fetchPolicy: 'network-only',
						errorPolicy: 'all',
					});

					setLoading(false);

					if (newErrors) {
						setTopError(
							'Ein unerwarteter Fehler ist aufgetreten, bitte laden Sie die Seite neu und versuchen es noch einmal'
						);
					} else {
						reset();
					}
				}
			} catch (err: any) {
				setLoading(false);
				if (err.networkError) {
					setTopError('Keine Internetverbindung.');
				} else {
					throw e;
				}
			}
		},
		[email, password]
	);

	const onTextFieldChange = useCallback((e) => {
		setEmail(e);
		setEmailError(false);
	}, []);

	const onPasswordChange = useCallback((e) => {
		setPassword(e);
		setPasswordError(false);
	}, []);

	return (
		<Wrapper>
			<FormWrapper>
				<Typography variant="h4" align="center" margin="2rem 0rem" fontWeight={900}>
					ANMELDUNG
				</Typography>
				<form autoComplete="on" onSubmit={handleLogin}>
					<TextField
						label="Email"
						flex
						value={email}
						error={emailError}
						onChange={onTextFieldChange}
						startIconName="Email"
						m="2rem 0"
					/>
					<TextField
						label="Passwort"
						flex
						value={password}
						error={passwordError}
						onChange={onPasswordChange}
						onSubmit={handleClickSubmit}
						startIconName="Password"
						type="password"
						m="2rem 0"
					/>
					{topError ? (
						<Typography variant="body1" sx={typographyErrorSx} align="center">
							{topError}
						</Typography>
					) : undefined}
					<input type="submit" ref={submitRef} hidden />
					<Button
						m="4rem 0rem 0rem"
						label="ANMELDEN"
						variant="accentGradient"
						onClick={handleClickSubmit}
						loading={loading}
						loadingVariant="accentGradient"
						loadingInside
					/>
				</form>
			</FormWrapper>

			<Typography
				variant="body1"
				width="42rem"
				margin="6rem 0rem 0rem"
				align="center"
				sx={typographySx}
			>
				Nutzen Sie dieselbe Kombination aus Email und Passwort, wie für die Kino-App.
			</Typography>
			<Typography
				variant="body1"
				width="42rem"
				margin="1.5rem 0rem 0rem"
				align="center"
				sx={typographySx}
			>
				Sollten Sie ihr Passwort vergessen haben, können Sie dieses über die App zurücksetzen.
			</Typography>
			<Typography
				variant="body1"
				width="42rem"
				margin="1.5rem 0rem 0rem"
				align="center"
				sx={typographySx}
			>
				Zur Freischaltung Ihres Accounts für das Dashboard wenden Sie sich bitte an{' '}
				<a href="mailto:<EMAIL>" style={linkStyle}>
					unseren Support
				</a>
				.
			</Typography>
		</Wrapper>
	);
};
