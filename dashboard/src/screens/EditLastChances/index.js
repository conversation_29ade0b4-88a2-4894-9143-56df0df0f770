import * as React from 'react';
import styled from 'styled-native-components';

import LastMovies from './LastMovies';
import CinemaSelectField from '../../components/SplitCinemaSelectFieldNew';

const Wrapper = styled.View`
	background-color: $background0;
	padding: 3rem 4rem;
	border-radius: ${(p) => p.theme.borderRadius[2]};
	margin-bottom: 4rem;
	elevation: 2;
`;

const EditLastChances = () => {
	const [cinemaId, setCinemaId] = React.useState(null);

	const handleCinemaChange = React.useCallback(
		(cinema) => {
			setCinemaId(cinema[0]);
		},
		[setCinemaId]
	);

	const value = React.useMemo(() => (cinemaId ? [cinemaId] : []), [cinemaId]);

	return (
		<React.Fragment>
			<Wrapper>
				<CinemaSelectField value={value} label={`Kino wählen`} onChange={handleCinemaChange} />
			</Wrapper>
			{cinemaId ? <LastMovies cinemaId={cinemaId} /> : null}
		</React.Fragment>
	);
};

export default EditLastChances;
