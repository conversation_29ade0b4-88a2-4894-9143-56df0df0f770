import { useCallback, useMemo } from 'react';
import Table, { Actions, mapColumns, RowItem } from '../../components/Table';
import { useNavigate } from 'react-router';
import { StickyHeaderButton, useStickyHeaderWrapper } from '../../utils/useStickyHeaderWrapper';
import { useCinemaClusters } from './queries';
import { useDeleteCinemaClustersMutation } from '../../gql/graphql';
import { useApolloClient } from '@apollo/client';
import Dialog from '../../components/Dialog';

const columns = mapColumns(['Name', 'Position']);

const CinemaClustersOverview: React.FC = () => {
	const navigate = useNavigate();
	const client = useApolloClient();

	const clusters = useCinemaClusters();

	const [deleteCinemaClusters, { loading: loadingDelete }] = useDeleteCinemaClustersMutation({
		onCompleted: () => {
			client.refetchQueries({
				include: ['CinemaClustersOverview'],
			});
		},
	});

	const deleteCinemaClustersWithConfirmation = useCallback(
		async (ids: string[], withConfirmation: boolean = false) => {
			if (!withConfirmation) {
				Dialog.render({
					title: 'Löschen bestätigen',
					description: 'Soll der Kinocluster wirklich gelöscht werden?',
					buttons: [
						{
							id: 'delete',
							label: 'Löschen',
							onClick: () => deleteCinemaClustersWithConfirmation(ids, true),
						},
					],
				});
			} else {
				deleteCinemaClusters({ variables: { ids } });
			}
		},
		[]
	);

	const rows = useMemo<RowItem[]>(
		() =>
			clusters.map((c) => ({
				id: c.id,
				data: [
					{ text: c.name },
					{ text: c.position },
					{
						buttonLabel: 'Bearbeiten',
						onPress: () => navigate(`/admin/cinema-cluster/details/${c.id}`),
					},
				].filter((item) => item !== null),
				rawData: c,
			})),
		[clusters]
	);

	const stickyHeaderProps = useMemo(
		() => ({
			label: 'Kinocluster',
			maxContentWidth: '130rem',
			buttons: [
				{
					label: 'Kinocluster erstellen',
					onClick: () => navigate('/admin/cinema-cluster/create'),
					loading: loadingDelete,
					startIconName: 'Add',
					collapsedIconName: 'Add',
				} as StickyHeaderButton,
			],
		}),
		[loadingDelete]
	);
	useStickyHeaderWrapper(stickyHeaderProps);

	const actions = useMemo<Actions<any>>(() => {
		return {
			allActions: [
				{
					type: 'delete',
					label: 'Löschen',
				},
			],
			getPossibleActionTypes: (rowIds) => {
				return rowIds && rowIds?.length > 0 ? ['delete'] : [];
			},
		};
	}, []);

	const handleAction = useCallback((action: string, rowIds: string[]) => {
		if (action === 'delete') {
			deleteCinemaClustersWithConfirmation(rowIds);
		}
	}, []);

	return (
		<Table
			defaultRowsPerPage={10}
			columns={columns}
			allRows={rows}
			actions={actions}
			onHandleAction={handleAction}
			isSearchable={true}
			isSelectable={true}
		/>
	);
};

export default CinemaClustersOverview;
