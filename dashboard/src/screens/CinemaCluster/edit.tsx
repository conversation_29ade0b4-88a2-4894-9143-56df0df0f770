import React, { useCallback, useEffect, useMemo, useState } from 'react';
import ContentWrapper from '../../components/ContentWrapper';
import Txt from '../../components/Txt';
import TextField from '../../components/TextField';
import { useNavigate, useParams } from 'react-router';
import { Box } from '@mui/material';
import SectionWrapper2 from '../../components/SectionWrapper2';
import { xor } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import { StickyHeaderButton, useStickyHeaderWrapper } from '../../utils/useStickyHeaderWrapper';
import {
	CinemaClusterData,
	useCreateCinemaClusterMutation,
	useSingleCinemaClusterSuspenseQuery,
	useUpdateCinemaClusterMutation,
} from '../../gql/graphql';
import CinemaSelectField from '../../components/SplitCinemaSelectFieldNew';
import { ApolloError, useApolloClient } from '@apollo/client';

const CinemaClusterEdit: React.FC = () => {
	const { id } = useParams<{ id: string }>();
	const isEditing = !!id;

	const navigate = useNavigate();
	const client = useApolloClient();

	const { data } = useSingleCinemaClusterSuspenseQuery({
		skip: !isEditing,
		fetchPolicy: 'cache-first',
		errorPolicy: 'none',
		variables: {
			id: id!,
		},
	});

	const cinemaCluster = data?.cinemaCluster;

	const [cinemaClusterInput, setCinemaClusterInput] = useState<CinemaClusterData>(() => ({
		name: '',
		position: 0,
		cinemaIds: [],
	}));

	useEffect(() => {
		if (cinemaCluster) {
			setCinemaClusterInput({
				name: cinemaCluster.name,
				position: cinemaCluster.position,
				cinemaIds: cinemaCluster.cinemas?.map((c) => c.id) || [],
			});
		}
	}, [!!cinemaCluster, id]);

	const onSave = useCallback(() => {
		navigate('/admin/cinema-clusters');

		client.refetchQueries({
			include: ['CinemaClustersOverview'],
		});

		if (isEditing) {
			enqueueSnackbar('Kinocluster bearbeitet', {
				variant: 'success',
			});
		} else {
			enqueueSnackbar('Kinocluster erstellt', {
				variant: 'success',
			});
		}
	}, []);

	const onError = useCallback((error: ApolloError) => {
		enqueueSnackbar(error.message, { variant: 'error' });
	}, []);

	const [
		createCinemaCluster,
		{ called: calledSave, loading: loadingSave },
	] = useCreateCinemaClusterMutation({
		onCompleted: onSave,
		onError,
	});
	const [
		updateCinemaCluster,
		{ called: calledUpdate, loading: loadingUpdate },
	] = useUpdateCinemaClusterMutation({
		onCompleted: onSave,
		onError,
	});

	const hasTriedSaving = calledUpdate || calledSave;

	const loading = loadingSave || loadingUpdate;

	const shouldUpdate = useMemo(
		() =>
			!isEditing ||
			!!xor(cinemaCluster!.cinemas.map((c) => c.id) || [], cinemaClusterInput.cinemaIds).length ||
			cinemaCluster!.name !== cinemaClusterInput.name ||
			cinemaCluster!.position !== cinemaClusterInput.position,

		[cinemaCluster, isEditing, cinemaClusterInput]
	);

	const canSave = useMemo(() => {
		if (!cinemaClusterInput.name) {
			return false;
		}

		if (cinemaClusterInput.position == null) {
			return shouldUpdate;
		}

		return true;
	}, [cinemaCluster, cinemaClusterInput]);

	const handleSave = useCallback(() => {
		if (cinemaCluster && id) {
			updateCinemaCluster({
				variables: {
					id,
					data: cinemaClusterInput,
				},
			});
		} else {
			createCinemaCluster({
				variables: {
					data: cinemaClusterInput,
				},
			});
		}
	}, [cinemaClusterInput]);

	const stickyHeaderProps = React.useMemo(
		() => ({
			showWarningOnLeave: shouldUpdate && !calledSave && !calledUpdate,
			warningOnLeave: 'Änderungen noch nicht gespeichert. Ohne Speichern fortfahren?',
			label: 'Kinocluster bearbeiten',
			maxContentWidth: '130rem',
			buttons: [
				{
					label: isEditing ? 'Kinocluster bearbeiten' : 'Kinocluster erstellen',
					onClick: handleSave,
					startIconName: isEditing ? 'EditRounded' : 'SaveOutlined',
					disabled: !canSave,
					loading: loadingSave || loadingUpdate,
					loadingText: 'Speichern...',
					collapsedIconName: isEditing ? 'EditRounded' : 'SaveOutlined',
				} as StickyHeaderButton,
			],
		}),
		[
			shouldUpdate,
			calledSave,
			calledUpdate,
			cinemaClusterInput,
			canSave,
			loadingSave,
			loadingUpdate,
			isEditing,
		]
	);
	useStickyHeaderWrapper(stickyHeaderProps);

	const changeClusterName = useCallback(
		(name: string) => setCinemaClusterInput((input) => ({ ...input, name })),
		[]
	);

	const changeClusterPosition = useCallback(
		(position: string) =>
			setCinemaClusterInput((input) => ({ ...input, position: parseInt(position, 10) })),
		[]
	);

	const changeClusterCinemaIds = useCallback(
		(cinemaIds: string[]) => setCinemaClusterInput((input) => ({ ...input, cinemaIds })),
		[]
	);

	return (
		<ContentWrapper>
			<Box>
				<Txt variant="h6">Name</Txt>
				<TextField
					m="2rem 0"
					fullWidth
					variant="outlined"
					autoFocus
					error={hasTriedSaving && !cinemaClusterInput.name}
					key={cinemaClusterInput.name}
					defaultValue={cinemaClusterInput.name}
					label={'Name des Kinoclusters'}
					onChange={changeClusterName}
					disabled={loading}
				/>
			</Box>
			<Box>
				<Txt variant="h6">Position</Txt>
				<TextField
					m="2rem 0"
					fullWidth
					type={'number'}
					variant="outlined"
					autoFocus
					error={hasTriedSaving && cinemaClusterInput.position == null}
					key={cinemaClusterInput.position}
					defaultValue={cinemaClusterInput.position}
					label={'Position des Kinoclusters'}
					onChange={changeClusterPosition}
					disabled={loading}
				/>
			</Box>
			<SectionWrapper2 label="Zugehörige Kinos" p="2rem" m="2rem 0 0 0">
				<CinemaSelectField
					value={cinemaClusterInput.cinemaIds}
					multi
					onChange={changeClusterCinemaIds}
				/>
			</SectionWrapper2>
		</ContentWrapper>
	);
};

export default CinemaClusterEdit;
