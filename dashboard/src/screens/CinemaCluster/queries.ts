import gql from 'graphql-tag';
import { useCinemaClustersOverviewSuspenseQuery } from '../../gql/graphql';

gql`
	fragment CinemaClusterTable on CinemaCluster {
		id
		name
		position
	}

	fragment CinemaClusterDetails on CinemaCluster {
		id
		name
		position
		cinemas {
			id
		}
	}

	query CinemaClustersOverview {
		cinemaClusters {
			...CinemaClusterTable
		}
	}

	mutation CreateCinemaCluster($data: CinemaClusterData!) {
		createCinemaCluster(data: $data) {
			...CinemaClusterDetails
		}
	}

	mutation UpdateCinemaCluster($id: ID!, $data: CinemaClusterData!) {
		updateCinemaCluster(id: $id, data: $data) {
			...CinemaClusterDetails
		}
	}

	mutation DeleteCinemaClusters($ids: [ID!]!) {
		deleteCinemaClusters(ids: $ids)
	}

	query SingleCinemaCluster($id: ID!) {
		cinemaCluster(id: $id) {
			...CinemaClusterDetails
		}
	}
`;

export const useCinemaClusters = () => {
	const { data } = useCinemaClustersOverviewSuspenseQuery({ fetchPolicy: 'cache-first' });

	return data!.cinemaClusters!;
};
