import React, { useCallback } from 'react';
import { Box, Typography, Paper } from '@mui/material';
import MovieSelectField, { Movie } from '../../components/MovieSelectField';
import Dialog from '../../components/Dialog';
import { ID } from '@cinuru/utils/types';
import ContentWrapper from '../../components/ContentWrapper';
import {
	useIncomingForeignMovieIdentifiersSuspenseQuery,
	useUpdateMovieMappingMutation,
} from '../../gql/graphql';
import CinemaSelectField from '../../components/SplitCinemaSelectFieldNew';
import { ApolloError } from '@apollo/client';
import { enqueueSnackbar } from 'notistack';
import { SuspenseDefault } from '../../components/SuspenseDefault';
import { ErrorBoundaryDefault } from '../../components/ErrorBoundaryDefault';
import styled from 'styled-components';
import { ForeignMovieIdentifierFragment } from './queries';
import { useSearchParams } from 'react-router-dom';

interface MovieItemProps {
	item: ForeignMovieIdentifierFragment;
	onMovieChange: (itemId: ID, mappedMovie: Movie) => void;
}

const StyledPaper = styled(Paper)`
	padding: 2rem;
	margin-bottom: 2rem;
`;

const MovieItem: React.FC<MovieItemProps> = React.memo(({ item, onMovieChange }) => {
	const handleMovieChange = React.useCallback(
		(movies: Movie[]) => {
			onMovieChange(item.id, movies[0]!);
		},
		[item]
	);

	return (
		<StyledPaper>
			<Box display="flex" alignItems="center" justifyContent="space-between">
				<Box>
					<Typography variant="h6">Bezeichnung im Kassensystem{item.title}</Typography>
					<Typography variant="body2" color="textSecondary">
						Foreign Type: {item.foreignIdType}
					</Typography>
				</Box>
				<Box width={500}>
					<MovieSelectField
						defaultMovieId={item.currentMovie?.id}
						label="Neuen Film zuordnen"
						onChange={handleMovieChange}
					/>
				</Box>
			</Box>
		</StyledPaper>
	);
});

const onError = (error: ApolloError) => {
	enqueueSnackbar(error.message, { variant: 'error' });
};

const MovieAssignment: React.FC<{ cinemas: ID[] }> = ({ cinemas }) => {
	const [updateMapping] = useUpdateMovieMappingMutation({
		onError,
	});

	const { data, refetch } = useIncomingForeignMovieIdentifiersSuspenseQuery({
		variables: { cinemaIds: cinemas },
	});

	const handleMovieSelect = React.useCallback(async (foreignId: string, newMovieId: ID) => {
		await updateMapping({
			variables: {
				foreignId,
				movieId: newMovieId,
			},
		});
		await refetch({ force: true });
	}, []);

	const handleMovieChange = React.useCallback((item: ID, selectedMovie: Movie): void => {
		if (selectedMovie) {
			Dialog.render({
				title: 'Film zuordnen',
				description: `Möchten Sie dem Film "${selectedMovie.title}" zuordnen?`,
				buttons: [
					{
						id: 'cancel-button',
						label: 'Abbrechen',
						onClick: () => Dialog.unmount(),
					},
					{
						id: 'confirm-button',
						label: 'Bestätigen',
						onClick: () => {
							handleMovieSelect(item, selectedMovie.id);
							Dialog.unmount();
						},
					},
				],
			});
		}
	}, []);

	return (
		<Box>
			{data!.incomingForeignMovieIdentifiers.map((item) => (
				<MovieItem key={item.id} item={item} onMovieChange={handleMovieChange} />
			))}
		</Box>
	);
};

const MovieAssignmentSuspense: React.FC = () => {
	const [searchParams, setSearchParams] = useSearchParams();

	const setCinemaIds = useCallback((cinemaIds: string[]) => {
		setSearchParams(
			{
				cinemaIds,
			},
			{ replace: true }
		);
	}, []);

	const cinemaIds = searchParams.getAll('cinemaIds') || [];

	return (
		<ContentWrapper>
			<Box p={3}>
				<Typography variant="h4" gutterBottom>
					Filme zuordnen
				</Typography>

				<Typography variant="body1" color="textSecondary" paragraph>
					Änderungen sind erst nach dem nächsten Import der Spielzeiten in der App sichtbar. Dies
					kann bis zu 30 Minuten dauern.
				</Typography>
				<Box mb={3}>
					<CinemaSelectField
						label="Kino auswählen"
						value={cinemaIds}
						multi
						onChange={setCinemaIds}
					/>
				</Box>
				<ErrorBoundaryDefault>
					<SuspenseDefault>
						<MovieAssignment cinemas={cinemaIds} />
					</SuspenseDefault>
				</ErrorBoundaryDefault>
			</Box>
		</ContentWrapper>
	);
};

export default MovieAssignmentSuspense;
