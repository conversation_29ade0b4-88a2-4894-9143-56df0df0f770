import gql from 'graphql-tag';
import { IncomingForeignMovieIdentifiersQuery } from '../../gql/graphql';

export type ForeignMovieIdentifierFragment = IncomingForeignMovieIdentifiersQuery['incomingForeignMovieIdentifiers'][number];

gql`
	fragment ForeignMovieIdentifierListing on ForeignMovieIdentifier {
		id
		foreignIdType
		title
		currentMovie {
			id
			title
		}
	}

	query IncomingForeignMovieIdentifiers($cinemaIds: [ID!]!, $force: Boolean) {
		incomingForeignMovieIdentifiers(cinemaIds: $cinemaIds, force: $force) {
			...ForeignMovieIdentifierListing
		}
	}

	mutation UpdateMovieMapping($foreignId: ID!, $movieId: ID!) {
		updateForeignMovieMapping(foreignId: $foreignId, movieId: $movieId)
	}
`;
