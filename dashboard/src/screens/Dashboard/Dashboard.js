/* eslint-disable react-perf/jsx-no-new-function-as-prop */
/* eslint-disable react-perf/jsx-no-new-array-as-prop */
/* eslint-disable react-perf/jsx-no-new-object-as-prop */
import React from 'react';
import styled, { useTheme } from 'styled-components';

import { format, subMinutes } from 'date-fns';
import { useState, useEffect } from 'react';
import { range } from 'lodash';
import { Arrow } from './Arrow';
import { PosterRanking } from './PosterRanking';
import { StatusLevelBarChart } from './StatusLevelBarChart';
import { data, data2, data4, data5, MiniLineChart, MiniLineChart2 } from './MiniLineCharts';
import { DemographicsBarChart } from './MiniDemographicsBarChart';
import { GroupSizeBarChart } from './GroupSizeBarChart';

import { Box, Typography } from '@mui/material';
import MoviePoster from '../../components/MoviePoster';
import CinemaSelectField from '../../components/SplitCinemaSelectFieldNew';

const CARD_WIDTH = 40;
const CARD_MARGIN = 0.5;
const STATS_WRAPPER_MARGIN = 1;

const Card = styled(Box)`
	width: ${CARD_WIDTH}rem;
	margin: ${CARD_MARGIN}rem;
	background-color: ${(p) =>
		p.backgroundColor ? p.backgroundColor : p.theme.colorScheme.background0};
	border-radius: ${(p) => p.theme.borderRadius[1]};
`;
const DoubleWidthCard = styled(Card)`
	flex-grow: 2;
`;

const Wrapper = styled(Box)`
	display: flex;
	flex-direction: column;
`;

const RowWrapper = styled(Box)`
	margin: 1rem ${CARD_MARGIN + STATS_WRAPPER_MARGIN}rem;
	background-color: ${(p) => p.theme.colorScheme.background0};
	border-radius: ${(p) => p.theme.borderRadius[1]};
`;

const StatsWrapper = styled(Box)`
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: center;
	margin: 0 ${STATS_WRAPPER_MARGIN}rem 1rem;
`;

const CardHeading = styled((props) => <Typography variant="body1" {...props} />)`
	padding: 1rem 2rem;
	text-align: center;
	border-bottom: 0.5px solid ${(p) => p.theme.colorScheme.border0};
	color: ${(p) => p.theme.colorScheme.neutral1};
	font-weight: 700;
`;
const StatisticsHeading = styled((props) => <Typography variant="h5" {...props} />)`
	color: ${(p) => p.theme.colorScheme.accent0};
	font-weight: 700;
`;
const CardContentWrapper = styled(Box)`
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	height: 25rem;
`;
const KPIWrapper = styled(Box)`
	width: 18rem;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	min-width: 0;
`;

const StyledMoreText = styled(Typography)`
	text-align: end;
	width: 100%;
	padding: 1rem;
	border-top: 0.5px solid ${(p) => p.theme.colorScheme.border0};
`;

const MoreText = () => <StyledMoreText variant="body2">&gt; Mehr Infos</StyledMoreText>;

const StickerWrapper = styled(Box)`
	display: flex;
	margin: 0.5rem 0 0.5rem 0;
	flex-direction: row;
	align-items: center;
	width: 100%;
`;
const StickerSectionWrapper = styled(Box)`
	display: flex;
	flex-direction: column;
	padding: 0 1rem 0 1rem;
	height: 25rem;
	justify-content: center;
`;

const InitialsWrapper = styled(Box)`
	display: flex;
	width: 5rem;
	height: 5rem;
	align-items: center;
	justify-content: center;
	border-radius: 10000rem;
	background-color: ${(p) => p.theme.colorScheme.background1};
	margin: 0 1rem 0 1rem;
`;

const CityInitials = ({ text }) => {
	const theme = useTheme();
	return (
		<InitialsWrapper>
			<Typography color={theme?.colorScheme?.neutral2} fontWeight={700}>
				{text}
			</Typography>
		</InitialsWrapper>
	);
};

const Dashboard = () => {
	const theme = useTheme();
	const statsWrapperRef = React.useRef(null);
	const [fakeActiveUsers, setFakeActiveUsers] = useState(
		range(30).map(() => Math.round(Math.random() * 200) + 11100)
	);

	const data3 = [
		{
			id: 'Aktive Nutzer',
			data: fakeActiveUsers.map((val, idx) => ({
				x: format(subMinutes(new Date(), idx), 'HH:mm'),
				y: val,
			})),
		},
	];
	useEffect(() => {
		const interval = setInterval(() => {
			console.log('runInterval');
			const current = [...fakeActiveUsers];
			current.shift();
			current.push(Math.round(Math.random() * 400) + 11100);
			setFakeActiveUsers(current);
		}, 1000);
		return () => clearInterval(interval);
	}, [setFakeActiveUsers, fakeActiveUsers]);

	const [selectedCinemas, setSelectedCinemas] = React.useState([]);

	const handleChange = React.useCallback((selected) => {
		setSelectedCinemas(selected);
	}, []);

	return (
		<Wrapper>
			<RowWrapper>
				<CinemaSelectField value={selectedCinemas} onChange={handleChange} multi />
			</RowWrapper>
			<StatsWrapper ref={statsWrapperRef}>
				<Card>
					<CardHeading size="xs">Aktive Nutzer</CardHeading>
					<CardContentWrapper>
						<MiniLineChart data={data3} yScaleMin={10500} yScaleMax={12500} />
						<KPIWrapper>
							<StatisticsHeading>
								{Number(fakeActiveUsers[fakeActiveUsers.length - 1]).toLocaleString('de-De')}
							</StatisticsHeading>
							<Typography>Nutzer</Typography>
							<Typography variant="body2">sind zur Zeit aktiv.</Typography>
						</KPIWrapper>
					</CardContentWrapper>
					<MoreText />
				</Card>
				<Card>
					<CardHeading size="xs">Neue Nutzer</CardHeading>
					<CardContentWrapper>
						<MiniLineChart data={data} />
						<KPIWrapper>
							<StatisticsHeading>+7.304</StatisticsHeading>
							<Typography>Nutzer</Typography>
							<Typography variant="body2">in den letzten 7 Tagen.</Typography>
							<Arrow percent={-30} />
						</KPIWrapper>
					</CardContentWrapper>
					<MoreText />
				</Card>
				<Card>
					<CardHeading size="xs">Neue Plus-Mitglieder</CardHeading>
					<CardContentWrapper>
						<MiniLineChart data={data2} />
						<KPIWrapper>
							<StatisticsHeading>+6.184</StatisticsHeading>
							<Typography>Plus-Mitglieder</Typography>
							<Typography variant="body2">in den letzten 7 Tagen.</Typography>
							<Arrow percent={12} />
						</KPIWrapper>
					</CardContentWrapper>
					<MoreText />
				</Card>

				<Card>
					<CardHeading size="xs">Merkliste</CardHeading>
					<CardContentWrapper>
						<PosterRanking
							movies={[
								{
									poster: 'https://static.cinuru.com/public/posterSmall/1523464641258.jpg',
									kpiValue: '83.468',
								},
								{
									poster: 'https://static.cinuru.com/public/posterSmall/1540585266999.jpg',
									kpiValue: '41.889',
								},
								{
									poster: 'https://static.cinuru.com/public/posterSmall/1557672914476.jpg',
									kpiValue: '3.268',
								},
							]}
							description="Vormerkungen"
						/>
					</CardContentWrapper>
					<MoreText />
				</Card>
				<Card>
					<CardHeading size="xs">Interaktionen</CardHeading>
					<CardContentWrapper>
						<PosterRanking
							movies={[
								{
									poster: 'https://static.cinuru.com/public/posterSmall/1575966651946.jpg',
									kpiValue: '487.998',
								},
								{
									poster: 'https://static.cinuru.com/public/posterSmall/1597667699497.jpg',
									kpiValue: '383.233',
								},
								{
									poster: 'https://static.cinuru.com/public/posterSmall/1540585266999.jpg',
									kpiValue: '310.112',
								},
							]}
							description="Interaktionen"
						/>
					</CardContentWrapper>
					<MoreText />
				</Card>

				<Card>
					<CardHeading size="xs">Ticketpunkte</CardHeading>
					<CardContentWrapper>
						<MiniLineChart2 data={data4} yScaleMin={0} />
						<KPIWrapper>
							<StatisticsHeading>+38.461</StatisticsHeading>
							<Typography>Punkte gesammelt</Typography>
							<StatisticsHeading>-21.461</StatisticsHeading>
							<Typography>Punkte eingelöst</Typography>
							<Typography variant="body2">in den letzten 7 Tagen.</Typography>
						</KPIWrapper>
					</CardContentWrapper>
					<MoreText />
				</Card>

				<Card>
					<CardHeading size="xs">Statuspunkte</CardHeading>
					<CardContentWrapper>
						<MiniLineChart data={data5} yScaleMin={4500} />
						<KPIWrapper>
							<StatisticsHeading>+738.443</StatisticsHeading>
							<Typography>Punkte gesammelt</Typography>
							<Typography variant="body2">in den letzten 7 Tagen.</Typography>
							<Arrow percent={+15} />
						</KPIWrapper>
					</CardContentWrapper>
					<MoreText />
				</Card>
				<Card>
					<CardHeading size="xs">Statuslevel</CardHeading>
					<CardContentWrapper>
						<StatusLevelBarChart />
					</CardContentWrapper>
					<MoreText />
				</Card>
				<DoubleWidthCard>
					<CardHeading size="xs">Demografie</CardHeading>
					<CardContentWrapper>
						<DemographicsBarChart />
					</CardContentWrapper>
					<MoreText />
				</DoubleWidthCard>
				<Card>
					<CardHeading size="xs">Sticker</CardHeading>
					<CardContentWrapper>
						<StickerSectionWrapper>
							<StickerWrapper>
								<MoviePoster
									width="8rem"
									height="8rem"
									poster={
										'https://cdn-app.cineplex.de/app/cineplex/achievements/10-Nachtschicht-500x500.png'
									}
								/>
								<Typography variant="body2" width="13.5rem" paddingLeft="2rem">
									Nachtschicht
								</Typography>
								<Typography color={theme.colorScheme.accent0} width="5rem" textAlign="right">
									2.318
								</Typography>
								<Typography variant="body2" paddingLeft="0.5rem">
									mal vergeben{' '}
								</Typography>
							</StickerWrapper>
							<StickerWrapper>
								<MoviePoster
									width="7rem"
									height="7rem"
									marginLeft="0.5rem"
									poster={
										'https://cdn-app.cineplex.de/app/cineplex/achievements/0-welcome-500x500.png'
									}
								/>
								<Typography variant="body2" width="13.5rem" paddingLeft="2.5rem">
									Welcome
								</Typography>
								<Typography color={theme.colorScheme.accent0} width="5rem" textAlign="right">
									1.966
								</Typography>
								<Typography variant="body2" paddingLeft="0.5rem">
									mal vergeben{' '}
								</Typography>
							</StickerWrapper>
							<StickerWrapper>
								<MoviePoster
									width="6rem"
									height="6rem"
									poster="https://cdn-app.cineplex.de/app/cineplex/achievements/7-StarWars-500x500.png"
									marginLeft="1rem"
								/>
								<Typography variant="body2" width="13.5rem" paddingLeft="3rem">
									Star-Wars
								</Typography>
								<Typography color={theme.colorScheme.accent0} width="5rem" textAlign="right">
									1.408
								</Typography>
								<Typography variant="body2" paddingLeft="0.5rem">
									mal vergeben{' '}
								</Typography>
							</StickerWrapper>
						</StickerSectionWrapper>
					</CardContentWrapper>
					<MoreText />
				</Card>
				<Card>
					<CardHeading size="xs">Aktivste Standorte</CardHeading>
					<CardContentWrapper>
						<StickerSectionWrapper>
							<StickerWrapper>
								<CityInitials text="AC" />
								<Typography variant="body2" width="15rem" paddingLeft="2rem">
									Aachen
								</Typography>
								<Typography color={theme.colorScheme.accent0} width="5rem" textAlign="right">
									438
								</Typography>
								<Typography variant="body2" paddingLeft="0.5rem">
									neue Nutzer
								</Typography>
							</StickerWrapper>
							<StickerWrapper>
								<CityInitials text="B" />
								<Typography variant="body2" width="15rem" paddingLeft="2rem">
									Berlin Titania
								</Typography>
								<Typography color={theme.colorScheme.accent0} width="5rem" textAlign="right">
									307
								</Typography>
								<Typography variant="body2" paddingLeft="0.5rem">
									neue Nutzer
								</Typography>
							</StickerWrapper>
							<StickerWrapper>
								<CityInitials text="MS" />
								<Typography variant="body2" width="15rem" paddingLeft="2rem">
									Münster
								</Typography>
								<Typography color={theme.colorScheme.accent0} width="5rem" textAlign="right">
									240
								</Typography>
								<Typography variant="body2" paddingLeft="0.5rem">
									neue Nutzer
								</Typography>
							</StickerWrapper>
						</StickerSectionWrapper>
					</CardContentWrapper>
					<MoreText />
				</Card>
				<Card>
					<CardHeading size="xs">Gruppengrößen</CardHeading>
					<CardContentWrapper>
						<GroupSizeBarChart />
					</CardContentWrapper>
					<MoreText />
				</Card>
				<DoubleWidthCard>
					<CardHeading size="xs">Personalisierte Push-Nachrichten</CardHeading>
					<CardContentWrapper flexDirection={'row'} flexWrap={'wrap'} height={'auto !important'}>
						<KPIWrapper>
							<StatisticsHeading>+7.304</StatisticsHeading>
							<Typography>Mehrverkäufe</Typography>
							<Typography variant="body2">
								in den letzten 7 Tagen, im Vergleich zur Kontrollgruppe.
							</Typography>
							<Arrow percent={+23} />
						</KPIWrapper>
						<PosterRanking
							width="30rem"
							movies={[
								{
									poster: 'https://static.cinuru.com/public/posterSmall/1523464641258.jpg',
									kpiValue: '83.468',
								},
								{
									poster: 'https://static.cinuru.com/public/posterSmall/1540585266999.jpg',
									kpiValue: '41.889',
								},
								{
									poster: 'https://static.cinuru.com/public/posterSmall/1557672914476.jpg',
									kpiValue: '3.268',
								},
							]}
							description="mal Empfohlen"
						/>
						<KPIWrapper>
							<StatisticsHeading>303.304</StatisticsHeading>
							<Typography>Versendete Nachrichten</Typography>
							<Typography variant="body2">in den letzten 7 Tagen.</Typography>
							<Arrow percent={+7} />
						</KPIWrapper>
					</CardContentWrapper>
					<MoreText />
				</DoubleWidthCard>
			</StatsWrapper>
		</Wrapper>
	);
};

export default Dashboard;
