import React, { useMemo } from 'react';
import Table, { mapColumns, RowItem } from '../../components/Table';
import { useUserPrivileges } from '../../utils/user';
import styled from 'styled-components';
import { Box } from '@mui/material';
import { isCinfinity } from '../../consts';
import { StickyHeaderButton, useStickyHeaderWrapper } from '../../utils/useStickyHeaderWrapper';
import { useNavigate } from 'react-router';

const cinemaCompaniesColumns = mapColumns([
	'Name',
	'Benutzer',
	'Zugriff auf das Dashboard',
	'Filmstatistiken',
	!isCinfinity ? 'Zugriffsrecht Bonusprogramm' : null,
	'Zugriff auf Campaigning-Tools',
]);

const ClickableUser = styled(Box)`
	&:hover {
		text-decoration: underline;
		color: ${({ theme }) => theme.customColors.accentBlue};
		cursor: pointer;
	}
`;

const CinemaOperatingCompaniesOverview: React.FC = () => {
	const privileges = useUserPrivileges();

	const navigate = useNavigate();

	const cinemaOperatingCompanies = privileges?.belongsToCinemaOperatingCompanies || [];

	const formatBooleanText = (value?: boolean | null) => (value ? 'Ja' : 'Nein');
	const rows = useMemo<RowItem[]>(
		() =>
			cinemaOperatingCompanies.map((c) => ({
				id: c.id,
				data: [
					{ text: c.name },
					{
						text: c.associatedUsers?.length
							? c.associatedUsers.map((c) => (
									<ClickableUser key={c.id} onClick={() => navigate(`/users/details/${c.id}`)}>
										{c.fullName || c.email}
									</ClickableUser>
							  ))
							: '-',
					},
					{ text: formatBooleanText(c.accessRightDashboard) },
					{ text: formatBooleanText(c.accessRightFilmStatistics) },
					!isCinfinity ? { text: formatBooleanText(c.accessRightBonusProgram) } : null,
					{ text: formatBooleanText(c.accessRightCampaigning) },
					{
						buttonLabel: 'Bearbeiten',
						onPress: () => navigate(`/admin/cinema-company/details/${c.id}`),
					},
				].filter((item) => item !== null),
				rawData: c,
			})),
		[cinemaOperatingCompanies]
	);

	const stickyHeaderProps = React.useMemo(
		() => ({
			label: 'Kinobetreiber',
			maxContentWidth: '130rem',
			isLoading: !privileges,
			buttons: [
				{
					label: 'Kinobetreiber erstellen',
					onClick: () => navigate('/admin/cinema-company/create'),
					startIconName: 'Add',
					loading: false,
					collapsedIconName: 'Add',
				} as StickyHeaderButton,
			],
		}),
		[privileges]
	);
	useStickyHeaderWrapper(stickyHeaderProps);

	return (
		<Table
			defaultRowsPerPage={10}
			columns={cinemaCompaniesColumns}
			allRows={rows}
			isSearchable={true}
			isSelectable={false}
		/>
	);
};

export default CinemaOperatingCompaniesOverview;
