import React, { useEffect, useMemo, useState } from 'react';
import ContentWrapper from '../../components/ContentWrapper';
import Txt from '../../components/Txt';
import TextField from '../../components/TextField';
import { useNavigate, useParams } from 'react-router';
import { useUserPrivileges } from '../../utils/user';
import { CinemaOperatingCompanyInput } from './queries';
import { Box, Checkbox, FormControlLabel } from '@mui/material';
import styled from 'styled-components';
import SectionWrapper2 from '../../components/SectionWrapper2';
import SearchSelectField2 from '../../components/SearchSelectField2';
import { omit, xor } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import Dialog from '../../components/Dialog';
import { isCinfinity } from '../../consts';
import { StickyHeaderButton, useStickyHeaderWrapper } from '../../utils/useStickyHeaderWrapper';
import {
	useCreateCinemaOperatingCompanyMutation,
	useUpdateCinemaOperatingCompanyMutation,
} from '../../gql/graphql';

const FormControlLabelGapped = styled(FormControlLabel)`
	gap: 8px;
`;

const CinemaOperatingCompanyEdit: React.FC = () => {
	const { id } = useParams<{ id: string }>();
	const isEditing = !!id;
	const privileges = useUserPrivileges();

	const navigate = useNavigate();

	const [cinemaOperatingCompanyInput, setCinemaOperatingCompanyInput] = useState<
		Partial<CinemaOperatingCompanyInput>
	>({});

	const onSave = () => {
		navigate('/admin/cinema-companies');

		if (isEditing) {
			enqueueSnackbar('FTB bearbeitet', {
				variant: 'success',
			});
		} else {
			enqueueSnackbar('FTB erstellt', {
				variant: 'success',
			});
		}
	};

	const [
		createCinemaOperatingCompany,
		{ called: calledSave, loading: loadingSave },
	] = useCreateCinemaOperatingCompanyMutation({
		onCompleted: onSave,
		refetchQueries: ['Privileges'],
	});
	const [
		updateCinemaOperatingCompany,
		{ called: calledUpdate, loading: loadingUpdate },
	] = useUpdateCinemaOperatingCompanyMutation({
		onCompleted: onSave,
		refetchQueries: ['Privileges'],
	});

	const hasTriedSaving = calledUpdate || calledSave;

	const loading = loadingSave || loadingUpdate;

	const existingCinemaOperatingCompany = useMemo(
		() =>
			isEditing
				? privileges?.belongsToCinemaOperatingCompanies?.find((c) => c.id === id)
				: undefined,
		[isEditing]
	);

	const availableCinemaData = useMemo(
		() => privileges?.belongsToCinemaOperatingCompanies?.flatMap((c) => c.cinemas || []) || [],
		[privileges]
	);

	const conflictsWithCinemaCompanies = () =>
		privileges?.belongsToCinemaOperatingCompanies?.filter(
			(c) =>
				(!isEditing || c.id != id) &&
				c.cinemas?.some((cc) => cinemaOperatingCompanyInput.cinemasIds?.includes(cc.id))
		);

	useEffect(() => {
		if (existingCinemaOperatingCompany) {
			setCinemaOperatingCompanyInput({
				...omit(existingCinemaOperatingCompany, ['id', '__typename', 'cinemas', 'associatedUsers']),
				cinemasIds: existingCinemaOperatingCompany.cinemas?.map((a) => a.id) || [],
			});
		}
	}, [!!existingCinemaOperatingCompany]);

	const shouldUpdate = useMemo(
		() =>
			!existingCinemaOperatingCompany ||
			!!xor(
				cinemaOperatingCompanyInput.cinemasIds,
				existingCinemaOperatingCompany.cinemas?.map((c) => c.id) || []
			).length ||
			cinemaOperatingCompanyInput.name != existingCinemaOperatingCompany.name ||
			cinemaOperatingCompanyInput.accessRightBonusProgram !=
				existingCinemaOperatingCompany.accessRightBonusProgram ||
			cinemaOperatingCompanyInput.accessRightCampaigning !=
				existingCinemaOperatingCompany.accessRightCampaigning ||
			cinemaOperatingCompanyInput.accessRightDashboard !=
				existingCinemaOperatingCompany.accessRightDashboard ||
			cinemaOperatingCompanyInput.accessRightFilmStatistics !=
				existingCinemaOperatingCompany.accessRightFilmStatistics,
		[existingCinemaOperatingCompany, cinemaOperatingCompanyInput]
	);

	const canSave = useMemo(() => {
		if (!cinemaOperatingCompanyInput.name) {
			return false;
		}

		if (existingCinemaOperatingCompany) {
			return shouldUpdate;
		}

		return true;
	}, [cinemaOperatingCompanyInput, existingCinemaOperatingCompany]);

	const handleSave = (withConfirmation?: true) => {
		const conflictingCinemaCompanies = conflictsWithCinemaCompanies();

		if (!withConfirmation && !!conflictingCinemaCompanies?.length) {
			Dialog.render({
				title: 'Warnung',
				description: `Dieses Kino ist bereits dem Kinobetrieb "${conflictingCinemaCompanies
					.map((c) => c.name)
					.join(', ')}" zugeordnet. Wirklich ändern?`,
				buttons: [
					{
						id: '1',
						label: 'Abbrechen',
					},
					{
						id: '2',
						label: 'Ja',
						onClick: () => handleSave(true),
						variant: 'mainButton',
					},
				],
			});
			return;
		}

		if (existingCinemaOperatingCompany && id) {
			updateCinemaOperatingCompany({
				variables: {
					id,
					data: {
						...cinemaOperatingCompanyInput,
					},
				},
			});
		} else {
			createCinemaOperatingCompany({
				variables: {
					data: cinemaOperatingCompanyInput,
				},
			});
		}
	};

	const stickyHeaderProps = React.useMemo(
		() => ({
			showWarningOnLeave: shouldUpdate && !calledSave && !calledUpdate,
			warningOnLeave:
				'Das Kinobetreiber wurde bearbeitet aber Änderungen werden nicht automatisch gespeichert. Ohne speichern fortfahren?',
			label: 'Kinobetreiber bearbeiten',
			maxContentWidth: '130rem',
			buttons: [
				{
					label: isEditing ? 'Kinobetreiber bearbeiten' : 'Kinobetreiber erstellen',
					onClick: handleSave,
					startIconName: isEditing ? 'EditRounded' : 'SaveOutlined',
					disabled: !canSave,
					loading: loadingSave || loadingUpdate,
					loadingText: 'Speichern...',
					collapsedIconName: isEditing ? 'EditRounded' : 'SaveOutlined',
				} as StickyHeaderButton,
			],
		}),
		[shouldUpdate, calledSave, calledUpdate, canSave, loadingSave, loadingUpdate, isEditing]
	);
	useStickyHeaderWrapper(stickyHeaderProps);

	console.log({ fds: cinemaOperatingCompanyInput });

	return (
		<>
			<ContentWrapper>
				<Box>
					<Txt variant="h6">FTB name</Txt>
					<TextField
						m="2rem 0"
						fullWidth
						variant="outlined"
						autoFocus
						error={hasTriedSaving && !cinemaOperatingCompanyInput.name}
						key={cinemaOperatingCompanyInput.name}
						defaultValue={cinemaOperatingCompanyInput.name}
						label={'Name des FTB'}
						onChange={(name) => setCinemaOperatingCompanyInput((input) => ({ ...input, name }))}
						disabled={loading}
					/>
				</Box>
				<Box display="flex" flexDirection={'column'} paddingLeft={'12px'}>
					<FormControlLabelGapped
						control={
							<Checkbox
								defaultChecked={cinemaOperatingCompanyInput.accessRightDashboard}
								key={'dashboard' + cinemaOperatingCompanyInput.accessRightDashboard}
								onChange={() =>
									setCinemaOperatingCompanyInput((customer) => ({
										...customer,
										accessRightDashboard: !customer.accessRightDashboard,
									}))
								}
							/>
						}
						label={'Zugriff auf das Dashboard'}
					/>
					{!isCinfinity && (
						<FormControlLabelGapped
							control={
								<Checkbox
									defaultChecked={cinemaOperatingCompanyInput.accessRightBonusProgram}
									key={'bonusprogram' + cinemaOperatingCompanyInput.accessRightBonusProgram}
									onChange={() =>
										setCinemaOperatingCompanyInput((customer) => ({
											...customer,
											accessRightBonusProgram: !customer.accessRightBonusProgram,
										}))
									}
								/>
							}
							label={'Zugriffsrecht Bonusprogramm'}
						/>
					)}
					<FormControlLabelGapped
						control={
							<Checkbox
								defaultChecked={cinemaOperatingCompanyInput.accessRightFilmStatistics}
								key={'filmstatistics' + cinemaOperatingCompanyInput.accessRightFilmStatistics}
								onChange={() =>
									setCinemaOperatingCompanyInput((customer) => ({
										...customer,
										accessRightFilmStatistics: !customer.accessRightFilmStatistics,
									}))
								}
							/>
						}
						label={'Filmstatistiken'}
					/>
					<FormControlLabelGapped
						control={
							<Checkbox
								defaultChecked={cinemaOperatingCompanyInput.accessRightCampaigning}
								key={'rightcampaigning' + cinemaOperatingCompanyInput.accessRightCampaigning}
								onChange={() =>
									setCinemaOperatingCompanyInput((customer) => ({
										...customer,
										accessRightCampaigning: !customer.accessRightCampaigning,
									}))
								}
							/>
						}
						label={'Zugriff auf Campaigning-Tools'}
					/>
				</Box>
				<SectionWrapper2 label="Zugehörige Kinos" p="2rem" m="2rem 0 0 0">
					<SearchSelectField2
						label="Kinos zuweisen"
						options={availableCinemaData}
						labelKey="name"
						valueKey="id"
						m="1rem 0 0 0"
						onChange={(opts) =>
							setCinemaOperatingCompanyInput((customer) => ({
								...customer,
								cinemasIds: opts.map((o) => o.value),
							}))
						}
						key={JSON.stringify(cinemaOperatingCompanyInput.cinemasIds)}
						defaultValues={cinemaOperatingCompanyInput.cinemasIds}
						multiple
					/>
				</SectionWrapper2>
			</ContentWrapper>
		</>
	);
};

export default CinemaOperatingCompanyEdit;
