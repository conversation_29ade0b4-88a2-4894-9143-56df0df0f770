import React, { useMemo } from 'react';
import DatetimeRangeSelectForm from '../../components/DatetimeRangeSelectForm';
import SectionWrapper2 from '../../components/SectionWrapper2';
import gql from 'graphql-tag';
import Table, { RowItem } from '../../components/Table';
import { map } from 'lodash';
import { dateToString } from '../../utils/time';
import { useStickyHeaderWrapper } from '../../utils/useStickyHeaderWrapper';
import { ErrorBoundaryDefault } from '../../components/ErrorBoundaryDefault';
import { SuspenseDefault } from '../../components/SuspenseDefault';
import { useMissedPayoutsSuspenseQuery } from '../../gql/graphql';
import { addYears } from 'date-fns';

gql`
	query MissedPayouts($from: Date, $to: Date) {
		missedPayouts(filter: { from: $from, to: $to }) {
			user {
				id
				fullName
				email
			}
			subscription {
				id
				paidUntil: payedUntil
			}
		}
	}
`;

const columns = map(['Nutzername', 'Email', 'Fehlgeschlagenen Zahlung'], (label, id) => ({
	label,
	id,
}));

const MissedPaymentsOverviewWrapped: React.FC<{
	dateFrom?: string;
	dateTo?: string;
}> = ({ dateFrom, dateTo }) => {
	const { data } = useMissedPayoutsSuspenseQuery({
		variables: {
			from: dateFrom,
			to: dateTo,
		},
		errorPolicy: 'none',
		fetchPolicy: 'cache-and-network',
	});

	const rows = useMemo<RowItem[]>(
		() =>
			data?.missedPayouts?.map((mp) => ({
				id: mp.subscription.id,
				data: [
					{ text: mp.user?.fullName || '-' },
					{ text: mp.user?.email },
					{ text: mp.subscription.paidUntil },
				],
				rawData: mp,
			})) ?? [],
		[data]
	);

	return (
		<Table
			defaultRowsPerPage={10}
			columns={columns}
			allRows={rows}
			isSelectable={false}
			isSearchable={false}
		/>
	);
};

const MissedPaymentsOverview: React.FC = () => {
	const [dateFrom, setDateFrom] = React.useState<string | undefined>(() =>
		dateToString(addYears(new Date(), -2))
	);
	const [dateTo, setDateTo] = React.useState<string | undefined>(() => dateToString(new Date()));

	const stickyHeaderProps = React.useMemo(
		() => ({
			label: 'Mahnwesen',
			maxContentWidth: '130rem',
		}),
		[]
	);
	useStickyHeaderWrapper(stickyHeaderProps);

	const resetKeys = React.useMemo(() => [dateFrom, dateTo], [dateFrom, dateTo]);

	return (
		<>
			<DatetimeRangeSelectForm
				label="Zeitraum wählen"
				defaultStartDate={dateFrom}
				defaultEndDate={dateTo}
				onChangeStartDate={setDateFrom}
				onChangeEndDate={setDateTo}
				dateOnly={true}
				hideCampaignEndTypeControls={true}
			/>
			<SectionWrapper2>
				<ErrorBoundaryDefault resetKeys={resetKeys}>
					<SuspenseDefault>
						<MissedPaymentsOverviewWrapped dateFrom={dateFrom} dateTo={dateTo} />
					</SuspenseDefault>
				</ErrorBoundaryDefault>
			</SectionWrapper2>
		</>
	);
};
export default MissedPaymentsOverview;
