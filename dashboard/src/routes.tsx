import * as React from 'react';
import { createRoutesFromElements, Navigate, Route, Routes } from 'react-router';
import { brand, Feature, FEATURES, Privilege } from './consts';
import { Privileges, useUserPrivileges } from './utils/user';
import { Brand, IconName } from '@cinuru/utils';
import styled from 'styled-components';
import { Box, Typography } from '@mui/material';
import { createBrowserRouter } from 'react-router-dom';
import App from './App';
import { useOnRouteEnter } from './services/analytics';

const Campaigns = React.lazy(() => import('./screens/Campaigning/Campaigns'));
const CampaignsNew = React.lazy(() => import('./screens/CampaigningNew/CampaignsNew'));
const CreateCampaign = React.lazy(() => import('./screens/Campaigning/CreateCampaign'));
const EditCampaignOld = React.lazy(() => import('./screens/Campaigning/EditCampaign'));
const EditCampaignNew = React.lazy(() => import('./screens/CampaigningNew/EditCampaignNew'));
const StatisticsBonusProgram = React.lazy(() => import('./screens/StatisticsBonusProgram'));
const StatisticsMovies = React.lazy(() => import('./screens/StatisticsMovies'));
const StatisticsUsage = React.lazy(() => import('./screens/StatisticsUsage'));
const EditCinemaNew = React.lazy(() => import('./screens/EditCinemaNew/EditCinemaNew'));
const EditSoonInCinema = React.lazy(() => import('./screens/EditSoonInCinema'));
const EditLastChances = React.lazy(() => import('./screens/EditLastChances'));
const EditBonusprogram = React.lazy(() => import('./screens/EditBonusprogram'));
const PosTokens = React.lazy(() => import('./screens/PosTokens'));
const Dashboard = React.lazy(() => import('./screens/Dashboard/Dashboard'));
const EmailEditor = React.lazy(() => import('./screens/EmailEditor/EmailEditor'));
const MovieStats = React.lazy(() => import('./screens/Dashboard/MovieStats/MovieStats'));
const CinemaComparison = React.lazy(
	() => import('./screens/Dashboard/CinemaComparison/CinemaComparison')
);
const TargetGroups = React.lazy(() => import('./screens/TargetGroups/TargetGroups'));
const EditTargetGroup = React.lazy(() => import('./screens/EditTargetGroup/EditTargetGroup'));
const EditUsers = React.lazy(() => import('./screens/EditUsers/EditUsers'));
const EditUser = React.lazy(() => import('./screens/EditUser/EditUser'));
const CinfinityStats = React.lazy(() => import('./screens/CinfinityStats/CinfinityStats'));
const PayoutOverview = React.lazy(() => import('./screens/PayoutOverview/PayoutOverview'));
const CreateVoucher = React.lazy(() => import('./screens/Vouchers/CreateVoucher'));
const VoucherDetails = React.lazy(() => import('./screens/Vouchers/VoucherDetails'));
const ShopifyOrders = React.lazy(() => import('./screens/Vouchers/ShopifyOrders'));
const BeaconDetails = React.lazy(() => import('./screens/Beacons/BeaconDetails'));
const EditBeacon = React.lazy(() => import('./screens/Beacons/EditBeacon'));
const CompanyOverview = React.lazy(() => import('./screens/EditCompany/CompanyOverview'));
const EditCompany = React.lazy(() => import('./screens/EditCompany/CompanyEdit'));
const MissedPaymentsOverview = React.lazy(() => import('./screens/MissedPaymentsOverview'));
const CinemaOperatingCompaniesOverview = React.lazy(
	() => import('./screens/CinemaOperatingCompany/overview')
);
const CinemaOperatingCompanyEdit = React.lazy(
	() => import('./screens/CinemaOperatingCompany/edit')
);

const CinemaClustersOverview = React.lazy(() => import('./screens/CinemaCluster/overview'));
const CinemaClustersEdit = React.lazy(() => import('./screens/CinemaCluster/edit'));
const MovieAssignment = React.lazy(() => import('./screens/MovieAssignment'));

const checkPrivileges = (userPrivileges?: Privileges | null) => (
	requiredPrivileges?: Privilege[]
) => {
	if (!userPrivileges) return false;
	if (!requiredPrivileges || requiredPrivileges.length === 0) return true;
	if (userPrivileges.rootRole) return true;
	return requiredPrivileges.some((priv) => userPrivileges?.[priv]);
};

const Card = styled(Box)`
	background-color: ${(p) => p.theme.palette.grey[100]};
	padding: 3rem 4rem;
	border-radius: 1rem;
`;

const RouteNotAllowed = () => {
	return (
		<Card>
			<Typography variant="h4" fontWeight={900}>
				{'Sie haben keine Berechtigung'} <br /> {'diese Seite zu öffnen'}
			</Typography>
		</Card>
	);
};

const RouteName: React.FC<{ name: string; children: React.ReactNode }> = ({ name, children }) => {
	useOnRouteEnter(name);

	return children;
};

const cinfinityBrands: [Brand, Brand] = ['CINFINITY', 'CINFINITY-WEB'];

const Restrict: React.FC<{
	children: React.ReactNode;
	hasBrand?: Brand | [Brand, ...Brand[]];
	hasPrivileges?: Privilege | Privilege[];
	hasFeature?: Feature | [Feature, ...Feature[]];
}> = (props) => {
	const { hasFeature, hasBrand, hasPrivileges, children } = props;

	const userPrivileges = useUserPrivileges();

	const isAllowed = React.useMemo(() => {
		if (hasFeature) {
			const featuresAllowed = Array.isArray(hasFeature) ? hasFeature : [hasFeature];

			if (featuresAllowed.some((feature) => FEATURES.includes(feature))) {
				return true;
			}
		} else if (hasBrand) {
			const brandsAllowed = Array.isArray(hasBrand) ? hasBrand : [hasBrand];

			if (brandsAllowed.includes(brand)) {
				return true;
			}
		} else if (hasPrivileges) {
			const privilegesAllowed = Array.isArray(hasPrivileges) ? hasPrivileges : [hasPrivileges];

			if (checkPrivileges(userPrivileges)(privilegesAllowed)) {
				return true;
			}
		}

		return true;
	}, [hasFeature, hasBrand, hasPrivileges, userPrivileges]);

	return isAllowed ? children : <RouteNotAllowed />;
};

/**
 * now routes and nav menu items are decoupled, and must be added separately to appRouter and NavMenuEntries.
 * if you want to add an alias to a route for analytics use <RouteName />
 * if you want to restrict access use <Restrict />, or add a ternary case, whichever suits best
 * */

const createAppRouter = () =>
	createBrowserRouter(
		createRoutesFromElements(
			<Route element={<App />}>
				{/* Campaigning */}
				<Route
					path="/marketing/*"
					element={
						<Restrict hasPrivileges={'accessRightCampaigns'}>
							{FEATURES.includes('NEW_CAMPAIGNING') ? (
								<Routes>
									<Route
										path="campaigns"
										element={
											<RouteName name={'campaign-list'}>
												<CampaignsNew />
											</RouteName>
										}
									/>
									<Route
										path="campaigns/:campaignId/edit"
										element={
											<RouteName name={'campaign-detail'}>
												<EditCampaignNew />
											</RouteName>
										}
									/>
									<Route
										path="campaigns/:campaignId/edit-email"
										element={
											<RouteName name={'emaileditor'}>
												<EmailEditor />
											</RouteName>
										}
									/>
								</Routes>
							) : (
								<Routes>
									<Route
										path="campaigns"
										element={
											<RouteName name={'campaign-list'}>
												<Campaigns />
											</RouteName>
										}
									/>
									<Route
										path="campaigns/new"
										element={
											<RouteName name={'campaign-new'}>
												<CreateCampaign />
											</RouteName>
										}
									/>
									<Route
										path="campaigns/:id/edit"
										element={
											<RouteName name={'campaign-detail'}>
												<EditCampaignOld />
											</RouteName>
										}
									/>
								</Routes>
							)}
						</Restrict>
					}
				/>

				{/* Target Groups */}
				<Route
					path="/marketing/targetgroups/*"
					element={
						<Restrict hasFeature={'TARGETGROUP_EDIT'} hasPrivileges={'accessRightCampaigns'}>
							<Routes>
								<Route
									path="/"
									element={
										<RouteName name={'target-group-overview'}>
											<TargetGroups />
										</RouteName>
									}
								/>
								<Route
									path=":targetGroupId/edit"
									element={
										<RouteName name={'target-group-detail'}>
											<EditTargetGroup />
										</RouteName>
									}
								/>
							</Routes>
						</Restrict>
					}
				/>

				{/* Analysis */}
				<Route
					path="/analytics/*"
					element={
						<Restrict hasPrivileges={'accessRightDashboard'}>
							<Routes>
								<Route
									path="cinfinity"
									element={
										<Restrict hasFeature={'CINFINITY_STATS'}>
											<RouteName name={'cinfinity-stats'}>
												<CinfinityStats />
											</RouteName>
										</Restrict>
									}
								/>
								<Route
									path="payouts"
									element={
										<Restrict hasFeature={'PAYOUT_OVERVIEW'}>
											<RouteName name={'payout-overview'}>
												<PayoutOverview />
											</RouteName>
										</Restrict>
									}
								/>

								{FEATURES.includes('NEW_STATISTICS') ? (
									<>
										<Route
											path="dashboard"
											element={
												<RouteName name={'dashboard'}>
													<Dashboard />
												</RouteName>
											}
										/>
										<Route
											path="cinemacomparison"
											element={
												<RouteName name={'cinemacomparison'}>
													<CinemaComparison />
												</RouteName>
											}
										/>
										<Route
											path="moviestats"
											element={
												<RouteName name={'moviestats'}>
													<MovieStats />
												</RouteName>
											}
										/>
										<Route index element={<Navigate to="dashboard" replace />} />
									</>
								) : (
									<>
										<Route
											path="bonusprogram"
											element={
												<Restrict hasFeature={'BONUSPROGRAM'}>
													<RouteName name={'bonusprogram-stats'}>
														<StatisticsBonusProgram />
													</RouteName>
												</Restrict>
											}
										/>
										<Route
											path="app"
											element={
												<RouteName name={'app-stats'}>
													<StatisticsUsage />
												</RouteName>
											}
										/>
										<Route
											path="movies"
											element={
												<RouteName name={'movie-stats'}>
													<StatisticsMovies />
												</RouteName>
											}
										/>
										<Route index element={<Navigate to="app" replace />} />
									</>
								)}
							</Routes>
						</Restrict>
					}
				/>

				{/* Editorial */}
				<Route
					path="/content/*"
					element={
						<Restrict hasPrivileges={'accessRightDashboard'}>
							<Routes>
								{FEATURES.includes('NEW_EDIT_CINEMA') && (
									<Route
										path="cinema-new"
										element={
											<RouteName name={'cinema-editor-new'}>
												<EditCinemaNew />
											</RouteName>
										}
									/>
								)}
								<Route
									path="soon"
									element={
										<RouteName name={'starting-soon-editor'}>
											<EditSoonInCinema />
										</RouteName>
									}
								/>
								<Route
									path="lastChances"
									element={
										<RouteName name={'last-chance-editor'}>
											<EditLastChances />
										</RouteName>
									}
								/>
								{FEATURES.includes('BONUSPROGRAM') && (
									<Route
										path="bonusprogram"
										element={
											<RouteName name={'bonusprogram-editor'}>
												<EditBonusprogram />
											</RouteName>
										}
									/>
								)}
								<Route
									path="movie-assignment"
									element={
										<RouteName name={'movie-assignment'}>
											<MovieAssignment />
										</RouteName>
									}
								/>
							</Routes>
						</Restrict>
					}
				/>

				{/* Users */}
				<Route
					path="/users/*"
					element={
						<Restrict hasFeature={'USER_EDIT'}>
							<Routes>
								<Route
									path="overview"
									element={
										<RouteName name={'users overview'}>
											<EditUsers />
										</RouteName>
									}
								/>
								<Route
									path="details/:id"
									element={
										<RouteName name={'user-edit'}>
											<EditUser />
										</RouteName>
									}
								/>
								<Route
									path="missed-payments"
									element={
										<Restrict hasFeature={'PAYOUT_OVERVIEW'}>
											<RouteName name={'MissedPayments'}>
												<MissedPaymentsOverview />
											</RouteName>
										</Restrict>
									}
								/>
							</Routes>
						</Restrict>
					}
				/>

				{/* Vouchers */}
				<Route
					path="/gutscheine/*"
					element={
						<Restrict hasBrand={cinfinityBrands}>
							<Routes>
								<Route
									path="erstellen"
									element={
										<RouteName name={'create voucher'}>
											<CreateVoucher />
										</RouteName>
									}
								/>
								<Route
									path="details"
									element={
										<RouteName name={'voucher details'}>
											<VoucherDetails />
										</RouteName>
									}
								/>
								<Route
									path="shopify"
									element={
										<RouteName name={'shopify orders'}>
											<ShopifyOrders />
										</RouteName>
									}
								/>
							</Routes>
						</Restrict>
					}
				/>

				{/* Admin */}
				<Route
					path="/admin/*"
					element={
						<>
							<Restrict hasPrivileges={'adminRole'}>
								<Routes>
									<Route
										path={'cinema-cluster/create'}
										element={
											<RouteName name={'CinemaClusterCreate'}>
												<CinemaClustersEdit />
											</RouteName>
										}
									/>
									<Route
										path={'cinema-cluster/details/:id'}
										element={
											<RouteName name={'CinemaClusterEdit'}>
												<CinemaClustersEdit />
											</RouteName>
										}
									/>
									<Route
										path={'cinema-clusters'}
										element={
											<RouteName name={'CinemaClustersTable'}>
												<CinemaClustersOverview />
											</RouteName>
										}
									/>
									<Route
										path={'cinema-companies'}
										element={
											<RouteName name={'CinemaOperatingCompaniesTable'}>
												<CinemaOperatingCompaniesOverview />
											</RouteName>
										}
									/>
									<Route
										path={'cinema-company/create'}
										element={
											<RouteName name={'CinemaOperatingCompanyCreate'}>
												<CinemaOperatingCompanyEdit />
											</RouteName>
										}
									/>
									<Route
										path={'cinema-company/details/:id'}
										element={
											<RouteName name={'CinemaOperatingCompanyEdit'}>
												<CinemaOperatingCompanyEdit />
											</RouteName>
										}
									/>
								</Routes>
							</Restrict>

							<Restrict hasFeature={'BONUSPROGRAM'} hasPrivileges={'accessRightBonusProgram'}>
								<Routes>
									<Route
										path={'postokens'}
										element={
											<RouteName name={'postokens'}>
												<PosTokens />
											</RouteName>
										}
									/>
								</Routes>
							</Restrict>
							<Restrict hasBrand={cinfinityBrands}>
								<Routes>
									<Route
										path="company/create"
										element={
											<RouteName name={'company creation'}>
												<EditCompany />
											</RouteName>
										}
									/>
									<Route
										path="company/edit/:id"
										element={
											<RouteName name={'company edition'}>
												<EditCompany />
											</RouteName>
										}
									/>
									<Route
										path="companies"
										element={
											<RouteName name={'companies overview'}>
												<CompanyOverview />
											</RouteName>
										}
									/>
									<Route
										path="beacons"
										element={
											<RouteName name={'beacon-editor'}>
												<BeaconDetails />
											</RouteName>
										}
									/>
									<Route
										path="beacons/new"
										element={
											<RouteName name={'beacon-create'}>
												<EditBeacon />
											</RouteName>
										}
									/>
									<Route
										path="beacons/details/:id"
										element={
											<RouteName name={'beacon-edit'}>
												<EditBeacon />
											</RouteName>
										}
									/>
								</Routes>
							</Restrict>
						</>
					}
				/>

				<Route index element={<Navigate to={'/analytics'} replace />} />
			</Route>
		)
	);

export const appRouter = createAppRouter();

export type NavMenuEntryCore = Partial<{
	brand: Brand[];
	features: Feature[];
	requiredPrivileges: (string | Privilege)[];
}>;

export type NavMenuEntry = {
	iconName: IconName;
	label: string;
	subEntries?: Array<
		{
			path: string;
			label: string;
		} & NavMenuEntryCore
	>;
} & NavMenuEntryCore;

export const NavMenuEntries: NavMenuEntry[] = [
	{
		label: 'Marketing',
		iconName: 'marketing',
		requiredPrivileges: ['accessRightCampaigns'],
		subEntries: [
			...(FEATURES.includes('NEW_CAMPAIGNING')
				? [
						{
							path: '/marketing/campaigns',
							label: 'Kampagnen',
						},
				  ]
				: [
						{
							label: 'Kampagnen-Planer',
							path: '/marketing/campaigns',
						},
				  ]),
			{
				label: 'Zielgruppen',
				path: '/marketing/targetgroups',
			},
		],
	},
	{
		label: 'Analyse',
		iconName: 'graph',
		requiredPrivileges: ['accessRightDashboard'],
		subEntries: [
			{
				label: 'Abo Statistiken',
				path: '/analytics/cinfinity',
				requiredPrivileges: ['accessRightFilmStatistics'],
				features: ['CINFINITY_STATS'],
			},
			{
				label: 'Auszahlungen',
				path: '/analytics/payouts',
				requiredPrivileges: ['accessRightDashboard'],
				features: ['PAYOUT_OVERVIEW'],
			},
			{
				label: 'Filme zuordnen',
				path: '/content/movie-assignment',
				requiredPrivileges: ['accessRightDashboard'],
			},
			...(FEATURES.includes('NEW_STATISTICS')
				? [
						{
							label: 'Dashboard',
							path: '/analytics/dashboard',
							requiredPrivileges: ['accessRightFilmStatistics'],
						},
						{
							label: 'Kino-Vergleich',
							path: '/analytics/cinemacomparison',
							requiredPrivileges: ['accessRightFilmStatistics'],
						},

						{
							label: 'Filme',
							path: '/analytics/moviestats',
							requiredPrivileges: ['accessRightFilmStatistics'],
						},
				  ]
				: [
						...(FEATURES.includes('BONUSPROGRAM')
							? [
									{
										label: 'Bonusprogramm',
										path: '/analytics/bonusprogram',
										requiredPrivileges: ['accessRightBonusProgram'],
									},
							  ]
							: []),
						{
							label: 'App-Nutzung',
							path: '/analytics/app',
							requiredPrivileges: ['accessRightDashboard'],
						},
						{
							label: 'Filme',
							path: '/analytics/movies',
							requiredPrivileges: ['accessRightFilmStatistics'],
						},
				  ]),
		],
	},
	{
		label: 'Redaktion',
		iconName: 'pen',
		requiredPrivileges: ['accessRightDashboard'],
		subEntries: [
			{
				label: 'Kino',
				path: '/content/cinema-new',
				requiredPrivileges: ['accessRightDashboard'],
				features: ['NEW_EDIT_CINEMA'],
			},
			{
				label: 'Vorschau',
				path: '/content/soon',
				requiredPrivileges: ['accessRightDashboard'],
			},
			{
				label: 'Letzte Chance',
				path: '/content/lastChances',
				requiredPrivileges: ['accessRightDashboard'],
			},
			{
				label: 'Bonusprogramm',
				path: '/content/bonusprogram',
				requiredPrivileges: ['rootRole'],
				features: ['BONUSPROGRAM'],
			},
		],
	},
	{
		features: ['USER_EDIT'],
		label: 'Nutzer',
		iconName: 'profile',
		subEntries: [
			{
				label: 'Nutzer verwalten',
				path: '/users/overview',
				requiredPrivileges: ['supportRole'],
			},
			{
				path: '/users/missed-payments',
				label: 'Mahnwesen',
				requiredPrivileges: ['adminRole'],
			},
		],
	},
	{
		brand: ['CINFINITY', 'CINFINITY-WEB'],
		label: 'Gutscheine',
		iconName: 'pen',
		requiredPrivileges: ['adminRole'],
		subEntries: [
			{
				label: 'Gutscheine erstellen',
				path: '/gutscheine/erstellen',
			},
			{
				label: 'Gutscheindetails',
				path: '/gutscheine/details',
			},
			{
				label: 'Shopify bestellungen',
				path: '/gutscheine/shopify',
			},
		],
	},
	{
		label: 'Admin',
		iconName: 'gear',
		requiredPrivileges: ['accessRightBonusProgram'],
		subEntries: [
			{
				label: 'Kinocluster',
				path: '/admin/cinema-clusters',
				requiredPrivileges: ['adminRole'],
			},
			{
				label: 'Kinobetreiber',
				path: '/admin/cinema-companies',
				requiredPrivileges: ['adminRole'],
			},
			{
				path: '/admin/companies',
				label: 'Unternehmen bearbeiten',
				requiredPrivileges: ['adminRole'],
				brand: ['CINFINITY', 'CINFINITY-WEB'],
			},
			{
				label: 'Cinuru-POS',
				path: '/admin/postokens',
				features: ['BONUSPROGRAM'],
			},
			{
				label: 'Beacons',
				path: '/admin/beacons',
				requiredPrivileges: ['rootRole', 'adminRole'],
				brand: ['CINFINITY', 'CINFINITY-WEB'],
			},
		],
	},
] as const;
