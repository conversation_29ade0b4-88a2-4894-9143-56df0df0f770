import fs from 'fs';
import path from 'path';
import HtmlPlugin from 'html-webpack-plugin';
import ReactRefreshPlugin from '@pmmmwh/react-refresh-webpack-plugin';
import CopyWebpackPlugin from 'copy-webpack-plugin';
import Dotenv from 'dotenv-webpack';
import WorkboxPlugin from 'workbox-webpack-plugin';
import { WebpackManifestPlugin } from 'webpack-manifest-plugin';
import * as webpack from 'webpack';
import 'webpack-dev-server';
import { capitalize } from 'lodash';

const isProd = process.env.NODE_ENV === 'production';

const config = (env): webpack.Configuration => {
	const brand = env.brand?.toLowerCase().startsWith('cinfinity') ? 'cinfinity' : 'cinuru';

	return {
		entry: {
			polyfill: './src/polyfill.js',
			index: './src/index.js',
		},
		optimization: {
			minimize: isProd,
			usedExports: true,
			runtimeChunk: 'single',
		},
		mode: isProd ? 'production' : 'development',
		target: 'web',
		devtool: !isProd ? 'inline-source-map' : 'source-map',
		resolve: {
			alias: { 'react-native': 'react-native-web', 'react-native$': 'react-native-web' },
			extensions: ['.web.js', '.web.ts', '.web.tsx', '.js', '.ts', '.tsx', '.json', '.css', '.svg'],
			modules: [path.resolve('./node_modules')],
		},
		resolveLoader: {
			modules: [path.resolve('./node_modules')],
		},
		devServer: {
			hot: true,
			port: 5002,
			compress: true,
			static: path.join(__dirname, 'public'),
			historyApiFallback: true,
			client: {
				overlay: false,
			},
		},
		module: {
			rules: [
				{
					test: /\.(js|ts|tsx)$/,
					include: [
						path.resolve(__dirname, 'src'),
						...[
							'@cinuru/utils',
							'@cinuru/components',
							'react-native-pose',
							'react-pose-core',
							'animated-pose',
							'rn-markdown-parser',
							'react-native-gesture-handler',
						].map((p) => fs.realpathSync('node_modules/' + p)),
					],
					use: [
						{
							loader: require.resolve('babel-loader'),
							options: {
								cacheDirectory: true,
								sourceMaps: true,
								presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
								plugins: [
									['@babel/plugin-proposal-class-properties'],
									!isProd && [require.resolve('react-refresh/babel')],
									[
										'babel-plugin-import',
										{
											libraryName: '@cinuru/components',
											libraryDirectory: 'src',
											camel2DashComponentName: false,
										},
										'@cinuru/components',
									],
									[
										'babel-plugin-import',
										{ libraryName: 'lodash', libraryDirectory: '', camel2DashComponentName: false },
										'lodash',
									],
								].filter(Boolean),
							},
						},
					],
				},
				{ test: /\.css$/, use: ['style-loader', 'css-loader'] },
				{
					test: /\.(png|jp(e*)g|svg|gif)$/,
					use: [
						{
							loader: 'file-loader',
							options: {
								name: 'images/[name].[ext]',
							},
						},
					],
				},
			],
		},
		output: {
			publicPath: '/',
			chunkFilename: '[name].[contenthash].js',
			filename: '[name].[contenthash].js',
		},
		plugins: [
			new webpack.ProvidePlugin({
				React: 'react',
			}),
			new Dotenv({
				path: path.resolve(__dirname, isProd ? `.env.${brand}` : '.env'),
				safe: path.resolve(__dirname, '.env.example'),
				systemvars: true,
				defaults: false,
			}),
			new CopyWebpackPlugin({
				patterns: [
					{
						from: 'public',
						to: './',
						globOptions: {
							ignore: ['**/index.html'],
						},
					},
				],
			}),
			new WebpackManifestPlugin({
				fileName: './dist/manifest.json',
				writeToFileEmit: true,
				filter: () => false,
				seed: {
					name: `${capitalize(brand)} Dashboard`,
					short_name: `${capitalize(brand)} Dashboard`,
					start_url: `./`,
					lang: 'de-DE',
					display: 'standalone',
					screenshots: [
						{
							src: `icon-${brand}.png`,
							sizes: '200x200',
							form_factor: 'wide',
						},
						{
							src: `icon-${brand}.png`,
							sizes: '200x200',
						},
					],
					icons: [
						{
							src: `icon-${brand}.png`,
							type: 'image/png',
							sizes: '200x200',
						},
					],
					theme_color: '#4114C8',
					background_color: '#ffffff',
				},
			}),
			new HtmlPlugin({
				template: `./public/index.html`,
				favicon: `./public/favicon-${brand}.ico`,
				title: `${capitalize(brand)} Dashboard`,
				filename: './index.html',
			}),
			isProd &&
				new WorkboxPlugin.GenerateSW({
					clientsClaim: true,
					skipWaiting: true,
					maximumFileSizeToCacheInBytes: 40_000_000,
				}),
			new webpack.DefinePlugin({
				__DEV__: !isProd,
				//if production, brand comes from command line argument, otherwise from .env file
				...(isProd
					? { 'process.env.BRAND': brand === 'cinfinity' ? '"CINFINITY"' : '"CINURU"' }
					: {}),
			}),
			!isProd && new ReactRefreshPlugin(),
		].filter(Boolean),
	};
};

export default config;
