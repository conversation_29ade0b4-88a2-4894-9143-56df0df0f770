import type { CodegenConfig } from '@graphql-codegen/cli';
import dotenv from 'dotenv';

dotenv.config();

const config: CodegenConfig = {
	schema: '../api/graphql-api-aws/schema.gql',
	documents: ['src/**/!(*.d).(tsx|ts|jsx|js)'],
	generates: {
		'./src/gql/graphql.ts': {
			plugins: ['typescript', 'typescript-operations', 'typescript-react-apollo'],
			presetConfig: {
				fragmentMasking: false,
			},
			config: {
				withHooks: true,
				withHOC: true,
				maybeValue: 'T | undefined',
				dedupeOperationSuffix: true,
				flattenGeneratedTypes: true,
				dedupeFragments: true,
				enumsAsTypes: true,
			},
		},
	},
};
export default config;
