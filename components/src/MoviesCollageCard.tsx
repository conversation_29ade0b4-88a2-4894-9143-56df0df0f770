/** needs refactoring or may be eliminated by using simpler components in the ForYouView in the future, maintaining this complicated layout is costing more effort than it is worth, there are better ways to design a card that displays multiple images, this one mostly looks stupid anyways because the banner images are weird for special events */

import * as React from 'react';
import { Dimensions } from 'react-native';
import styled, { useTheme } from 'styled-native-components';

import Ripple from './Ripple';
import WrappedImage from './WrappedImage';
import Label from './Label';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const MIN_BANNER_WIDTH = 20;
const CARD_MARGIN = 1.5;
const CARD_PADDING = 0.5;
const POSTER_MARGIN = 0.5;
const SMALL_LAYOUT_FACTORS = {
	nPosterWidth: 10,
	nPosterHeight: 15,
	nContentHeight: 16,
	nBannerHeight: 11,
};
const LAYOUT_FACTORS = {
	nPosterWidth: 7,
	nPosterHeight: 10.5,
	nContentHeight: 23,
	nBannerHeight: 17.5,
};

const Card = styled(Ripple)`
	margin: 2rem ${CARD_MARGIN}rem;
	background-color: $background0;
	border-radius: ${(p) => p.theme.borderRadius[1]};
	padding: ${CARD_PADDING}rem;
	flex-direction: row;
	elevation: 2;
`;

const ElementWrapper = styled.View`
	margin: ${POSTER_MARGIN}rem;
`;

const BannerAndTitleSide = styled.View`
	flex: 1;
	flex-direction: column;
	height: ${(p) => p.nHeight}rem;
`;

const PostersSide = styled.View`
	width: ${
		(p) =>
			p.nRows * (p.nPosterWidth + 2 * POSTER_MARGIN) +
			0.1 /* adding this is necessary to prevent overflow for obscure reasons */
	}rem;
	flex-flow: row wrap;
	height: ${(p) => p.nHeight}rem;
`;

const Banner = styled(WrappedImage)`
	width: 100%;
	height: ${(p) => p.nHeight}rem;
	border-radius: ${(p) => p.theme.borderRadius[0]};
	background-color: $neutral3;
`;

const Poster = styled(WrappedImage)`
	width: ${(p) => p.nWidth}rem;
	height: ${(p) => p.nHeight || (p.nWidth / 2) * 3}rem;
	border-radius: ${(p) => p.theme.borderRadius[0]};
	background-color: $neutral3;
`;

const getNumberOfFittingRows = (theme, factors) => {
	const cardContentWidth = SCREEN_WIDTH - (CARD_MARGIN + 2 * CARD_PADDING) * theme.rem;
	const posterWidth = (factors.nPosterWidth + 2 * POSTER_MARGIN) * theme.rem;
	return Math.floor((cardContentWidth - MIN_BANNER_WIDTH * theme.rem) / posterWidth);
};

const MoviesCollageCard = ({
	banner,
	posters,
	title,
	onCardPress,
}: {
	banner: string,
	posters: string[],
	title: string,
	onCardPress: () => void,
}) => {
	const theme = useTheme();

	let factors, nRows, nPosters;
	if (posters.length < 4) {
		factors = SMALL_LAYOUT_FACTORS;
		nRows = Math.min(posters.length, getNumberOfFittingRows(theme, factors));
		nPosters = nRows;
	} else {
		factors = LAYOUT_FACTORS;
		nRows = Math.min(Math.floor(posters.length / 2), getNumberOfFittingRows(theme, factors));
		nPosters = nRows * 2;
	}
	return (
		<Card onPress={onCardPress}>
			<BannerAndTitleSide nHeight={factors.nContentHeight}>
				<ElementWrapper>
					<Label size="l" margin="0 0.5rem" numberOfLines={1}>
						{title}
					</Label>
				</ElementWrapper>
				<ElementWrapper>
					{banner ? <Banner nHeight={factors.nBannerHeight} src={banner} /> : null}
				</ElementWrapper>
			</BannerAndTitleSide>
			<PostersSide
				nRows={nRows}
				nPosterWidth={factors.nPosterWidth}
				nHeight={factors.nContentHeight}
			>
				{posters.slice(0, nPosters).map((poster, i) => {
					const showEllipsis = nPosters < posters.length && i === nPosters - 1;
					return (
						<ElementWrapper key={i}>
							<Poster
								nWidth={factors.nPosterWidth}
								src={poster}
								nHeight={showEllipsis ? factors.nPosterHeight - 2 : undefined}
							/>
							{showEllipsis && (
								<Label flex align="center">
									• • •
								</Label>
							)}
						</ElementWrapper>
					);
				})}
				<ElementWrapper />
			</PostersSide>
		</Card>
	);
};

export default React.memo(MoviesCollageCard);
