/** should be rewritten and inlines in CinemaDetailView */

import * as React from 'react';
import styled from 'styled-native-components';

import Paragraph from './Paragraph';

const Wrapper = styled.View`
	padding: 1rem 2rem;
`;

const OpeningItemWrapper = styled.View`
	justify-content: space-between;
	flex-direction: row;
`;

const OpeningHoursSection = ({
	openingHours,
}: {
	openingHours: { label?: string, hours?: string }[],
}) => (
	<Wrapper>
		{openingHours
			.filter(({ label, hours }) => label || hours)
			.map(({ label, hours }) => (
				<OpeningItemWrapper key={label}>
					<Paragraph>{label}: </Paragraph>
					<Paragraph>{hours ? hours : ''}</Paragraph>
				</OpeningItemWrapper>
			))}
	</Wrapper>
);

export default React.memo(OpeningHoursSection);
