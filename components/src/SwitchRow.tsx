import * as React from 'react';
import styled from 'styled-native-components';

import Switch from './Switch';
import Paragraph from './Paragraph';

import type { SwitchRef } from './Switch';

const RowWrapper = styled.View`
	width: 100%;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	padding: 1rem 2rem;
	border-bottom-width: 1px;
	border-color: $border0;
`;

export const Col = styled.View``;

const SwitchRow = React.forwardRef<
	SwitchRef,
	{
		label: string;
		subLabel?: string;
		defaultValue?: boolean;
		activeGradient?: string;
		onChange?: (val: boolean) => void;
		light?: boolean;
	}
>(({ defaultValue, onChange, activeGradient, label, subLabel, light }, ref) => (
	<RowWrapper>
		<Col>
			<Paragraph bold={!light}>{label}</Paragraph>
			{subLabel ? (
				<Paragraph size="s" color="$neutral2">
					{subLabel}
				</Paragraph>
			) : null}
		</Col>
		<Switch
			ref={ref}
			activeGradient={activeGradient}
			defaultValue={defaultValue}
			onChange={onChange}
		/>
	</RowWrapper>
));

export default React.memo(SwitchRow);
