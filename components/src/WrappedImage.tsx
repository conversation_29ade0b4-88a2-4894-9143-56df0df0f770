import * as React from 'react';

import Icon from './Icon';
import { useCinuruComponentsContext } from './ContextProvider';
import styled from 'styled-components';

const MissingWrapper = styled.div`
	justify-content: center;
	align-items: center;
	background-color: $border0;
	padding: 1rem;
`;

type ResizeMode = 'contain' | 'cover' | 'stretch' | 'center';

const Image = styled.img.withConfig({
	shouldForwardProp: (prop) => !prop.includes('resizeMode'),
})<{
	resizeMode: ResizeMode;
}>`
	width: 100%;
	height: 100%;
	object-fit: ${(p) => p.resizeMode};
`;

const MAX_RETRIES = 3;
const WrappedImage = ({
	src,
	style,
	resizeMode = 'cover',
	tiny,
	onLoad,
}: {
	src?: string;
	style?: React.CSSProperties;
	resizeMode?: ResizeMode;
	tiny?: boolean;
	onLoad?: () => void;
}) => {
	// eslint-disable-next-line no-console
	if (!src || src.substring(0, 4) !== 'http') console.log(`invalide src prop '${src}' for image`);
	const { log } = useCinuruComponentsContext();
	const reportError = React.useCallback(
		(error, context) => {
			log &&
				log({
					type: 'IMAGE_ERROR',
					value: { errorMessage: error.message, context },
				});
		},
		[log]
	);

	const [retries, setRetries] = React.useState(0);
	const handleError = React.useCallback(() => {
		reportError(new Error('failed to load image'), { uri: src });
		setRetries((r) => {
			if (r <= MAX_RETRIES) {
				// eslint-disable-next-line no-console
				console.warn('fetching image failed, retrying');
				return r + 1;
			} else {
				// eslint-disable-next-line no-console
				console.error('fetching image fatally failed');
				return r;
			}
		});
	}, [reportError, src]);

	const source = React.useMemo(() => {
		return !src || retries > MAX_RETRIES || src.substring(0, 4) !== 'http' ? undefined : src;
	}, [src, retries]);

	return source ? (
		<Image
			style={style}
			resizeMode={resizeMode}
			key={retries}
			onError={handleError}
			src={source}
			onLoad={onLoad}
		/>
	) : (
		<MissingWrapper style={style}>
			<Icon name="missingPicture" color="$neutral3" size={tiny ? '2rem' : '3rem'} />
		</MissingWrapper>
	);
};

export default React.memo(WrappedImage);
