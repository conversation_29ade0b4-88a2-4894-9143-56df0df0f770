import * as React from 'react';
import { Platform } from 'react-native';

import Slider from './Slider';

import type { SliderRef } from './Slider';

export type SwitchRef = { setValue: (val: boolean) => void };

const Switch = React.forwardRef<
	SwitchRef,
	{
		backgroundColor?: string;
		activeGradient?: string;
		knobColor?: string;
		defaultValue?: boolean;
		onChange?: (val: boolean) => unknown;
	}
>(
	(
		{
			backgroundColor = '$border0',
			activeGradient = 'accentGradient0',
			knobColor = '$overlayText',
			defaultValue = false,
			onChange,
		},
		ref
	) => {
		const sliderRef = React.useRef<SliderRef>(null);
		const [value, setValue] = React.useState(defaultValue ? 1 : 0);
		const handleChange = React.useCallback(
			(val) => {
				setValue(val);
				if (onChange) onChange(val === 1);
			},
			[onChange]
		);

		React.useImperativeHandle(
			ref,
			() => ({ setValue: (val) => sliderRef.current?.setValue(val ? 1 : 0) }),
			[]
		);

		const handleToggleValue = React.useCallback(() => {
			sliderRef.current?.setValue(value ? 0 : 1);
		}, [value]);

		return (
			<Slider
				step={1}
				ref={sliderRef}
				defaultValue={value}
				onChange={handleChange}
				onPress={handleToggleValue}
				backgroundColor={backgroundColor}
				activeGradient={activeGradient}
				knobColor={knobColor}
				{...{
					web: {
						width: '5.5rem',
						barHeight: '3.5rem',
						knobSize: '2rem',
						borderColor: '$border0',
					},
					android: {
						width: '4.5rem',
						margin: '0.5rem',
						barHeight: '1.5rem',
						knobSize: '2.5rem',
					},
					ios: {
						width: '5.5rem',
						barHeight: '3.5rem',
						knobSize: '2.75rem',
					},
				}[Platform.OS]}
			/>
		);
	}
);

export default React.memo(Switch);
