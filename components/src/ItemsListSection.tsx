/** should be eliminated/rewritten completely together with the CinemaDetailView */

import * as React from 'react';
import styled from 'styled-native-components';

import WrappedImage from './WrappedImage';
import Paragraph from './Paragraph';
import Article from './Article';

const Wrapper = styled.View`
	padding: 1rem 2rem;
`;

const ItemWrapper = styled.View`
	margin: 1rem 0;
	flex-direction: row;
`;

const ItemImage = styled(WrappedImage)`
	width: 8rem;
	height: 8rem;
	border-radius: ${(p) => p.theme.borderRadius[1]};
`;

const ItemInfoWrapper = styled.View`
	flex: 1;
	padding-left: 1rem;
`;

const HeaderWrapper = styled.View`
	justify-content: space-between;
	flex-direction: row;
`;

const ItemsListSection = ({
	items,
	onLinkPress,
}: {
	items: {
		title: string,
		image?: string,
		description?: string,
		price?: string,
	}[],
	onLinkPress?: (link: string) => any,
}) => {
	return (
		<Wrapper>
			{items.map(({ title, image, description, price }) => (
				<ItemWrapper key={title + (price ? price : '')}>
					{Boolean(image) && <ItemImage src={image} />}
					<ItemInfoWrapper>
						<HeaderWrapper>
							<Paragraph bold>{title}</Paragraph>
							<Paragraph bold>{price}</Paragraph>
						</HeaderWrapper>
						<Article size="s" color="$neutral2" onLinkPress={onLinkPress} markdown={true}>
							{description || ''}
						</Article>
					</ItemInfoWrapper>
				</ItemWrapper>
			))}
		</Wrapper>
	);
};

export default React.memo(ItemsListSection);
