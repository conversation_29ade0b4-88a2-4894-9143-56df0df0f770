import * as React from 'react';
import styled from 'styled-native-components';

import type { ID } from '@cinuru/utils/types';

import Icon from './Icon';
import Gradient from './Gradient';
import Ripple from './Ripple';
import VoucherCard from './VoucherCard';
import Badge from './Badge';
import Heading from './Heading';
import Paragraph from './Paragraph';

const CARD_HEIGHT = 11;
const CARD_INSET = 0.5;
const IMAGE_WIDTH = CARD_HEIGHT + 2 * CARD_INSET;
const CARD_OVERLAP = 2;

const Wrapper = styled.View`
	flex-direction: row;
	align-items: center;
	margin: 1.5rem;
`;

const Card = styled(Ripple)`
	flex: 1;
	height: ${CARD_HEIGHT}rem;
	flex-direction: row;
	justify-content: space-between;
	background-color: $background0;
	border-radius: ${(p) => p.theme.borderRadius[2]};
	margin-left: ${IMAGE_WIDTH - CARD_OVERLAP}rem;
	padding-left: ${CARD_OVERLAP}rem;
	elevation: 2;
`;

const VoucherCardWrapper = styled.View`
	position: absolute;
	elevation: 2;
`;

const ContentWrapper = styled.View<{ purchasable?: boolean }>`
	flex: 1;
	padding: 0 1.5rem;
	opacity: ${(p) => (p.purchasable ? 1 : 0.5)};
	justify-content: center;
`;

const PriceWrapper = styled(Gradient)<{ purchasable?: boolean }>`
	border-radius: 0 1rem 1rem 0;
	width: 7rem;
	height: 100%;
	opacity: ${(p) => (p.purchasable ? 1 : 0.75)};
	flex-direction: column;
	justify-content: space-around;
	align-items: center;
`;

const PositionedBadge = styled(Badge)`
	position: absolute;
	top: -0.5rem;
	right: -0.5rem;
`;

const getMiddleColorFromGradient = (gradient: string) => {
	const colors = gradient.split(',');
	return colors[Math.floor(colors.length / 2)];
};

const VoucherListCard = ({
	id,
	title,
	description,
	iconName,
	backgroundColor,
	accentColor = '$background0',
	price,
	remainingCapacity,
	purchasable,
	onPress,
}: {
	id: ID;
	title: string;
	description?: string;
	iconName?: string;
	backgroundColor?: string;
	accentColor?: string;
	price: number;
	remainingCapacity?: number;
	purchasable: boolean;
	onPress?: (id: ID) => unknown;
}) => {
	const handlePress = React.useMemo(() => onPress && (() => onPress(id)), [id, onPress]);
	return (
		<Wrapper>
			<Card onPress={handlePress}>
				<ContentWrapper purchasable={purchasable}>
					<Heading size="s" condensed numberOfLines={2} margin="0 0 0.5rem">
						{title}
					</Heading>
					{description ? (
						<Paragraph size="s" numberOfLines={2}>
							{description}
						</Paragraph>
					) : null}
				</ContentWrapper>
				<PriceWrapper purchasable={purchasable} color={backgroundColor}>
					<Icon color={accentColor} name="coin" size="4rem" margin="0.5rem 0 -2.5rem 0" />
					<Heading size="s" condensed color={accentColor} margin="1rem 0 0">
						{price}
					</Heading>
				</PriceWrapper>
			</Card>
			<VoucherCardWrapper>
				<VoucherCard
					id={id}
					onPress={onPress}
					width={IMAGE_WIDTH + 'rem'}
					margin="0"
					iconName={iconName}
					accentColor={accentColor}
					backgroundColor={backgroundColor}
				/>
			</VoucherCardWrapper>
			{remainingCapacity === 0 || remainingCapacity ? (
				<PositionedBadge
					text={String(remainingCapacity)}
					textColor={backgroundColor && getMiddleColorFromGradient(backgroundColor)}
					backgroundColor={accentColor}
				/>
			) : null}
		</Wrapper>
	);
};

export default React.memo(VoucherListCard);
