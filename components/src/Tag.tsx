import * as React from 'react';
import styled from 'styled-native-components';

import Icon from './Icon';
import Label from './Label';
import Ripple from './Ripple';

import type { IconName } from '@cinuru/utils/theme';

const Wrapper = styled(Ripple)<{ color: string; padding: string; margin: string }>`
	background-color: ${(p) => p.color};
	padding: ${(p) => p.padding};
	border-radius: ${(p) => p.theme.borderRadius[1]};
	margin: ${(p) => p.margin};
	flex-direction: row;
	align-items: center;
`;

const Tag = ({
	label,
	iconName,
	margin = '0rem 1rem 0rem 0rem',
	padding = '0.25rem 1rem',
	color = '$neutral3',
	textColor = '$background0',
	onPress,
	onRemove,
}: {
	label: string;
	margin?: string;
	padding?: string;
	iconName?: IconName;
	color?: string;
	textColor?: string;
	onPress?: () => void;
	onRemove?: () => void;
}) => (
	<Wrapper margin={margin} padding={padding} color={color} onPress={onPress}>
		{iconName ? (
			<Icon name={iconName} size="1.5rem" color="$background0" margin="0rem 0.5rem 0rem 0rem" />
		) : null}
		<Label size="xs" color={textColor}>
			{label.toUpperCase()}
		</Label>
		{onRemove && (
			<Icon
				name="close"
				color="$background0"
				size="1.5rem"
				margin="0.25rem 0rem 0.25rem 0.75rem"
				accessible={false}
				onPress={onRemove}
			/>
		)}
	</Wrapper>
);

export default React.memo(Tag);
