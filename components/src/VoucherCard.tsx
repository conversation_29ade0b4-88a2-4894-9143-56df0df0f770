import * as React from 'react';
import styled, { useColorAttribute } from 'styled-native-components';
import Svg, { Path } from 'svgs';
import AsyncStorage from '@react-native-community/async-storage';

import type { ViewStyle } from 'react-native';
import type { ID } from '@cinuru/utils/types';
import type { GradientName } from '@cinuru/utils/theme';

import Badge from './Badge';
import Ripple from './Ripple';
import Gradient from './Gradient';
import Label from './Label';
import LoadingIndicator from './LoadingIndicator';

const Wrapper = styled.View<{ width: string; margin: string }>`
	max-width: ${(p) => p.width};
	margin: ${(p) => p.margin};
`;

const Card = styled(Ripple)<{ width: string }>`
	width: ${(p) => p.width};
	height: ${(p) => p.width};
	border-radius: ${(p) => p.theme.borderRadius[1]};
	background-color: $background0;
	elevation: 2;
`;

const Background = styled(Gradient).attrs<
	{ backgroundGradient?: GradientName },
	{ colors?: string[] }
>((p) => ({
	colors: p.backgroundGradient ? p.theme.colors[p.backgroundGradient] : undefined,
}))`
	border-radius: ${(p) => p.theme.borderRadius[1]};
	flex: 1;
	overflow: hidden;
`;

const TitleWrapper = styled.View`
	margin: 1rem 0;
	height: 3.5rem;
	margin-top: 1rem;
	background-color: transparent;
`;

const PositionedBadge = styled(Badge)`
	position: absolute;
	top: -1rem;
	right: -0.5rem;
`;

const getMiddleColorFromGradient = (gradient: string) => {
	const colors = gradient.split(',');
	return colors[Math.floor(colors.length / 2)];
};
type Paths = { [name: string]: string };

const fetchPaths = async (): Promise<Paths | null> => {
	try {
		const res = await fetch('https://static.cinuru.com/public/voucher-icon-paths.json');
		return await res.json();
	} catch (e) {
		if (!e.message.includes('Network request failed')) throw e;
		return null;
	}
};
const STORAGE_KEY = 'VOUCHER_ICON_PATHS';
let pathsCache: Paths;
let pathsPromise: Promise<boolean>;
const getPaths = async () => {
	// if paths are cached, return them
	if (pathsCache) return pathsCache;
	// we want to only fetch paths once, so we scope the promise to the module
	if (!pathsPromise) {
		pathsPromise = (async () => {
			const paths = await fetchPaths();
			if (paths) {
				pathsCache = paths;
				AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(pathsCache));
				return true;
			}
			return false;
		})();
	}
	await pathsPromise;
	return pathsCache;
};

const usePaths = (): Paths | undefined => {
	const [paths, setPaths] = React.useState<Paths>(pathsCache);
	React.useEffect(() => {
		let aborted = false;
		(async () => {
			if (!aborted) {
				const storedVal = await AsyncStorage.getItem(STORAGE_KEY);
				if (storedVal && !aborted) setPaths(JSON.parse(storedVal) as Paths);
				const fetchedVal = await getPaths();
				if (fetchedVal && !aborted) setPaths(fetchedVal);
			}
		})();
		return () => {
			aborted = true;
		};
	}, []);
	return paths;
};

const VoucherCard = ({
	id,
	title,
	iconName = 'unknown',
	backgroundColor,
	backgroundGradient,
	accentColor,
	badge,
	width = '10rem',
	margin = '1rem',
	onPress,
	style,
}: {
	id: ID;
	title?: string;
	iconName?: string;
	backgroundColor?: string;
	backgroundGradient?: GradientName;
	accentColor?: string;
	badge?: string;
	width?: string;
	margin?: string;
	onPress?: (id: ID) => unknown;
	style?: ViewStyle;
}) => {
	const paths = usePaths();
	const hanldePress = React.useMemo(() => onPress && (() => onPress(id)), [id, onPress]);
	const color = useColorAttribute(accentColor || '$neutral0');
	return (
		<Wrapper width={width} margin={margin} style={style}>
			<Card width={width} onPress={hanldePress} rippleColor={accentColor}>
				<Background color={backgroundColor} backgroundGradient={backgroundGradient}>
					{paths ? (
						<Svg width="100%" height="100%" viewBox="0 0 40 40">
							<Path fillRule="nonzero" fill={color} d={paths[iconName]} />
						</Svg>
					) : (
						<LoadingIndicator color={accentColor} height={width} />
					)}
				</Background>
			</Card>
			{Boolean(badge) && badge && (
				<PositionedBadge
					text={badge}
					textColor={backgroundColor && getMiddleColorFromGradient(backgroundColor)}
					backgroundColor={accentColor}
				/>
			)}
			{title ? (
				<TitleWrapper>
					<Label size="xs" light numberOfLines={2} align="center">
						{title}
					</Label>
				</TitleWrapper>
			) : null}
		</Wrapper>
	);
};
export default React.memo(VoucherCard);
