import * as React from 'react';
import styled from 'styled-native-components';

import Paragraph from './Paragraph';
import Icon from './Icon';
import Ripple from './Ripple';

const Wrapper = styled.View.attrs<
	{ margin: string; altBackground?: boolean },
	{ activeOpacity: number }
>({
	activeOpacity: 0.8,
})`
	margin: ${(p) => p.margin};
	flex-direction: row;
	border: 1px solid $border0;
	align-items: center;
	justify-content: center;
	height: 4rem;
	width: 12rem;
	background-color: ${(p) => (p.altBackground ? '$background0' : '$background1')};
`;

const StepperButton = styled(Ripple).attrs({
	rippleColor: '$neutral0',
})`
	height: 100%;
	width: 4rem;
	align-items: center;
	justify-content: center;
	background: $border0;
`;

export type StepperFieldRef = { setValue: (val: number) => void };

const CodeField = React.forwardRef<
	StepperFieldRef,
	{
		margin?: string;
		altBackground?: boolean;
		defaultValue?: number;
		value?: number;
		onChange?: (value: number) => void;
		minValue?: number;
		maxValue?: number;
	}
>(
	(
		{
			margin = '0',
			altBackground,
			defaultValue = 0,
			value: passedValue,
			onChange,
			minValue,
			maxValue,
		},
		ref
	) => {
		// this can be a controlled and an uncontrolled component depending on wether a value is passed
		const [internalValue, setInternalValue] = React.useState(defaultValue);
		const handleSetValue = React.useCallback(
			(val: number) => {
				if (val < minValue! || val > maxValue!) {
					throw new Error(`value ${val} out of rage [${minValue},${maxValue}]`);
				} else {
					if (onChange) onChange(val);
					if (passedValue === undefined) setInternalValue(val);
				}
			},
			[maxValue, minValue, onChange, passedValue]
		);

		const value = passedValue !== undefined ? passedValue : internalValue;
		const handleMinus = React.useCallback(() => handleSetValue(value - 1), [handleSetValue, value]);
		const handlePlus = React.useCallback(() => handleSetValue(value + 1), [handleSetValue, value]);

		React.useImperativeHandle(ref, () => ({ setValue: (v) => handleSetValue(v) }), [
			handleSetValue,
		]);

		return (
			<Wrapper margin={margin} altBackground={altBackground}>
				<StepperButton onPress={value <= minValue! ? undefined : handleMinus}>
					<Icon name="minus" size="2rem" color={value <= minValue! ? '$neutral3' : '$neutral0'} />
				</StepperButton>
				<Paragraph width="4rem" align="center" bold>
					{value}
				</Paragraph>
				<StepperButton onPress={value >= maxValue! ? undefined : handlePlus}>
					<Icon name="plus" size="2rem" color={value >= maxValue! ? '$neutral3' : '$neutral0'} />
				</StepperButton>
			</Wrapper>
		);
	}
);

export default React.memo(CodeField);
