import * as React from 'react';
import styled from 'styled-native-components';

import type { ID } from '@cinuru/utils/types';
import type { GradientName } from '@cinuru/utils/theme';

import VoucherCard from './VoucherCard';
import Heading from './Heading';

const ScrollWrapper = styled.FlatList.attrs({
	keyExtractor: ({ id }) => id,
})`
	width: 100%;
	contentContainer {
		padding: 0.5rem 1rem;
	}
`;

const VoucherCardsSection = ({
	vouchers,
	title,
	backgroundColor,
	backgroundGradient,
	accentColor,
	onVoucherPress,
}: {
	vouchers: {
		id: ID;
		title?: string;
		iconName?: string;
		badge?: string;
	}[];
	title?: string;
	backgroundColor?: string;
	backgroundGradient?: GradientName;
	accentColor?: string;
	onVoucherPress: (id: ID) => void;
}) => {
	const renderVoucher = React.useCallback(
		({ item }) => (
			<VoucherCard
				key={item.id}
				{...item}
				badge={item.badge}
				width="10rem"
				onPress={onVoucherPress}
				backgroundColor={backgroundColor}
				backgroundGradient={backgroundGradient}
				accentColor={accentColor}
			/>
		),
		[onVoucherPress, backgroundColor, backgroundGradient, accentColor]
	);
	return (
		<>
			{title ? (
				<Heading size="s" margin="2rem 0rem 0rem 2.5rem">
					{title}
				</Heading>
			) : null}
			<ScrollWrapper
				horizontal
				showsHorizontalScrollIndicator={false}
				data={vouchers}
				renderItem={renderVoucher}
				initialNumToRender={5}
			/>
		</>
	);
};

VoucherCardsSection.defaultProps = {
	nMargins: [1.5, 0],
};

export default React.memo(VoucherCardsSection);
